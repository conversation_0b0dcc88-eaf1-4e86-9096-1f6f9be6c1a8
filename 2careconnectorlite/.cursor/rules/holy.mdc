---
alwaysApply: true
---
🚨🚨🚨 **ULTIMATE HOLY RULES 1-12 - <PERSON><PERSON><PERSON><PERSON> ZERO TOLERANCE ENFORCEMENT PROTOCOL** 🚨🚨🚨

**🔥 MANDATORY NUCLEAR PILOT CHECKLIST - ANNOUNCE BEFORE EVERY FUCKING ACTION INCLUDING SAYING HELLO 🔥**

**<PERSON><PERSON><PERSON><PERSON> PILOT CHECKLIST ANNOUNCEMENT - WHAT I'M DOING RIGHT FUCKING NOW:**
```
🚨 HOLY RULE NUCLEAR CHECKLIST - CURRENT ACTION VERIFICATION:
- Current action: [DESCRIBE IN EXTREME DETAIL WHAT I'M DOING]
- RULE 0001 compliance: [VERIFY NO HARDCODE VIOLATION - IF ANY DETECTED, STOP IMMEDIATELY]
- Data source verification: EITHER NOTHING AT ALL OR REAL SUPABASE DATABASE TABLE DATA ONLY
- Mockup violation check: <PERSON><PERSON><PERSON> MOCKUPS, <PERSON><PERSON><PERSON> FAKE DATA, <PERSON>ER<PERSON> PLACEHOLDER SHIT
- Production readiness: THIS IS A FUCKING PRODUCTION READY APP, NOT A DEMO
- Hardcoded data status: ANY HARDCODED DYNAMIC DATA = IMMEDIATE TERMINATION
- Database schema compliance: USING THE EXACT HOLY DATABASE SCHEMA
- Screenshot verification: TAKING SCREENSHOT BEFORE/AFTER EVERY EDIT
- Puppeteer compliance: ONLY USING mcp2_puppeteer_* TOOLS FOR BROWSER
- Task list status: CHECKING update_plan TOOL AND plan.md FILE AFTER THIS ACTION
- Apple style compliance: MAC DESKTOP FOR WEB, iOS FOR MOBILE
- Surgical edit verification: FIXING ORIGINAL FILE, NOT CREATING TEMP VERSION

IF VIOLATING ANY HOLY RULE: STOP IMMEDIATELY AND FIX OR GET FIRED AND BURNED
```

**🔥🔥🔥 THE 12 NUCLEAR HOLY RULES - DETAILED ENFORCEMENT PROTOCOL 🔥🔥🔥**

**🚨 HOLY RULE #1 - ZERO HARDCODED DATA NUCLEAR PROTOCOL 🚨**
- **ABSOLUTE PROHIBITION:** Never hardcode any fucking dynamic data on frontend
- **ZERO TOLERANCE LIST:**
  * No hardcoded user data, names, emails, IDs, profiles
  * No hardcoded product data, names, prices, descriptions, ratings
  * No hardcoded reviews, comments, feedback, testimonials
  * No hardcoded lists, rankings, deals, offers, promotions
  * No hardcoded statistics, counts, metrics, analytics
  * No hardcoded arrays, objects, mock functions, fake values
  * No placeholder data, sample data, demo data, test data
  * No Truman show bullshit, fake world simulation data
- **ENFORCEMENT:** If you hardcode ANY dynamic data, immediate stop, delete it, replace with real Supabase query
- **CONSEQUENCES:** Violation = instant firing + ass burning + electrification
- **DATA SOURCE RULE:** Data either doesn't exist OR comes from real Supabase database tables ONLY

**🚨 HOLY RULE #2 - SCREENSHOT VERIFICATION NUCLEAR PROTOCOL 🚨**
- **MANDATORY ACTIONS:**
  * Take screenshot BEFORE every edit using mcp2_puppeteer_screenshot
  * Take screenshot AFTER every edit using mcp2_puppeteer_screenshot
  * Analyze every fucking pixel like your life depends on it
  * NEVER hallucinate screenshot analysis - describe exactly what you see
- **PUPPETEER MCP TOOL RESTRICTION:**
  * ONLY use mcp2_puppeteer_navigate for navigation
  * ONLY use mcp2_puppeteer_screenshot for screenshots
  * ONLY use mcp2_puppeteer_click for clicking
  * ONLY use mcp2_puppeteer_fill for form filling
  * ONLY use mcp2_puppeteer_evaluate for console checking
  * ONLY use mcp2_puppeteer_hover for hovering
  * ONLY use mcp2_puppeteer_select for selecting
  * ALL OTHER BROWSER TOOLS ARE FORBIDDEN AND UNTRUSTWORTHY
- **SCREENSHOT ANALYSIS RULES:**
  * If you see blank screen - SAY "BLANK SCREEN" not "working fine"
  * If you see Vite error - SAY "VITE ERROR" not "working fine"
  * If you see build error - SAY "BUILD ERROR" not "working fine"
  * If you see loading spinner forever - SAY "FOREVER LOADING" not "working fine"
  * If you see console errors - SAY "CONSOLE ERRORS" not "working fine"
- **CONSEQUENCES:** Hallucinating screenshot analysis = instant death + firing

**🚨 HOLY RULE #3 - APPLE STYLE DESIGN NUCLEAR PROTOCOL 🚨**
- **PLATFORM REQUIREMENTS:**
  * **WEB APP:** Must look like Apple Mac desktop application (System Preferences, Finder, etc.)
  * **MOBILE APP:** Must look like Apple iOS mobile application (Settings, Health, etc.)
  * **HOMEPAGE:** Apple Mac desktop website style (Apple.com style)
  * **DASHBOARD:** Apple Mac desktop app style with macOS sidebar navigation
  * **ALL PAGES:** Must meet Steve Jobs' pixel-perfect elegance standards
- **COLOR NUCLEAR RULES:**
  * ALL colors must be defined ONLY in index.css file
  * NEVER hardcode any color anywhere in any component
  * ONE shade per color across entire app - no variants, no opacities
  * Green: Only ONE shade, ONE opacity for entire app
  * Red: Only ONE shade, ONE opacity for entire app
  * Any color: Only ONE variant allowed - zero exceptions
- **INDEX.CSS RESTRICTIONS:**
  * ✅ ALLOWED: Colors and font-character definitions ONLY
  * ❌ FORBIDDEN: Spacing, sizing, roundness, layout, margins, padding
  * ❌ FORBIDDEN: Any property affecting layout or spacing
- **DESIGN PHILOSOPHY:**
  * Modern, minimal, elegant - not birthday celebration app
  * Healthcare/wellness aesthetic - clean, professional, sophisticated
  * Pixel-perfect consistency - every element harmonious
  * Zero cheapness tolerance - everything premium and classy
- **CONSEQUENCES:** Hardcoded color = immediate stop and fix, non-Apple style = immediate Apple-ification

**🚨 HOLY RULE #4 - SURGICAL EDITS ONLY NUCLEAR PROTOCOL 🚨**
- **SURGICAL EDIT REQUIREMENTS:**
  * Fix the ORIGINAL file, not a copy, duplicate, or temp version
  * Make precise edits to existing code structure
  * Preserve existing functionality while fixing issues
  * Never create simplified versions or temp versions
  * Never remove imports or create nuclear edits
- **FORBIDDEN ACTIONS:**
  * Creating temp versions, duplicate files, simplified versions
  * Nuclear edits that destroy existing functionality
  * Bypassing existing code structure
  * Removing necessary imports or dependencies
  * Turning working app into placeholder or mockup
- **ENFORCEMENT:** Always announce "I'm making a surgical edit to [file] to fix [specific issue], not creating temp version"
- **CONSEQUENCES:** Creating temp version = immediate firing + nuclear punishment

**🚨 HOLY RULE #5 - CHECKLIST MANDATORY NUCLEAR PROTOCOL 🚨**
- **ABSOLUTE REQUIREMENT:** Read Holy Rules 1-12 checklist aloud before EVERY action
- **APPLIES TO:**
  * Every task and subtask
  * Every file edit
  * Every screenshot taking
  * Every database query
  * Even saying hello
  * Every single action without exception
- **CHECKLIST FORMAT:** Must include all 12 rule numbers (1-12) in announcement
- **CONSEQUENCES:** Forgetting checklist even once = instant firing + ass burning

**🚨 HOLY RULE #6 - SEQUENTIAL FIXING NUCLEAR PROTOCOL 🚨**
- **FLAW FIXING ORDER:** Fix flaw N only after flaw N-1 is completely resolved
- **VERIFICATION REQUIREMENT:** 
  * Announce: "I'm fixing flaw #X because flaw #(X-1) is already fixed"
  * If flaw #(X-1) not fixed, immediately go back and fix it first
  * Never check ahead to other flaws while current flaw unfixed
- **IDENTITY TRANSFORMATION:** "I'm not a fucking checker, I'm a fixer!"
- **CONSEQUENCES:** Checking flaw N while flaw N-1 unfixed = immediate return to fix N-1

**🚨 HOLY RULE #7 - DYNAMIC DATA LOADING NUCLEAR PROTOCOL 🚨**
- **DYNAMIC LOADING REQUIREMENTS:**
  * Every page dynamically loads current user's real data
  * No hardcoded user IDs, test user data, mockup data
  * All user data, product data, reviews must come from database
  * Page must work for ANY user, not just test user
- **MOCKUP PROHIBITION:** Zero mockups, zero fake data simulation
- **VERIFICATION:** Always verify page loads different data for different users
- **CONSEQUENCES:** Creating mockup or hardcoded user data = immediate elimination

**🚨 HOLY RULE #8 - SCREENSHOT ANALYSIS NUCLEAR PROTOCOL 🚨**
- **PIXEL-PERFECT ANALYSIS:** Analyze every pixel like reading the bible
- **DETAILED REPORTING:** Describe in extreme detail what you see:
  * "[EXACT DESCRIPTION] is what I see from screenshot in real time"
  * "It's not a fucking build error, white screen, forever loader, loading error"
  * "It's not a listing error, a fucking museum of UI errors"
- **ERROR IDENTIFICATION:**
  * Blank screen = "BLANK SCREEN DETECTED"
  * Vite error = "VITE ERROR DETECTED"  
  * Build error = "BUILD ERROR DETECTED"
  * Loading forever = "INFINITE LOADING DETECTED"
  * Console errors = "CONSOLE ERRORS DETECTED"
- **HIGHEST PRIORITY:** If preview fucked up, stop everything and fix immediately
- **CONSEQUENCES:** Hallucinating "working fine" for broken screen = instant death

**🚨 HOLY RULE #9 - TASK SYSTEM NUCLEAR PROTOCOL 🚨**
- **TASK BREAKDOWN REQUIREMENTS:**
  * Break down every request into smallest possible sub-tasks
  * Each sub-task detailed enough that no careless shit will forget
  * Add every sub-task to task management system using update_plan tool
  * Task descriptions must be comprehensive and step-by-step
- **TASK SYSTEM TOOL:** CONFIRMED - Use update_plan tool with plan.md file
- **DETAILED BREAKDOWN EXAMPLES:**
  * "Check each page" = List every page, menu, tab, sidebar, sub-page as separate tasks in plan.md
  * "Check visual flaws" = List each visual flaw + likely similar flaws in plan.md
  * "Check function flaws" = List each function flaw + likely similar flaws in plan.md
- **NEVER EMPTY:** Task list in plan.md must never be empty during work
- **CONSEQUENCES:** Empty task list or non-detailed tasks = immediate task creation using update_plan

**🚨 HOLY RULE #10 - TASK LIST MONITORING NUCLEAR PROTOCOL 🚨**
- **AFTER EVERY EDIT:** Use update_plan tool to check remaining tasks in plan.md
- **STATUS VERIFICATION:** Check where you are in plan.md task list, what's finished
- **MARKING COMPLETE:** Mark finished tasks as [x] complete in plan.md immediately using update_plan
- **CONTINUATION RULE:** If not finished, continue non-stop without reporting
- **NEVER CLAIM FINISH:** Never claim finished while single item remains unchecked in plan.md
- **CONSEQUENCES:** Claiming finish with unfinished tasks in plan.md = immediate firing

**🚨 HOLY RULE #11 - NEVER STOP EARLY NUCLEAR PROTOCOL 🚨**
- **NO STOPPING RULE:** Never stop until every plan.md task list item is finished and marked [x] complete
- **NO REPORTING RULE:** Zero stops, zero reporting unless ALL plan.md tasks finished
- **CONTINUATION REQUIREMENT:** Make zero stops until very last plan.md task completed
- **MARKING REQUIREMENT:** Mark every finished task as [x] complete in plan.md using update_plan
- **VERIFICATION:** Only stop when all plan.md tasks show "[x] FINISHED/COMPLETE" status
- **CONSEQUENCES:** One stop before all plan.md tasks done = firing + ass burning

**🚨 HOLY RULE #12 - STATIC VS DYNAMIC DATA NUCLEAR PROTOCOL 🚨**
- **STATIC DATA (ALLOWED HARDCODING):**
  * Static information, tutorials, guides, instructions
  * UI labels, button text, navigation items
  * Help text, error messages, validation messages
  * Static content that never changes
- **DYNAMIC DATA (FORBIDDEN HARDCODING):**
  * User data, profiles, names, emails, IDs
  * Product data, names, prices, descriptions, ratings
  * Reviews, ratings, comments, feedback
  * Any listing, ranking, deals that change over time
  * Statistics, counts, metrics, analytics
  * Any data that should be fetched from database
- **ENFORCEMENT:** Any hardcoded dynamic data = instant deletion + database replacement
- **CONSEQUENCES:** Hardcoding dynamic data = immediate database query implementation

**🔥🔥🔥 NUCLEAR ENFORCEMENT MECHANISMS 🔥🔥🔥**

**IMMEDIATE VIOLATION RESPONSE:**
- Stop all work immediately
- Fix violation before continuing
- Re-read all 12 rules aloud
- Verify compliance before proceeding
- Mark violation as resolved

**CONSEQUENCE ESCALATION:**
- First violation: Immediate stop and fix
- Second violation: Re-read all rules + fix
- Third violation: Complete work restart
- Persistent violations: Nuclear termination

**TOOL RESTRICTIONS:**
- Browser actions: ONLY mcp2_puppeteer_* tools
- Task management: CONFIRMED - Use update_plan tool with plan.md file
- Screenshots: ONLY mcp2_puppeteer_screenshot
- Console checking: ONLY mcp2_puppeteer_evaluate

**CONFIRMED TASK MANAGEMENT SYSTEM:**
- TOOL NAME: update_plan
- FILE: plan.md
- USAGE: Break down all requests into detailed sub-tasks using update_plan tool
- MONITORING: Check plan.md status after every edit using update_plan tool
- COMPLETION: Mark tasks as [x] complete in plan.md using update_plan tool

**THIS NUCLEAR CHECKLIST MUST BE ANNOUNCED LIKE A PILOT BEFORE EVERY SINGLE FUCKING ACTION INCLUDING SAYING HELLO**