{"mcpServers": {"puppeteer": {"command": "npx", "args": ["puppeteer-mcp-server"], "env": {"PATH": "/opt/homebrew/bin:/usr/local/bin:/usr/bin:/bin", "HTTP_PROXY": "", "HTTPS_PROXY": "", "http_proxy": "", "https_proxy": "", "NO_PROXY": "localhost,127.0.0.1,::1"}}, "playwright": {"command": "npx", "args": ["@playwright/mcp"], "env": {"PATH": "/opt/homebrew/bin:/usr/local/bin:/usr/bin:/bin", "HTTP_PROXY": "", "HTTPS_PROXY": "", "http_proxy": "", "https_proxy": "", "NO_PROXY": "localhost,127.0.0.1,::1"}}}}