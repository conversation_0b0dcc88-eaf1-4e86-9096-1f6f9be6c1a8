"Chromium Shortcut" = "Chromium 快捷方式";
CFBundleGetInfoString = "Google Chrome for Testing 138.0.7204.183, 版权所有 2025 Google LLC. 保留所有权利。";
NSBluetoothAlwaysUsageDescription = "一旦 Chromium 获得了使用权限，网站便能请求您向其授予使用权限。";
NSBluetoothPeripheralUsageDescription = "一旦 Chromium 获得了使用权限，网站便能请求您向其授予使用权限。";
NSCameraUsageDescription = "一旦 Chromium 获得了使用权限，网站便能请求您向其授予使用权限。";
NSHumanReadableCopyright = "版权所有 2025 Google LLC. 保留所有权利。";
NSLocalNetworkUsageDescription = "这样，您就可以从可用设备中选择并在这些设备上显示内容。";
NSLocationUsageDescription = "一旦 Chromium 获得了使用权限，网站便能请求您向其授予使用权限。";
NSMicrophoneUsageDescription = "一旦 Chromium 获得了使用权限，网站便能请求您向其授予使用权限。";
NSWebBrowserPublicKeyCredentialUsageDescription = "一旦 Chromium 获得了使用权限，网站便能请求您向其授予使用权限。";
