"Chromium Shortcut" = "Ярлык Chromium";
CFBundleGetInfoString = "Google Chrome for Testing 138.0.7204.183, © Google LLC, 2025. Все права защищены.";
NSBluetoothAlwaysUsageDescription = "Когда вы предоставите доступ Chromium, веб-сайты также смогут запрашивать у вас доступ.";
NSBluetoothPeripheralUsageDescription = "Когда вы предоставите доступ Chromium, веб-сайты также смогут запрашивать у вас доступ.";
NSCameraUsageDescription = "Когда вы предоставите доступ Chromium, веб-сайты также смогут запрашивать у вас доступ.";
NSHumanReadableCopyright = "© Google LLC, 2025. Все права защищены.";
NSLocalNetworkUsageDescription = "Вы сможете выбирать любые из доступных устройств и показывать на них контент.";
NSLocationUsageDescription = "Когда вы предоставите доступ Chromium, веб-сайты также смогут запрашивать у вас доступ.";
NSMicrophoneUsageDescription = "Когда вы предоставите доступ Chromium, веб-сайты также смогут запрашивать у вас доступ.";
NSWebBrowserPublicKeyCredentialUsageDescription = "Когда вы предоставите доступ Chromium, веб-сайты также смогут запрашивать у вас доступ.";
