"Chromium Shortcut" = "Chromiumi otsetee";
CFBundleGetInfoString = "Google Chrome for Testing 138.0.7204.183, Autoriõigus 2025 Google LLC. Kõik õigused on kaitstud.";
NSBluetoothAlwaysUsageDescription = "Kui annate Chromiumile juurdepääsu, saavad veebisaidid küsida teilt juurdepääsuluba.";
NSBluetoothPeripheralUsageDescription = "Kui annate Chromiumile juurdepääsu, saavad veebisaidid küsida teilt juurdepääsuluba.";
NSCameraUsageDescription = "Kui annate Chromiumile juurdepääsu, saavad veebisaidid küsida teilt juurdepääsuluba.";
NSHumanReadableCopyright = "Autoriõigus 2025 Google LLC. Kõik õigused on kaitstud.";
NSLocalNetworkUsageDescription = "See võimaldab teil valida saadaolevate seadmete hulgast ja kuvada neis sisu.";
NSLocationUsageDescription = "Kui annate Chromiumile juurdepääsu, saavad veebisaidid küsida teilt juurdepääsuluba.";
NSMicrophoneUsageDescription = "Kui annate Chromiumile juurdepääsu, saavad veebisaidid küsida teilt juurdepääsuluba.";
NSWebBrowserPublicKeyCredentialUsageDescription = "Kui annate Chromiumile juurdepääsu, saavad veebisaidid küsida teilt juurdepääsuluba.";
