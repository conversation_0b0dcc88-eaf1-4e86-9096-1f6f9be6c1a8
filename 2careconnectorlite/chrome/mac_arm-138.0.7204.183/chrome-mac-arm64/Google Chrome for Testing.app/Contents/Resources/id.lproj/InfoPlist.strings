"Chromium Shortcut" = "Pintasan Chromium";
CFBundleGetInfoString = "Google Chrome for Testing 138.0.7204.183, Hak cipta 2025 Google LLC. Semua hak dilindungi undang-undang.";
NSBluetoothAlwaysUsageDescription = "Setelah Chromium memiliki akses, situs dapat meminta akses dari Anda.";
NSBluetoothPeripheralUsageDescription = "Setelah Chromium memiliki akses, situs dapat meminta akses dari Anda.";
NSCameraUsageDescription = "Setelah Chromium memiliki akses, situs dapat meminta akses dari Anda.";
NSHumanReadableCopyright = "Hak cipta 2025 Google LLC. Semua hak dilindungi undang-undang.";
NSLocalNetworkUsageDescription = "Tindakan ini akan memungkinkan Anda memilih dari perangkat yang tersedia dan menampilkan konten di perangkat tersebut.";
NSLocationUsageDescription = "Setelah Chromium memiliki akses, situs dapat meminta akses dari Anda.";
NSMicrophoneUsageDescription = "Setelah Chromium memiliki akses, situs dapat meminta akses dari Anda.";
NSWebBrowserPublicKeyCredentialUsageDescription = "Setelah Chromium memiliki akses, situs dapat meminta akses dari Anda.";
