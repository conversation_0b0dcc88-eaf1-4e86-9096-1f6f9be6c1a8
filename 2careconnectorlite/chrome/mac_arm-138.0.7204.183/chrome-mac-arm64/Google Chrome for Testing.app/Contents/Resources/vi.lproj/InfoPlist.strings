"Chromium Shortcut" = "Lối tắt tới Chromium";
CFBundleGetInfoString = "Google Chrome for Testing 138.0.7204.183, Bản quyền 2025 Google LLC. Mọi quyền được bảo lưu.";
NSBluetoothAlwaysUsageDescription = "Sau khi Chromium có quyền truy cập, các trang web sẽ có thể yêu cầu bạn cấp quyền truy cập.";
NSBluetoothPeripheralUsageDescription = "Sau khi Chromium có quyền truy cập, các trang web sẽ có thể yêu cầu bạn cấp quyền truy cập.";
NSCameraUsageDescription = "Sau khi Chromium có quyền truy cập, các trang web sẽ có thể yêu cầu bạn cấp quyền truy cập.";
NSHumanReadableCopyright = "Bản quyền 2025 Google LLC. M<PERSON>i quyền được bảo lưu.";
NSLocalNetworkUsageDescription = "Thao tác này sẽ cho phép bạn chọn trong số các thiết bị có sẵn và hiển thị nội dung trên các thiết bị đó.";
NSLocationUsageDescription = "Sau khi Chromium có quyền truy cập, các trang web sẽ có thể yêu cầu bạn cấp quyền truy cập.";
NSMicrophoneUsageDescription = "Sau khi Chromium có quyền truy cập, các trang web sẽ có thể yêu cầu bạn cấp quyền truy cập.";
NSWebBrowserPublicKeyCredentialUsageDescription = "Sau khi Chromium có quyền truy cập, các trang web sẽ có thể yêu cầu bạn cấp quyền truy cập.";
