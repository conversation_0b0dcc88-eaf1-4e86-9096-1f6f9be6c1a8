"Chromium Shortcut" = "Chromium-Verknüpfung";
CFBundleGetInfoString = "Google Chrome for Testing 138.0.7204.183, Copyright 2025 Google LLC. Alle Rechte vorbehalten.";
NSBluetoothAlwaysUsageDescription = "Sobald Chromium Zugriff hat, können Websites dich um Zugriff bitten.";
NSBluetoothPeripheralUsageDescription = "Sobald Chromium Zugriff hat, können Websites dich um Zugriff bitten.";
NSCameraUsageDescription = "Sobald Chromium Zugriff hat, können Websites dich um Zugriff bitten.";
NSHumanReadableCopyright = "Copyright 2025 Google LLC. Alle Rechte vorbehalten.";
NSLocalNetworkUsageDescription = "So kannst du aus verfügbaren Geräten auswählen und Inhalte darauf anzeigen lassen.";
NSLocationUsageDescription = "Sobald Chromium Zugriff hat, können Websites dich um Zugriff bitten.";
NSMicrophoneUsageDescription = "Sobald Chromium Zugriff hat, können Websites dich um Zugriff bitten.";
NSWebBrowserPublicKeyCredentialUsageDescription = "Sobald Chromium Zugriff hat, können Websites dich um Zugriff bitten.";
