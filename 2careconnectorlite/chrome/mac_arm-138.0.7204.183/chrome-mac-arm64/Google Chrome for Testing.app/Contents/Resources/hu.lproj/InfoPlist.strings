"Chromium Shortcut" = "Chromium-parancsikon";
CFBundleGetInfoString = "Google Chrome for Testing 138.0.7204.183, Copyright 2025 Google LLC. Minden jog fenntartva.";
NSBluetoothAlwaysUsageDescription = "<PERSON><PERSON><PERSON> hozzáférést biztosított a Chromiumnak, a webhelyek is hozzáférést kérhetnek.";
NSBluetoothPeripheralUsageDescription = "Miután hozzáférést biztosított a Chromiumnak, a webhelyek is hozzáférést kérhetnek.";
NSCameraUsageDescription = "<PERSON><PERSON><PERSON> hozzáférést biztosított a Chromiumnak, a webhelyek is hozzáférést kérhetnek.";
NSHumanReadableCopyright = "Copyright 2025 Google LLC. Minden jog fenntartva.";
NSLocalNetworkUsageDescription = "Ez lehetővé teszi, hogy válasszon a rendelkezésre álló eszköz<PERSON><PERSON> kö<PERSON><PERSON>, és tartalmakat jelenítsen meg rajtuk.";
NSLocationUsageDescription = "Miután hozzáférést biztosított a Chromiumnak, a webhelyek is hozzáférést kérhetnek.";
NSMicrophoneUsageDescription = "Miután hozzáférést biztosított a Chromiumnak, a webhelyek is hozzáférést kérhetnek.";
NSWebBrowserPublicKeyCredentialUsageDescription = "Miután hozzáférést biztosított a Chromiumnak, a webhelyek is hozzáférést kérhetnek.";
