<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>en</string>
	<key>CFBundleDisplayName</key>
	<string>Google Chrome for Testing Helper</string>
	<key>CFBundleExecutable</key>
	<string>Google Chrome for Testing Helper</string>
	<key>CFBundleIdentifier</key>
	<string>com.google.chrome.for.testing.helper</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>Google Chrome for Testing Helper</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>138.0.7204.183</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleVersion</key>
	<string>7204.183</string>
	<key>DTCompiler</key>
	<string>com.apple.compilers.llvm.clang.1_0</string>
	<key>DTSDKBuild</key>
	<string>24E241</string>
	<key>DTSDKName</key>
	<string>macosx15.4</string>
	<key>DTXcode</key>
	<string>1630</string>
	<key>DTXcodeBuild</key>
	<string>16E140</string>
	<key>LSEnvironment</key>
	<dict>
		<key>MallocNanoZone</key>
		<string>0</string>
	</dict>
	<key>LSFileQuarantineEnabled</key>
	<true/>
	<key>LSMinimumSystemVersion</key>
	<string>11.0</string>
	<key>LSUIElement</key>
	<string>1</string>
	<key>NSCameraReactionEffectGesturesEnabledDefault</key>
	<false/>
	<key>NSCameraUseContinuityCameraDeviceType</key>
	<true/>
	<key>NSSupportsAutomaticGraphicsSwitching</key>
	<true/>
</dict>
</plist>
