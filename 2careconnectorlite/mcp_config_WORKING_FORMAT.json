{"mcpServers": {"puppeteer": {"command": "mcp-server-puppeteer", "env": {"HTTP_PROXY": "", "HTTPS_PROXY": "", "SOCKS_PROXY": "", "ALL_PROXY": "", "NO_PROXY": "*", "npm_config_proxy": "", "npm_config_https_proxy": "", "npm_config_registry": "https://registry.npmjs.org/", "NODE_TLS_REJECT_UNAUTHORIZED": "0", "GEPH_BYPASS": "true", "DIRECT_CONNECTION": "true"}}, "supabase": {"command": "npx", "args": ["-y", "@supabase/mcp-server-supabase@latest", "--access-token", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inlla2FycWFuaXJka2Rja2ltcG5hIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzM4NzE1OTcsImV4cCI6MjA0OTQ0NzU5N30.wBMLiZ8KPCfPy-GeCOb7xnSBCdQhMX2wDpfnqHGvQH0"], "env": {"SUPABASE_URL": "https://yekarqanirdkdckimpna.supabase.co", "HTTP_PROXY": "", "HTTPS_PROXY": "", "SOCKS_PROXY": "", "ALL_PROXY": "", "NO_PROXY": "*", "npm_config_proxy": "", "npm_config_https_proxy": "", "npm_config_registry": "https://registry.npmjs.org/", "NODE_TLS_REJECT_UNAUTHORIZED": "0", "GEPH_BYPASS": "true", "DIRECT_CONNECTION": "true"}}}}