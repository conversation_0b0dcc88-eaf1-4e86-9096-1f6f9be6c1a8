{"mcpServers": {"puppeteer": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-puppeteer"], "env": {}}, "supabase": {"command": "npx", "args": ["-y", "@supabase/mcp-server-supabase@latest", "--access-token", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inlla2FycWFuaXJka2Rja2ltcG5hIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzM4NzE1OTcsImV4cCI6MjA0OTQ0NzU5N30.wBMLiZ8KPCfPy-GeCOb7xnSBCdQhMX2wDpfnqHGvQH0", "--project-ref", "yekarqanirdkdckimpna"], "env": {"SUPABASE_PROJECT_REF": "yekarqanirdkdckimpna"}}}}