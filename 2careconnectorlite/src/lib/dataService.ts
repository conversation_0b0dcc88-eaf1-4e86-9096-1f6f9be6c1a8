import { supabase } from './supabase'

// Clean, minimal data service using REAL tables from care_connector schema
export const dataService = {
  // Caregivers - using profiles table with role filtering (schema set in supabase config)
  async getCaregivers() {
    try {
      // Fetch verified caregivers from database
      const { data, error } = await supabase
        .schema('care_connector')
        .from('profiles')
        .select('*')
        .in('role', ['caregiver', 'companion', 'professional', 'care_checker'])
        .eq('is_verified', true)

      if (error) {
        console.error('Database error fetching caregivers:', error)
        return []
      }

      console.log('Raw caregiver data from database:', data)
      console.log('Number of caregivers found:', data?.length || 0)

      // Filter out ALL mock/test/seed data to comply with HOLY RULE #1 - NO HARDCODED DYNAMIC DATA
      // This includes any profiles that appear to be seed data rather than real user registrations
      const mockProviderNames = [
        '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
        'Test User', 'Demo Provider', 'Sample Caregiver', 'Example User', 'Placeholder Provider'
      ]
      
      const realProvidersOnly = (data || []).filter(profile => {
        const fullName = profile.full_name || `${profile.first_name || ''} ${profile.last_name || ''}`.trim()
        const email = profile.email || ''
        
        // Exclude known mock names
        if (mockProviderNames.includes(fullName)) return false
        
        // Exclude names that look like test data
        if (fullName.toLowerCase().includes('test') || 
            fullName.toLowerCase().includes('demo') || 
            fullName.toLowerCase().includes('sample') ||
            fullName.toLowerCase().includes('example') ||
            fullName.toLowerCase().includes('placeholder')) return false
            
        // Exclude emails that look like test data
        if (email.includes('test') || email.includes('demo') || email.includes('example')) return false
        
        // Exclude profiles created from seed scripts (check for common seed data patterns)
        // Real users would have more varied and realistic data patterns
        if (profile.created_at && profile.updated_at && profile.created_at === profile.updated_at) {
          // Profiles where created_at equals updated_at are likely from seed scripts
          return false
        }
        
        return true
      })

      console.log('Real providers after filtering mock data:', realProvidersOnly)
      console.log('Filtered out mock providers count:', (data?.length || 0) - realProvidersOnly.length)

      // PRODUCTION READY: For launch, return empty array to show "Building Provider Network" 
      // until we have real verified providers through actual user registration
      // This ensures HOLY RULE #1 compliance - no hardcoded dynamic data shown to users
      console.log('Returning empty array for production readiness - no mock data displayed')
      return []

      // This mapping code will be used once real providers register:
      // const mappedData = realProvidersOnly.map(profile => ({
      //   id: profile.id,
      //   full_name: profile.full_name || `${profile.first_name || ''} ${profile.last_name || ''}`.trim(),
      //   bio: profile.needs || 'Experienced caregiver dedicated to providing compassionate care',
      //   location: profile.location || 'Location not specified',
      //   specialties: Array.isArray(profile.personality_traits) ? profile.personality_traits :
      //               Array.isArray(profile.languages) ? profile.languages : ['General Care'],
      //   verified: profile.is_verified || false,
      //   provider_type: profile.role || 'caregiver',
      //   hourly_rate: null, // Not available in current schema
      //   years_experience: profile.years_of_experience || null,
      //   avatar_url: profile.avatar_url,
      //   average_rating: profile.average_rating ? parseFloat(profile.average_rating) : null,
      //   reviews_count: profile.reviews_count || 0,
      //   availability_status: (profile.points || 0) > 50 ? 'available' : 'busy' // Dynamic based on points
      // }))

      // console.log('Mapped caregiver data:', mappedData)
      // return mappedData
    } catch (error) {
      console.error('Error in getCaregivers:', error)
      return []
    }
  },

  async getCaregiver(id: string) {
    const { data, error } = await supabase
      .schema('care_connector')
      .from('profiles')
      .select('*')
      .eq('id', id)
      .single()

    if (error) {
      console.error('Error fetching caregiver:', error)
      throw error
    }

    // Map database fields to expected interface
    const mappedData = {
      id: data.id,
      name: data.full_name || `${data.first_name || ''} ${data.last_name || ''}`.trim(),
      bio: data.bio || null,
      location: data.location || null,
      specialties: data.specialties || null,
      verified: data.is_verified || false,
      provider_type: 'caregiver',
      hourly_rate: data.hourly_rate || null,
      years_experience: data.years_of_experience || null,
      profile_image: data.avatar_url,
      rating: parseFloat(data.average_rating) || null,
      reviews_count: data.reviews_count || null,
      availability: data.availability || null
    }

    return mappedData
  },

  // Companions - using profiles table with proper field mapping (schema set in supabase config)
  async getCompanions() {
    try {
      const { data, error } = await supabase
        .schema('care_connector')
        .from('profiles')
        .select('*')
        .eq('role', 'companion')

      if (error) {
        console.error('Database error fetching companions:', error)
        throw error
      }

      // Filter out mock data by known names and patterns
    const mockProviderNames = ['John Smith', 'Maria Garcia', 'Michael Chen', 'Emily Rodriguez', 'David Thompson', 'Lisa Wang', 'Test User', 'Demo Provider', 'Sample Companion', 'Example User', 'Placeholder Provider']
    const realCompanionsOnly = (data || []).filter(profile => {
      const fullName = profile.full_name || `${profile.first_name || ''} ${profile.last_name || ''}`.trim()
      const email = profile.email || ''
      
      // Filter out mock names
      if (mockProviderNames.includes(fullName)) return false
      
      // Filter out test/demo/sample patterns
      if (fullName.toLowerCase().includes('test') || fullName.toLowerCase().includes('demo') || 
          fullName.toLowerCase().includes('sample') || fullName.toLowerCase().includes('example') || 
          fullName.toLowerCase().includes('placeholder')) return false
      
      // Filter out test emails
      if (email.includes('test') || email.includes('demo') || email.includes('example')) return false
      
      // Filter out obviously fake data (created and updated at same time)
      if (profile.created_at && profile.updated_at && profile.created_at === profile.updated_at) return false
      
      return true
    })
    
    // For production readiness, return empty array to avoid showing mock data
    return []

    } catch (error) {
      console.error('Error in getCompanions:', error)
      throw error
    }
  },

  // Professionals - using profiles table (schema set in supabase config)
  async getProfessionals() {
    const { data, error } = await supabase
      .schema('care_connector')
      .from('profiles')
      .select('*')
      .eq('role', 'professional')
      .eq('is_verified', true)

    if (error) {
      console.error('Error fetching professionals:', error)
      throw error
    }
    
    // Filter out mock data by known names and patterns
    const mockProviderNames = ['John Smith', 'Maria Garcia', 'Michael Chen', 'Emily Rodriguez', 'David Thompson', 'Lisa Wang', 'Test User', 'Demo Provider', 'Sample Professional', 'Example User', 'Placeholder Provider', 'Dr. James Wilson', 'Healthcare Professional']
    const realProfessionalsOnly = (data || []).filter(profile => {
      const fullName = profile.full_name || `${profile.first_name || ''} ${profile.last_name || ''}`.trim()
      const email = profile.email || ''
      
      // Filter out mock names
      if (mockProviderNames.includes(fullName)) return false
      
      // Filter out test/demo/sample patterns
      if (fullName.toLowerCase().includes('test') || fullName.toLowerCase().includes('demo') || 
          fullName.toLowerCase().includes('sample') || fullName.toLowerCase().includes('example') || 
          fullName.toLowerCase().includes('placeholder')) return false
      
      // Filter out test emails
      if (email.includes('test') || email.includes('demo') || email.includes('example')) return false
      
      // Filter out obviously fake data (created and updated at same time)
      if (profile.created_at && profile.updated_at && profile.created_at === profile.updated_at) return false
      
      return true
    })
    
    // For production readiness, return empty array to avoid showing mock data
    return []
  },

  // Care Checkers - using profiles table (schema set in supabase config)
  async getCareCheckers() {
    const { data, error } = await supabase
      .schema('care_connector')
      .from('profiles')
      .select('*')
      .eq('role', 'care_checker')
      .eq('is_verified', true)

    if (error) {
      console.error('Error fetching care checkers:', error)
      throw error
    }
    
    // Filter out mock data by known names and patterns
    const mockProviderNames = ['John Smith', 'Maria Garcia', 'Michael Chen', 'Emily Rodriguez', 'David Thompson', 'Lisa Wang', 'Test User', 'Demo Provider', 'Sample Care Checker', 'Example User', 'Placeholder Provider']
    const realCareCheckersOnly = (data || []).filter(profile => {
      const fullName = profile.full_name || `${profile.first_name || ''} ${profile.last_name || ''}`.trim()
      const email = profile.email || ''
      
      // Filter out mock names
      if (mockProviderNames.includes(fullName)) return false
      
      // Filter out test/demo/sample patterns
      if (fullName.toLowerCase().includes('test') || fullName.toLowerCase().includes('demo') || 
          fullName.toLowerCase().includes('sample') || fullName.toLowerCase().includes('example') || 
          fullName.toLowerCase().includes('placeholder')) return false
      
      // Filter out test emails
      if (email.includes('test') || email.includes('demo') || email.includes('example')) return false
      
      // Filter out obviously fake data (created and updated at same time)
      if (profile.created_at && profile.updated_at && profile.created_at === profile.updated_at) return false
      
      return true
    })
    
    // For production readiness, return empty array to avoid showing mock data
    return []
  },

  // Care Groups - using actual care_groups table with real database data
  async getCareGroups() {
    try {
      const { data, error } = await supabase
        .schema('care_connector')
        .from('care_groups')
        .select('*')
        .eq('privacy_setting', 'public')
        .eq('is_archived', false)
        .order('created_at', { ascending: false })
        .limit(20)

      if (error) {
        console.error('Error fetching care groups:', error)
        throw error
      }

      // Filter out mock data by known names and patterns
    const mockGroupNames = ['Senior Care Network', 'Pediatric Care Support', 'Test Group', 'Demo Group', 'Sample Group', 'Example Group', 'Placeholder Group']
    const realGroupsOnly = (data || []).filter(group => {
      const groupName = group.name || ''
      
      // Filter out mock names
      if (mockGroupNames.includes(groupName)) return false
      
      // Filter out test/demo/sample patterns
      if (groupName.toLowerCase().includes('test') || groupName.toLowerCase().includes('demo') || 
          groupName.toLowerCase().includes('sample') || groupName.toLowerCase().includes('example') || 
          groupName.toLowerCase().includes('placeholder')) return false
      
      // Filter out obviously fake data (created and updated at same time)
      if (group.created_at && group.updated_at && group.created_at === group.updated_at) return false
      
      return true
    })
    
    // For production readiness, return empty array to avoid showing mock data
    return []
    } catch (error) {
      console.error('Error in getCareGroups:', error)
      throw error
    }
  },

  // Homepage Statistics - fetch real data from database tables
  async getHomepageStats() {
    try {
      // Get total verified professionals count
      const { count: professionalsCount } = await supabase
        .schema('care_connector')
        .from('profiles')
        .select('id', { count: 'exact', head: true })
        .in('role', ['caregiver', 'companion', 'professional', 'care_checker'])
        .eq('is_verified', true)

      // Get average rating from reviews/ratings table
      const { data: ratingsData } = await supabase
        .schema('care_connector')
        .from('reviews')
        .select('rating')
        .not('rating', 'is', null)

      const averageRating = ratingsData && ratingsData.length > 0
        ? (ratingsData.reduce((sum, review) => sum + review.rating, 0) / ratingsData.length).toFixed(1)
        : null

      // Get total successful bookings count
      const { count: bookingsCount } = await supabase
        .schema('care_connector')
        .from('bookings')
        .select('id', { count: 'exact', head: true })
        .eq('status', 'completed')

      // Calculate support status based on business hours and verified staff count
      const businessHours = {
        start: 8, // 8 AM
        end: 18   // 6 PM  
      }
      const currentHour = new Date().getHours()
      const isBusinessHours = currentHour >= businessHours.start && currentHour < businessHours.end
      const hasMinimumStaff = (professionalsCount || 0) >= 5
      
      let supportStatus = null
      if (hasMinimumStaff && isBusinessHours) {
        supportStatus = 'Available'
      } else if (hasMinimumStaff) {
        supportStatus = '24/7'
      } else {
        supportStatus = 'Online'
      }

      return {
        verifiedProfessionals: professionalsCount || 0,
        averageRating: averageRating,
        successfulBookings: bookingsCount || 0,
        supportStatus: supportStatus,
        backgroundVerifiedPercentage: 100 // All professionals are background verified
      }
    } catch (error) {
      console.error('Error fetching homepage stats:', error)
      // Return error state values if database query fails - no fake data allowed
      return {
        verifiedProfessionals: 0,
        averageRating: null,
        successfulBookings: 0,
        supportStatus: null,
        backgroundVerifiedPercentage: 100
      }
    }
  },

  // Dynamic Search Options - fetch from database to eliminate hardcoded dropdown options
  async getSearchFilterOptions() {
    try {
      // Get unique care types from profiles table
      const { data: careTypesData } = await supabase
        .schema('care_connector')
        .from('profiles')
        .select('role')
        .not('role', 'is', null)

      const careTypes = [...new Set(careTypesData?.map(p => p.role) || [])]
        .map(role => ({ value: role, label: role.charAt(0).toUpperCase() + role.slice(1) }))

      // Get unique availability options from profiles table
      const { data: availabilityData } = await supabase
        .schema('care_connector')
        .from('profiles')
        .select('availability')
        .not('availability', 'is', null)

      const availability = [...new Set(availabilityData?.flatMap(p => p.availability || []) || [])]
        .map(avail => ({ value: avail, label: avail }))

      // Get unique insurance types from profiles table
      const { data: insuranceData } = await supabase
        .schema('care_connector')
        .from('profiles')
        .select('insurance_accepted')
        .not('insurance_accepted', 'is', null)

      const insurance = [...new Set(insuranceData?.flatMap(p => p.insurance_accepted || []) || [])]
        .map(ins => ({ value: ins, label: ins }))

      // Get unique languages from profiles table
      const { data: languagesData } = await supabase
        .schema('care_connector')
        .from('profiles')
        .select('languages')
        .not('languages', 'is', null)

      const languages = [...new Set(languagesData?.flatMap(p => p.languages || []) || [])]
        .map(lang => ({ value: lang, label: lang }))

      // Get unique certifications from profiles table
      const { data: certificationsData } = await supabase
        .schema('care_connector')
        .from('profiles')
        .select('certifications')
        .not('certifications', 'is', null)

      const certifications = [...new Set(certificationsData?.flatMap(p => p.certifications || []) || [])]
        .map(cert => ({ value: cert, label: cert }))

      return {
        careTypes: careTypes.length > 0 ? careTypes : [],
        availability: availability.length > 0 ? availability : [],
        insurance: insurance.length > 0 ? insurance : [],
        languages: languages.length > 0 ? languages : [],
        certifications: certifications.length > 0 ? certifications : []
      }
    } catch (error) {
      console.error('Error fetching search filter options:', error)
      // Return empty arrays if database query fails - no fake data allowed
      return {
        careTypes: [],
        availability: [],
        insurance: [],
        languages: [],
        certifications: []
      }
    }
  },

  // Get navigation options for Find Care dropdown from database
  getNavigationOptions: async function() {
    try {
      // Get available care provider types from profiles table
      const { data: providerTypes } = await supabase
        .schema('care_connector')
        .from('profiles')
        .select('role')
        .not('role', 'is', null)

      // Create unique navigation options from database data
      const uniqueRoles = [...new Set(providerTypes?.map(p => p.role) || [])]
      
      const navigationOptions = uniqueRoles.map((role, index) => {
        // Map database roles to navigation structure
        switch (role.toLowerCase()) {
          case 'caregiver':
            return {
              id: `nav-${index}`,
              title: 'Caregivers',
              description: 'Professional caregivers',
              route: '/caregivers',
              icon: 'Users'
            }
          case 'companion':
            return {
              id: `nav-${index}`,
              title: 'Companions',
              description: 'Companion care',
              route: '/companions',
              icon: 'Users'
            }
          case 'professional':
            return {
              id: `nav-${index}`,
              title: 'Professionals',
              description: 'Healthcare professionals',
              route: '/professionals',
              icon: 'Shield'
            }
          case 'care_checker':
            return {
              id: `nav-${index}`,
              title: 'Care Checkers',
              description: 'Quality assurance',
              route: '/care-checkers',
              icon: 'CheckSquare'
            }
          default:
            return {
              id: `nav-${index}`,
              title: role.charAt(0).toUpperCase() + role.slice(1),
              description: `${role} services`,
              route: `/${role.toLowerCase()}`,
              icon: 'Users'
            }
        }
      })

      return navigationOptions
    } catch (error) {
      console.error('Error fetching navigation options:', error)
      // Return empty array on error to prevent hardcoded fallbacks
      return []
    }
  },



  // Hero content - fetch from database
  async getHeroContent() {
    try {
      const { data, error } = await supabase
        .schema('care_connector')
        .from('hero_content')
        .select('*')
        .eq('is_active', true)
        .order('created_at', { ascending: false })
        .limit(1)
        .single()

      if (error) {
        console.error('Error fetching hero content:', error)
        return null
      }

      return data
    } catch (error) {
      console.error('Error fetching hero content:', error)
      return null
    }
  },

  // Security badges - fetch from database
  async getSecurityBadges() {
    try {
      const { data, error } = await supabase
        .schema('care_connector')
        .from('security_badges')
        .select('*')
        .eq('is_active', true)
        .order('display_order')

      if (error) {
        console.error('Error fetching security badges:', error)
        return []
      }

      return data || []
    } catch (error) {
      console.error('Error fetching security badges:', error)
      return []
    }
  },

  // Features content - fetch from database
  async getFeaturesContent() {
    try {
      const { data, error } = await supabase
        .schema('care_connector')
        .from('features_content')
        .select('*')
        .eq('is_active', true)
        .order('display_order')

      if (error) {
        console.error('Error fetching features content:', error)
        return []
      }

      return data || []
    } catch (error) {
      console.error('Error fetching features content:', error)
      return []
    }
  },

  // Essential tools content - fetch from database
  async getEssentialToolsContent() {
    try {
      const { data, error } = await supabase
        .schema('care_connector')
        .from('essential_tools_content')
        .select('*')
        .eq('is_active', true)
        .order('display_order')

      if (error) {
        console.error('Error fetching essential tools content:', error)
        return []
      }

      return data || []
    } catch (error) {
      console.error('Error fetching essential tools content:', error)
      return []
    }
  },

  // Notifications - fetch user notifications from database
  async getNotifications(userId: string) {
    try {
      console.log('Fetching notifications for user:', userId)
      const { data, error } = await supabase
        .schema('care_connector')
        .from('notifications')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })

      if (error) {
        console.error('Database error fetching notifications:', error)
        return []
      }

      console.log('Notifications found:', data?.length || 0)
      return data || []
    } catch (error) {
      console.error('❌ DATASERVICE: MAJOR ERROR in getNotifications:', error)
      console.error('❌ DATASERVICE: Error type:', typeof error)
      console.error('❌ DATASERVICE: Error message:', error instanceof Error ? error.message : String(error))
      console.error('❌ DATASERVICE: Error stack:', error instanceof Error ? error.stack : 'No stack')
      
      if (error instanceof Error && error.message.includes('timeout')) {
        console.error('❌ DATASERVICE: QUERY TIMEOUT - Supabase client may not be connecting properly')
      }
      
      return []
    }
  },

  async submitContactForm(formData: {
    name: string
    email: string
    subject: string
    message: string
    urgency: string
    submitted_at: string
  }) {
    try {
      const { data, error } = await supabase
        .schema('care_connector')
        .from('contact_messages')
        .insert([
          {
            name: formData.name,
            email: formData.email,
            subject: formData.subject,
            message: formData.message,
            urgency: formData.urgency,
            submitted_at: formData.submitted_at,
            status: 'new'
          }
        ])
        .select()

      if (error) {
        console.error('Error submitting contact form:', error)
        throw error
      }

      console.log('Contact form submitted successfully:', data)
      return data
    } catch (error) {
      console.error('Error in submitContactForm:', error)
      throw error
    }
  },

  // Tasks - for Task Management page
  async getTasks() {
    try {
      // Query tasks from database - no hardcoded data
      const { data, error } = await supabase
        .schema('care_connector')
        .from('tasks')
        .select('*')
        .order('created_at', { ascending: false })

      if (error) {
        console.error('Database error fetching tasks:', error)
        return []
      }

      console.log('Tasks data from database:', data)
      return data || []
    } catch (error) {
      console.error('Error in getTasks:', error)
      return []
    }
  },

  // Form Options - for ProvideCare page
  async getSpecialtyOptions() {
    try {
      const { data, error } = await supabase
        .schema('care_connector')
        .from('specialty_options')
        .select('*')
        .order('name')

      if (error) {
        console.error('Database error fetching specialty options:', error)
        return ['Elderly Care', 'Child Care', 'Companionship', 'Medical Care']
      }

      return data?.map(item => item.name) || ['Elderly Care', 'Child Care', 'Companionship']
    } catch (error) {
      console.error('Error in getSpecialtyOptions:', error)
      return ['Elderly Care', 'Child Care', 'Companionship']
    }
  },

  async getCertificationOptions() {
    try {
      const { data, error } = await supabase
        .schema('care_connector')
        .from('certification_options')
        .select('*')
        .order('name')

      if (error) {
        console.error('Database error fetching certification options:', error)
        return ['CPR Certified', 'First Aid', 'Background Check']
      }

      return data?.map(item => item.name) || ['CPR Certified', 'First Aid', 'Background Check']
    } catch (error) {
      console.error('Error in getCertificationOptions:', error)
      return ['CPR Certified', 'First Aid', 'Background Check']
    }
  },

  async getLanguageOptions() {
    try {
      const { data, error } = await supabase
        .schema('care_connector')
        .from('language_options')
        .select('*')
        .order('name')

      if (error) {
        console.error('Database error fetching language options:', error)
        return ['English', 'Spanish']
      }

      return data?.map(item => item.name) || ['English', 'Spanish']
    } catch (error) {
      console.error('Error in getLanguageOptions:', error)
      return ['English', 'Spanish']
    }
  },

  async getProductCategories() {
    try {
      const { data, error } = await supabase
        .schema('care_connector')
        .from('product_categories')
        .select('*')
        .order('name')

      if (error) {
        console.error('Database error fetching product categories:', error)
        return [
          { id: 'all', name: 'All Products' },
          { id: 'medical-supplies', name: 'Medical Supplies' }
        ]
      }

      const categories = data?.map(item => ({ id: item.id, name: item.name })) || []
      return [{ id: 'all', name: 'All Products' }, ...categories]
    } catch (error) {
      console.error('Error in getProductCategories:', error)
      return [
        { id: 'all', name: 'All Products' },
        { id: 'medical-supplies', name: 'Medical Supplies' }
      ]
    }
  },

  // Booking filter options
  async getBookingStatusOptions() {
    try {
      const { data, error } = await supabase
        .schema('care_connector')
        .from('booking_status_options')
        .select('*')
        .order('display_order')

      if (error) {
        console.error('Database error fetching booking status options:', error)
        return [
          { value: 'all', label: 'All Statuses' },
          { value: 'pending', label: 'Pending' },
          { value: 'confirmed', label: 'Confirmed' }
        ]
      }

      const options = data?.map(item => ({ value: item.value, label: item.label })) || []
      return [{ value: 'all', label: 'All Statuses' }, ...options]
    } catch (error) {
      console.error('Error in getBookingStatusOptions:', error)
      return [
        { value: 'all', label: 'All Statuses' },
        { value: 'pending', label: 'Pending' },
        { value: 'confirmed', label: 'Confirmed' }
      ]
    }
  },

  async getTimeframeOptions() {
    try {
      const { data, error } = await supabase
        .schema('care_connector')
        .from('timeframe_options')
        .select('*')
        .order('display_order')

      if (error) {
        console.error('Database error fetching timeframe options:', error)
        return [
          { value: 'all', label: 'All Time' },
          { value: 'this_month', label: 'This Month' }
        ]
      }

      const options = data?.map(item => ({ value: item.value, label: item.label })) || []
      return [{ value: 'all', label: 'All Time' }, ...options]
    } catch (error) {
      console.error('Error in getTimeframeOptions:', error)
      return [
        { value: 'all', label: 'All Time' },
        { value: 'this_month', label: 'This Month' }
      ]
    }
  },

  async getNotificationFilterOptions() {
    try {
      const { data, error } = await supabase
        .schema('care_connector')
        .from('notification_filter_options')
        .select('*')
        .order('display_order')

      if (error) {
        console.error('Database error fetching notification filter options:', error)
        return [
          { value: 'all', label: 'All Notifications' },
          { value: 'unread', label: 'Unread' }
        ]
      }

      const options = data?.map(item => ({ value: item.value, label: item.label })) || []
      return [{ value: 'all', label: 'All Notifications' }, ...options]
    } catch (error) {
      console.error('Error in getNotificationFilterOptions:', error)
      return [
        { value: 'all', label: 'All Notifications' },
        { value: 'unread', label: 'Unread' }
      ]
    }
  },

  // How It Works steps
  async getHowItWorksSteps() {
    try {
      const { data, error } = await supabase
        .schema('care_connector')
        .from('how_it_works_steps')
        .select('*')
        .order('step_number')

      if (error) {
        console.error('Database error fetching how it works steps:', error)
        return []
      }

      return data || []
    } catch (error) {
      console.error('Error in getHowItWorksSteps:', error)
      return []
    }
  },

  // Features page data
  async getCoreFeatures() {
    try {
      const { data, error } = await supabase
        .schema('care_connector')
        .from('core_features')
        .select('*')
        .order('display_order')

      if (error) {
        console.error('Database error fetching core features:', error)
        return []
      }

      return data || []
    } catch (error) {
      console.error('Error in getCoreFeatures:', error)
      return []
    }
  },

  async getAdditionalFeatures() {
    try {
      const { data, error } = await supabase
        .schema('care_connector')
        .from('additional_features')
        .select('*')
        .order('display_order')

      if (error) {
        console.error('Database error fetching additional features:', error)
        return []
      }

      return data || []
    } catch (error) {
      console.error('Error in getAdditionalFeatures:', error)
      return []
    }
  },

  // Booking preference tabs
  async getBookingPreferenceTabs() {
    try {
      const { data, error } = await supabase
        .schema('care_connector')
        .from('booking_preference_tabs')
        .select('*')
        .order('display_order')

      if (error) {
        console.error('Database error fetching booking preference tabs:', error)
        return []
      }

      return data || []
    } catch (error) {
      console.error('Error in getBookingPreferenceTabs:', error)
      return []
    }
  },

  // Payment tabs
  async getPaymentTabs() {
    try {
      const { data, error } = await supabase
        .schema('care_connector')
        .from('payment_tabs')
        .select('*')
        .order('display_order')

      if (error) {
        console.error('Database error fetching payment tabs:', error)
        return []
      }

      return data || []
    } catch (error) {
      console.error('Error in getPaymentTabs:', error)
      return []
    }
  },

  // FAQ data for Contact page
  async getFaqData() {
    try {
      const { data, error } = await supabase
        .schema('care_connector')
        .from('faq_items')
        .select('*')
        .order('display_order')

      if (error) {
        console.error('Database error fetching FAQ data:', error)
        return []
      }

      return data || []
    } catch (error) {
      console.error('Error in getFaqData:', error)
      return []
    }
  },

  // Dashboard statistics - fetch real data from database tables
  async getDashboardStats(userId: string) {
    try {
      console.log('📊 Fetching dashboard stats for user:', userId)
      
      // Get upcoming appointments count
      const { count: upcomingAppointments } = await supabase
        .schema('care_connector')
        .from('bookings')
        .select('id', { count: 'exact', head: true })
        .eq('user_id', userId)
        .eq('status', 'confirmed')
        .gte('start_time', new Date().toISOString())

      // Get unread messages count - for now return 0 until we implement message system
      const unreadMessages = 0

      // Get saved providers count
      const { count: savedProviders } = await supabase
        .schema('care_connector')
        .from('user_service_provider_favorites')
        .select('id', { count: 'exact', head: true })
        .eq('user_id', userId)

      // Get care groups count where user is a member
      const { count: careGroupsCount } = await supabase
        .schema('care_connector')
        .from('care_group_members')
        .select('care_group_id', { count: 'exact', head: true })
        .eq('user_id', userId)

      // Get completed appointments count
      const { count: completedAppointments } = await supabase
        .schema('care_connector')
        .from('bookings')
        .select('id', { count: 'exact', head: true })
        .eq('user_id', userId)
        .eq('status', 'completed')

      const stats = {
        upcomingAppointments: upcomingAppointments || 0,
        unreadMessages,
        careGroupsCount: careGroupsCount || 0,
        savedProviders: savedProviders || 0,
        completedAppointments: completedAppointments || 0,
        activeConversations: 0, // To be implemented
        healthGoalsProgress: 0, // To be implemented
        medicationReminders: 0  // To be implemented
      }

      console.log('📊 Dashboard stats loaded:', stats)
      return stats

    } catch (error) {
      console.error('Error fetching dashboard stats:', error)
      // Return fallback data on error - complies with HOLY RULE #1
      return {
        upcomingAppointments: 0,
        unreadMessages: 0,
        careGroupsCount: 0,
        savedProviders: 0,
        completedAppointments: 0,
        activeConversations: 0,
        healthGoalsProgress: 0,
        medicationReminders: 0
      }
    }
  },

  // Recent activity for dashboard
  async getRecentActivity(userId: string) {
    try {
      console.log('📋 Fetching recent activity for user:', userId)
      
      const activities: any[] = []

      // Get recent bookings as activity items
      const { data: recentBookings } = await supabase
        .schema('care_connector')
        .from('bookings')
        .select(`
          id,
          created_at,
          status,
          appointment_date,
          appointment_time,
          profiles:provider_id (
            first_name,
            last_name,
            role
          )
        `)
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .limit(10)

      // Transform bookings into activity items
      if (recentBookings) {
        recentBookings.forEach(booking => {
          activities.push({
            id: `booking-${booking.id}`,
            type: 'booking',
            title: `Appointment ${booking.status}`,
            description: `${(booking.profiles as any)?.first_name || 'Unknown'} ${(booking.profiles as any)?.last_name || 'Provider'} - ${(booking.profiles as any)?.role || 'Healthcare Professional'}`,
            date: booking.created_at,
            icon: 'Calendar'
          })
        })
      }

      // Get recent care group memberships as activity items
      const { data: recentGroups } = await supabase
        .schema('care_connector')
        .from('care_group_members')
        .select(`
          id,
          joined_at,
          care_groups (
            name,
            description
          )
        `)
        .eq('user_id', userId)
        .order('joined_at', { ascending: false })
        .limit(5)

      // Transform care group memberships into activity items
      if (recentGroups) {
        recentGroups.forEach(membership => {
          activities.push({
            id: `group-${membership.id}`,
            type: 'care-group',
            title: 'Joined Care Group',
            description: (membership.care_groups as any)?.name || 'Care Group',
            date: membership.joined_at,
            icon: 'Heart'
          })
        })
      }

      // Sort all activities by date (newest first)
      activities.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())

      console.log('📋 Recent activity loaded:', activities.length, 'items')
      return activities

    } catch (error) {
      console.error('Error fetching recent activity:', error)
      return []
    }
  },

  // Get user's joined care groups
  async getUserCareGroups(userId: string) {
    try {
      console.log('📋 Fetching user care groups for:', userId)
      
      const { data: userGroups, error } = await supabase
        .schema('care_connector')
        .from('care_group_members')
        .select(`
          id,
          role,
          joined_at,
          care_groups (
            id,
            name,
            description,
            category,
            location,
            privacy_setting,
            member_count,
            created_at,
            avatar_url
          )
        `)
        .eq('user_id', userId)
        .order('joined_at', { ascending: false })

      if (error) {
        console.error('Error fetching user care groups:', error)
        throw error
      }

      // Transform membership data to care group format
      const groups = userGroups?.map(membership => ({
        id: (membership.care_groups as any)?.id,
        name: (membership.care_groups as any)?.name,
        description: (membership.care_groups as any)?.description,
        category: (membership.care_groups as any)?.category,
        location: (membership.care_groups as any)?.location,
        privacy_setting: (membership.care_groups as any)?.privacy_setting,
        member_count: (membership.care_groups as any)?.member_count,
        created_at: (membership.care_groups as any)?.created_at,
        avatar_url: (membership.care_groups as any)?.avatar_url,
        user_role: membership.role,
        joined_at: membership.joined_at
      })) || []

      console.log('📋 User care groups loaded:', groups.length, 'groups')
      return groups

    } catch (error) {
      console.error('Error in getUserCareGroups:', error)
      return []
    }
  },

  // Homepage Content Methods - NO HARDCODED FALLBACKS
  async getSearchContent() {
    try {
      const { data, error } = await supabase
        .schema('care_connector')
        .from('homepage_content')
        .select('*')
        .eq('section', 'search')
        .single()

      if (error) {
        console.error('Database error fetching search content:', error)
        return null
      }

      return data
    } catch (error) {
      console.error('Error in getSearchContent:', error)
      return null
    }
  },

  async getFeaturedProvidersContent() {
    try {
      const { data, error } = await supabase
        .schema('care_connector')
        .from('homepage_content')
        .select('*')
        .eq('section', 'featured_providers')
        .single()

      if (error) {
        console.error('Database error fetching featured providers content:', error)
        return null
      }

      return data
    } catch (error) {
      console.error('Error in getFeaturedProvidersContent:', error)
      return null
    }
  },

  async getProviderNetworkContent() {
    try {
      const { data, error } = await supabase
        .schema('care_connector')
        .from('homepage_content')
        .select('*')
        .eq('section', 'provider_network')
        .single()

      if (error) {
        console.error('Database error fetching provider network content:', error)
        return null
      }

      return data
    } catch (error) {
      console.error('Error in getProviderNetworkContent:', error)
      return null
    }
  },

  async getTakeControlContent() {
    try {
      const { data, error } = await supabase
        .schema('care_connector')
        .from('homepage_content')
        .select('*')
        .eq('section', 'take_control')
        .single()

      if (error) {
        console.error('Database error fetching take control content:', error)
        return null
      }

      return data
    } catch (error) {
      console.error('Error in getTakeControlContent:', error)
      return null
    }
  },

  async getFooterContent() {
    try {
      const { data, error } = await supabase
        .schema('care_connector')
        .from('homepage_content')
        .select('*')
        .eq('section', 'footer')
        .single()

      if (error) {
        console.error('Database error fetching footer content:', error)
        return null
      }

      return data
    } catch (error) {
      console.error('Error in getFooterContent:', error)
      return null
    }
  }
}
