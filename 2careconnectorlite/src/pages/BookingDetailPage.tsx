import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import { supabase } from '../lib/supabase';
import { Loader2, Calendar as CalendarIcon, UserCheck, MessageSquare, XCircle, Phone, CheckCircle2, Star, Lock } from 'lucide-react';
import { format, parseISO } from 'date-fns';

interface Booking {
  id: string;
  user_id: string;
  service_provider_id: string;
  service_details: string;
  booking_start_time: string;
  booking_end_time: string;
  status: string;
  total_cost: number;
  notes?: string;
  cancellation_reason?: string;
  duration_hours: string;
  payment_status: string;
  created_at: string;
  updated_at: string;
}

const BookingDetailPage: React.FC = () => {
  const { id: bookingId } = useParams<{ id: string }>();
  const navigate = useNavigate();
  
  const [booking, setBooking] = useState<Booking | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [cancellationReason, setCancellationReason] = useState('');
  const [cancelling, setCancelling] = useState(false);

  useEffect(() => {
    const fetchBooking = async () => {
      if (!bookingId) {
        setError("Booking ID is missing.");
        setLoading(false);
        return;
      }

      try {
        const { data, error: fetchError } = await supabase
          .schema('care_connector')
          .from('service_provider_bookings')
          .select('*')
          .eq('id', bookingId)
          .single();

        if (fetchError) {
          console.error('Supabase fetch error:', fetchError);
          throw fetchError;
        }

        if (!data) {
          throw new Error("Booking not found.");
        }

        console.log('Booking data fetched successfully:', data);
        setBooking(data);
      } catch (err: any) {
        console.error("Error fetching booking details:", err.message);
        setError(err.message || "Failed to load booking details.");
        setBooking(null);
      } finally {
        setLoading(false);
      }
    };

    fetchBooking();
  }, [bookingId]);

  const handleCancelBooking = async () => {
    if (!booking || !cancellationReason.trim()) {
      setError('Please provide a reason for cancellation.');
      return;
    }

    setCancelling(true);
    try {
      const { error } = await supabase
        .schema('care_connector')
        .from('service_provider_bookings')
        .update({ 
          status: 'cancelled_by_user',
          cancellation_reason: cancellationReason
        })
        .eq('id', booking.id);

      if (error) {
        throw error;
      }

      // Update local state
      setBooking({
        ...booking,
        status: 'cancelled_by_user',
        cancellation_reason: cancellationReason
      });

      setError('Booking cancelled successfully.');
    } catch (err: any) {
      console.error('Error cancelling booking:', err);
      setError('Failed to cancel booking. Please try again.');
    } finally {
      setCancelling(false);
    }
  };

  const getProviderProfileLink = (booking: Booking) => {
    return `/provider/caregiver/${booking.service_provider_id}`;
  };

  const generateGoogleCalendarLink = () => {
    if (!booking) return '';
    
    const startDate = format(parseISO(booking.booking_start_time), "yyyyMMdd'T'HHmmss");
    const endDate = format(parseISO(booking.booking_end_time), "yyyyMMdd'T'HHmmss");
    const title = encodeURIComponent(`${booking.service_details} with Care Provider`);
    const details = encodeURIComponent(`Booking ID: ${booking.id}\nService: ${booking.service_details}\nDuration: ${booking.duration_hours} hours`);
    
    return `https://calendar.google.com/calendar/render?action=TEMPLATE&text=${title}&dates=${startDate}/${endDate}&details=${details}`;
  };

  const getStatusBadgeStyle = (status: string) => {
    switch (status) {
      case 'confirmed':
        return { backgroundColor: 'var(--primary)', color: 'var(--bg-primary)' };
      case 'pending':
        return { backgroundColor: 'var(--warning)', color: 'var(--text-primary)' };
      case 'cancelled_by_user':
      case 'cancelled_by_provider':
        return { backgroundColor: 'var(--error)', color: 'var(--bg-primary)' };
      case 'completed':
        return { backgroundColor: 'var(--secondary)', color: 'var(--bg-primary)' };
      default:
        return { backgroundColor: 'var(--bg-secondary)', color: 'var(--text-primary)' };
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'confirmed':
        return 'Confirmed';
      case 'pending':
        return 'Pending';
      case 'cancelled_by_user':
        return 'Cancelled by You';
      case 'cancelled_by_provider':
        return 'Cancelled by Provider';
      case 'completed':
        return 'Completed';
      default:
        return status;
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center" style={{ backgroundColor: 'var(--bg-primary)' }}>
        <div className="flex items-center space-x-3">
          <Loader2 className="h-6 w-6 animate-spin text-var(--logo-green)" />
          <span className="text-gray-600 font-medium">Loading booking details...</span>
        </div>
      </div>
    );
  }

  if (error) {
    // Check if this is an authentication error
    const isAuthError = error.includes('sign in') || error.includes('Please sign in');
    
    if (isAuthError) {
      return (
        <div className="auth-error-container">
          <div className="auth-error-content">
            <div className="text-center">
              <Lock className="h-12 w-12 text-red-500 mx-auto mb-4" />
              <h2 className="text-xl font-semibold text-gray-900 mb-2">Access Restricted</h2>
              <p className="text-gray-600 mb-6">Please sign in to view booking details and access this feature.</p>
              <div className="space-y-3">
                <button
                  onClick={() => navigate('/auth')}
                  className="auth-error-primary-button w-full"
                >
                  Sign In
                </button>
                <button
                  onClick={() => navigate('/dashboard')}
                  className="auth-error-secondary-button w-full"
                >
                  Back to Dashboard
                </button>
              </div>
            </div>
          </div>
        </div>
      );
    }
    
    // General error state for non-authentication errors
    return (
      <div className="min-h-screen flex items-center justify-center" style={{ backgroundColor: 'var(--bg-secondary)' }}>
        <div className="rounded-2xl shadow-sm border p-8 max-w-md w-full mx-4" style={{ backgroundColor: 'var(--bg-primary)', borderColor: 'var(--border-light)' }}>
          <div className="text-center">
            <XCircle className="h-12 w-12 mx-auto mb-4" style={{ color: 'var(--error)' }} />
            <h2 className="text-xl font-semibold mb-2" style={{ color: 'var(--text-primary)' }}>Error Loading Booking</h2>
            <p className="mb-6" style={{ color: 'var(--text-secondary)' }}>{error}</p>
            <button
              onClick={() => navigate('/dashboard')}
              className="px-6 py-3 rounded-xl font-medium transition-colors"
              style={{ backgroundColor: 'var(--primary)', color: 'var(--bg-primary)' }}
              onMouseEnter={(e) => e.currentTarget.style.opacity = '0.9'}
              onMouseLeave={(e) => e.currentTarget.style.opacity = '1'}
            >
              Back to Dashboard
            </button>
          </div>
        </div>
      </div>
    );
  }

  if (!booking) {
    return (
      <div className="min-h-screen flex items-center justify-center" style={{ backgroundColor: 'var(--bg-primary)' }}>
        <div className="rounded-2xl shadow-sm p-8 max-w-md w-full mx-4" style={{ backgroundColor: 'var(--bg-primary)', borderColor: 'var(--border-light)' }}>
          <div className="text-center">
            <CalendarIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h2 className="text-xl font-semibold text-gray-900 mb-2">Booking Not Found</h2>
            <p className="text-gray-600 mb-6">The booking you are looking for could not be found.</p>
            <button
              onClick={() => navigate('/dashboard')}
              className="px-6 py-3 rounded-xl font-medium transition-colors"
              style={{ backgroundColor: 'var(--primary)', color: 'var(--bg-primary)' }}
              onMouseEnter={(e) => e.currentTarget.style.backgroundColor = 'var(--primary)'}
              onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'var(--primary)'}
            >
              Back to Dashboard
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen" style={{ backgroundColor: 'var(--bg-secondary)' }}>
      {/* Header */}
      <div className="border-b" style={{ backgroundColor: 'var(--bg-primary)', borderColor: 'var(--border-light)' }}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <div>
              <button
                onClick={() => navigate(-1)}
                className="mb-2 font-medium transition-colors"
                style={{ color: 'var(--text-secondary)' }}
                onMouseEnter={(e) => e.currentTarget.style.color = 'var(--text-primary)'}
                onMouseLeave={(e) => e.currentTarget.style.color = 'var(--text-secondary)'}
              >
                ← Back
              </button>
              <h1 className="text-2xl font-bold" style={{ color: 'var(--text-primary)' }}>Booking Details</h1>
              <p style={{ color: 'var(--text-secondary)' }}>Booking ID: {booking.id}</p>
            </div>
            <div className="px-3 py-1 rounded-full text-sm font-medium" style={getStatusBadgeStyle(booking.status)}>
              {getStatusLabel(booking.status)}
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          
          {/* Left Column - Main Content */}
          <div className="lg:col-span-2 space-y-6">
            
            {/* Booking Information Card */}
            <div className="rounded-2xl shadow-sm p-6" style={{ backgroundColor: 'var(--bg-primary)', borderColor: 'var(--border-light)' }}>
              <h2 className="text-xl font-semibold text-gray-900 mb-6">Booking Information</h2>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-500 mb-1">Service Details</label>
                  <p className="text-gray-900 font-medium">{booking.service_details}</p>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-500 mb-1">Duration</label>
                  <p className="text-gray-900 font-medium">{booking.duration_hours} hours</p>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-500 mb-1">Date & Time</label>
                  <p className="text-gray-900 font-medium">
                    {format(parseISO(booking.booking_start_time), 'MMMM d, yyyy')}
                  </p>
                  <p className="text-gray-600 text-sm">
                    {format(parseISO(booking.booking_start_time), 'h:mm a')} - {format(parseISO(booking.booking_end_time), 'h:mm a')}
                  </p>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-500 mb-1">Total Cost</label>
                  <p className="text-gray-900 font-medium">${booking.total_cost}</p>
                </div>
                
                {booking.notes && (
                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-gray-500 mb-1">Notes</label>
                    <p className="text-gray-900">{booking.notes}</p>
                  </div>
                )}
                
                {booking.cancellation_reason && (
                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-gray-500 mb-1">Cancellation Reason</label>
                    <p className="text-gray-900">{booking.cancellation_reason}</p>
                  </div>
                )}
              </div>
            </div>

            {/* Action Buttons */}
            <div className="rounded-2xl shadow-sm p-6" style={{ backgroundColor: 'var(--bg-primary)', borderColor: 'var(--border-light)' }}>
              <h2 className="text-xl font-semibold text-gray-900 mb-4">Actions</h2>
              
              <div className="flex flex-col sm:flex-row gap-4">
                {booking.status === 'confirmed' && (
                  <>
                    <a
                      href={generateGoogleCalendarLink()}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center justify-center px-6 py-3 rounded-xl font-medium transition-colors"
                      style={{ backgroundColor: 'var(--secondary)', color: 'var(--bg-primary)' }}
                      onMouseEnter={(e) => e.currentTarget.style.backgroundColor = 'var(--secondary-dark)'}
                      onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'var(--secondary)'}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      Add to Calendar
                    </a>
                    
                    <Link
                      to={`/messages/new?recipientId=${booking.service_provider_id}`}
                      className="flex items-center justify-center px-6 py-3 rounded-xl font-medium transition-colors"
                      style={{ backgroundColor: 'var(--primary)', color: 'var(--bg-primary)' }}
                      onMouseEnter={(e) => e.currentTarget.style.backgroundColor = 'var(--primary)'}
                      onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'var(--primary)'}
                    >
                      <MessageSquare className="mr-2 h-4 w-4" />
                      Message Provider
                    </Link>
                  </>
                )}
                
                {booking.status === 'confirmed' && (
                  <div className="border-t pt-4 mt-4">
                    <h3 className="font-medium text-gray-900 mb-2">Cancel Booking</h3>
                    <textarea
                      placeholder="Please provide a reason for cancellation..."
                      value={cancellationReason}
                      onChange={(e) => setCancellationReason(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg mb-3 min-h-[80px]"
                    />
                    <button
                      onClick={handleCancelBooking}
                      disabled={!cancellationReason.trim() || cancelling}
                      className="flex items-center justify-center px-6 py-3 rounded-xl font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                      style={{ backgroundColor: 'var(--error)', color: 'var(--bg-primary)' }}
                      onMouseEnter={(e) => !e.currentTarget.disabled && (e.currentTarget.style.backgroundColor = 'var(--error-dark)')}
                      onMouseLeave={(e) => !e.currentTarget.disabled && (e.currentTarget.style.backgroundColor = 'var(--error)')}
                    >
                      {cancelling ? (
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      ) : (
                        <XCircle className="mr-2 h-4 w-4" />
                      )}
                      {cancelling ? 'Cancelling...' : 'Cancel Booking'}
                    </button>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Right Column - Provider Information */}
          <div className="space-y-6">
            <div className="rounded-2xl shadow-sm p-6" style={{ backgroundColor: 'var(--bg-primary)', borderColor: 'var(--border-light)' }}>
              <h2 className="text-xl font-semibold text-gray-900 mb-4">Service Provider</h2>
              
              <div className="flex items-center space-x-4 mb-6">
                <div className="h-16 w-16 rounded-full bg-var(--logo-green) bg-opacity-10 flex items-center justify-center">
                  <UserCheck className="h-8 w-8 text-var(--logo-green)" />
                </div>
                <div className="flex-1">
                  <Link 
                    to={getProviderProfileLink(booking)} 
                    className="font-bold text-lg hover:text-var(--logo-green) transition-colors text-gray-900"
                  >
                    Care Provider
                  </Link>
                  <p className="text-sm text-gray-600 capitalize">Healthcare Professional</p>
                </div>
              </div>
              
              <div className="space-y-3">
                <a
                  href="tel:******-0123"
                  className="flex items-center justify-center w-full px-4 py-3 border border-gray-300 rounded-xl font-medium hover:shadow-sm transition-colors"
                  style={{ backgroundColor: 'var(--bg-primary)' }}
                  onMouseEnter={(e) => { e.currentTarget.style.borderColor = 'var(--primary)'; e.currentTarget.style.boxShadow = 'var(--shadow-card)' }}
                  onMouseLeave={(e) => { e.currentTarget.style.borderColor = '#d1d5db'; e.currentTarget.style.boxShadow = 'none' }}
                >
                  <Phone className="mr-2 h-4 w-4" />
                  Contact Provider
                </a>
                
                <Link
                  to={`/messages/new?recipientId=${booking.service_provider_id}&providerName=${encodeURIComponent('Care Provider')}`}
                  className="flex items-center justify-center w-full px-4 py-3 rounded-xl font-medium transition-colors"
                  style={{ backgroundColor: 'var(--primary)', color: 'var(--bg-primary)' }}
                  onMouseEnter={(e) => e.currentTarget.style.backgroundColor = 'var(--primary)'}
                  onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'var(--primary)'}
                >
                  <MessageSquare className="mr-2 h-4 w-4" />
                  Message Provider
                </Link>
              </div>
            </div>
            
            {/* Booking Timeline */}
            <div className="rounded-2xl shadow-sm p-6" style={{ backgroundColor: 'var(--bg-primary)', borderColor: 'var(--border-light)' }}>
              <h2 className="text-xl font-semibold text-gray-900 mb-4">Booking Timeline</h2>
              
              <div className="space-y-4">
                <div className="flex items-center space-x-3">
                  <CheckCircle2 className="h-5 w-5 text-var(--logo-green)" />
                  <div>
                    <p className="font-medium text-gray-900">Booking Created</p>
                    <p className="text-sm text-gray-600">{format(parseISO(booking.created_at), 'MMM d, yyyy h:mm a')}</p>
                  </div>
                </div>
                
                {booking.status === 'confirmed' && (
                  <div className="flex items-center space-x-3">
                    <CheckCircle2 className="h-5 w-5 text-var(--logo-green)" />
                    <div>
                      <p className="font-medium text-gray-900">Booking Confirmed</p>
                      <p className="text-sm text-gray-600">Ready for service</p>
                    </div>
                  </div>
                )}
                
                {(booking.status === 'cancelled_by_user' || booking.status === 'cancelled_by_provider') && (
                  <div className="flex items-center space-x-3">
                    <XCircle className="h-5 w-5 text-red-500" />
                    <div>
                      <p className="font-medium text-gray-900">Booking Cancelled</p>
                      <p className="text-sm text-gray-600">
                        {booking.status === 'cancelled_by_user' ? 'Cancelled by you' : 'Cancelled by provider'}
                      </p>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BookingDetailPage;
