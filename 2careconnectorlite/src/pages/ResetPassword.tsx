import React, { useState, useEffect } from 'react'
import { Link, useNavigate, useSearchParams } from 'react-router-dom'
import { Eye, EyeOff, Loader2, CheckCircle, AlertCircle } from 'lucide-react'
import { supabase } from '../lib/supabase'

export default function ResetPassword() {
  const [password, setPassword] = useState('')
  const [confirmPassword, setConfirmPassword] = useState('')
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState(false)
  const [validationErrors, setValidationErrors] = useState<{[key: string]: string}>({})
  const [searchParams] = useSearchParams()
  const navigate = useNavigate()

  useEffect(() => {
    // Check if we have the required tokens from the URL
    const accessToken = searchParams.get('access_token')
    const refreshToken = searchParams.get('refresh_token')
    
    if (!accessToken || !refreshToken) {
      setError('Invalid or expired reset link. Please request a new password reset.')
    }
  }, [searchParams])

  const validateForm = () => {
    const errors: {[key: string]: string} = {}

    if (!password.trim()) {
      errors.password = 'Password is required'
    } else if (password.length < 8) {
      errors.password = 'Password must be at least 8 characters'
    } else if (!/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])/.test(password)) {
      errors.password = 'Password must contain uppercase, lowercase, number, and special character'
    }

    if (!confirmPassword.trim()) {
      errors.confirmPassword = 'Please confirm your password'
    } else if (password !== confirmPassword) {
      errors.confirmPassword = 'Passwords do not match'
    }

    setValidationErrors(errors)
    return Object.keys(errors).length === 0
  }

  const handlePasswordReset = async (e: React.FormEvent) => {
    e.preventDefault()
    console.log('Password reset form submitted')

    if (!validateForm()) {
      return
    }

    const accessToken = searchParams.get('access_token')
    const refreshToken = searchParams.get('refresh_token')

    if (!accessToken || !refreshToken) {
      setError('Invalid or expired reset link. Please request a new password reset.')
      return
    }

    setLoading(true)
    setError(null)
    setValidationErrors({})

    try {
      console.log('🔐 Attempting password update...')
      
      // Set the session with the tokens from the URL
      const { error: sessionError } = await supabase.auth.setSession({
        access_token: accessToken,
        refresh_token: refreshToken
      })

      if (sessionError) {
        console.error('🔐 Session error:', sessionError)
        throw new Error('Invalid or expired reset link. Please request a new password reset.')
      }

      // Update the password
      const { error: updateError } = await supabase.auth.updateUser({
        password: password
      })

      if (updateError) {
        console.error('🔐 Password update failed:', updateError)
        throw updateError
      }

      console.log('🔐 Password updated successfully')
      setSuccess(true)

      // Redirect to sign in after 3 seconds
      setTimeout(() => {
        navigate('/sign-in')
      }, 3000)

    } catch (error) {
      console.error('🔐 Password reset error details:', error)
      const errorMessage = error instanceof Error ? error.message : 'An error occurred while resetting password'
      console.error('🔐 Setting error message:', errorMessage)
      setError(errorMessage)
    } finally {
      setLoading(false)
    }
  }

  if (success) {
    return (
      <div className="min-h-screen flex items-center justify-center px-4 sm:px-6 lg:px-8" style={{
        background: 'linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%)'
      }}>
        <div className="max-w-md w-full space-y-8">
          <div className="text-center">
            <CheckCircle className="mx-auto h-16 w-16 mb-6" style={{ color: 'var(--success)' }} />
            <h2 className="text-3xl font-semibold mb-4 macos-title" style={{
              color: 'var(--text-primary)',
              fontWeight: '600',
              letterSpacing: '-0.01em'
            }}>
              Password Reset Successful
            </h2>
            <p className="text-lg macos-body mb-6" style={{
              color: 'var(--text-secondary)',
              fontWeight: '400',
              letterSpacing: '-0.005em'
            }}>
              Your password has been successfully updated. You can now sign in with your new password.
            </p>
            <p className="text-base macos-body mb-8" style={{
              color: 'var(--text-secondary)',
              fontWeight: '400'
            }}>
              Redirecting to sign in page in 3 seconds...
            </p>
            <Link
              to="/sign-in"
              className="w-full flex justify-center py-3 px-4 border border-transparent rounded-xl text-base font-medium text-white transition-all duration-200 macos-body"
              style={{
                backgroundColor: 'var(--primary)',
                boxShadow: 'var(--shadow-medium)'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.opacity = '0.9'
                e.currentTarget.style.transform = 'translateY(-1px)'
                e.currentTarget.style.boxShadow = 'var(--shadow-large)'
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.opacity = '1'
                e.currentTarget.style.transform = 'translateY(0)'
                e.currentTarget.style.boxShadow = 'var(--shadow-medium)'
              }}
            >
              Sign In Now
            </Link>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen flex items-center justify-center px-4 sm:px-6 lg:px-8" style={{
      background: 'linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%)'
    }}>
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <AlertCircle className="mx-auto h-16 w-16 mb-6" style={{ color: 'var(--primary)' }} />
          
          <h2 className="text-3xl font-semibold mb-4 macos-title" style={{
            color: 'var(--text-primary)',
            fontWeight: '600',
            letterSpacing: '-0.01em'
          }}>
            Set New Password
          </h2>
          <p className="text-lg macos-body" style={{
            color: 'var(--text-secondary)',
            fontWeight: '400',
            letterSpacing: '-0.005em'
          }}>
            Enter your new password below
          </p>
        </div>

        <div
          className="rounded-2xl p-8 shadow-2xl"
          style={{
            backgroundColor: 'var(--bg-primary)',
            backdropFilter: 'blur(10px)',
            border: '1px solid var(--border-light)'
          }}
        >
          <form className="space-y-6" onSubmit={handlePasswordReset} noValidate>
            {error && (
              <div
                className="rounded-xl p-4 border"
                style={{
                  backgroundColor: 'var(--bg-error)',
                  borderColor: 'var(--border-error)'
                }}
              >
                <p className="text-sm" style={{ color: 'var(--text-error)' }}>{error}</p>
              </div>
            )}
            
            <div>
              <label htmlFor="password" className="block text-base font-medium mb-3 macos-body" style={{
                color: 'var(--text-primary)',
                fontWeight: '500',
                letterSpacing: '-0.005em'
              }}>
                New Password
              </label>
              <div className="relative">
                <input
                  id="password"
                  name="password"
                  type={showPassword ? 'text' : 'password'}
                  autoComplete="new-password"
                  required
                  value={password}
                  onChange={(e) => {
                    setPassword(e.target.value)
                    if (validationErrors.password) {
                      setValidationErrors(prev => ({...prev, password: ''}))
                    }
                  }}
                  className="appearance-none block w-full px-4 py-4 pr-12 border rounded-xl focus:outline-none transition-all duration-300 text-base macos-body"
                  style={{
                    backgroundColor: 'var(--bg-secondary)',
                    borderColor: 'var(--border-light)',
                    color: 'var(--text-primary)'
                  }}
                  onFocus={(e) => {
                    e.currentTarget.style.borderColor = 'var(--primary)'
                    e.currentTarget.style.boxShadow = 'var(--focus-shadow)'
                  }}
                  onBlur={(e) => {
                    e.currentTarget.style.borderColor = 'var(--border-light)'
                    e.currentTarget.style.boxShadow = 'none'
                  }}
                  placeholder="Enter new password"
                  aria-describedby={validationErrors.password ? "password-error" : undefined}
                  aria-invalid={!!validationErrors.password}
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-4 top-1/2 transform -translate-y-1/2 transition-all duration-200"
                  style={{ 
                    color: 'var(--text-muted)',
                    borderRadius: '4px',
                    padding: '4px'
                  }}
                  aria-label={showPassword ? 'Hide password' : 'Show password'}
                >
                  {showPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                </button>
              </div>
              {validationErrors.password && (
                <p id="password-error" className="mt-1 text-sm" style={{ color: 'var(--text-error)' }} role="alert">
                  {validationErrors.password}
                </p>
              )}
            </div>

            <div>
              <label htmlFor="confirmPassword" className="block text-base font-medium mb-3 macos-body" style={{
                color: 'var(--text-primary)',
                fontWeight: '500',
                letterSpacing: '-0.005em'
              }}>
                Confirm New Password
              </label>
              <div className="relative">
                <input
                  id="confirmPassword"
                  name="confirmPassword"
                  type={showConfirmPassword ? 'text' : 'password'}
                  autoComplete="new-password"
                  required
                  value={confirmPassword}
                  onChange={(e) => {
                    setConfirmPassword(e.target.value)
                    if (validationErrors.confirmPassword) {
                      setValidationErrors(prev => ({...prev, confirmPassword: ''}))
                    }
                  }}
                  className="appearance-none block w-full px-4 py-4 pr-12 border rounded-xl focus:outline-none transition-all duration-300 text-base macos-body"
                  style={{
                    backgroundColor: 'var(--bg-secondary)',
                    borderColor: 'var(--border-light)',
                    color: 'var(--text-primary)'
                  }}
                  onFocus={(e) => {
                    e.currentTarget.style.borderColor = 'var(--primary)'
                    e.currentTarget.style.boxShadow = 'var(--focus-shadow)'
                  }}
                  onBlur={(e) => {
                    e.currentTarget.style.borderColor = 'var(--border-light)'
                    e.currentTarget.style.boxShadow = 'none'
                  }}
                  placeholder="Confirm new password"
                  aria-describedby={validationErrors.confirmPassword ? "confirm-password-error" : undefined}
                  aria-invalid={!!validationErrors.confirmPassword}
                />
                <button
                  type="button"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  className="absolute right-4 top-1/2 transform -translate-y-1/2 transition-all duration-200"
                  style={{ 
                    color: 'var(--text-muted)',
                    borderRadius: '4px',
                    padding: '4px'
                  }}
                  aria-label={showConfirmPassword ? 'Hide password' : 'Show password'}
                >
                  {showConfirmPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                </button>
              </div>
              {validationErrors.confirmPassword && (
                <p id="confirm-password-error" className="mt-1 text-sm" style={{ color: 'var(--text-error)' }} role="alert">
                  {validationErrors.confirmPassword}
                </p>
              )}
            </div>

            <div>
              <button
                type="submit"
                disabled={loading}
                className="group relative w-full flex justify-center py-4 px-4 border border-transparent text-base font-medium rounded-xl text-white transition-all duration-200 macos-body"
                style={{
                  backgroundColor: loading ? 'var(--bg-muted)' : 'var(--primary)',
                  boxShadow: loading ? 'none' : 'var(--shadow-medium)',
                  cursor: loading ? 'not-allowed' : 'pointer'
                }}
                onMouseEnter={(e) => {
                  if (!loading) {
                    e.currentTarget.style.opacity = '0.9'
                    e.currentTarget.style.transform = 'translateY(-1px)'
                    e.currentTarget.style.boxShadow = 'var(--shadow-large)'
                  }
                }}
                onMouseLeave={(e) => {
                  if (!loading) {
                    e.currentTarget.style.opacity = '1'
                    e.currentTarget.style.transform = 'translateY(0)'
                    e.currentTarget.style.boxShadow = 'var(--shadow-medium)'
                  }
                }}
              >
                {loading ? (
                  <>
                    <Loader2 className="w-5 h-5 mr-2 animate-spin" />
                    Updating Password...
                  </>
                ) : (
                  'Update Password'
                )}
              </button>
            </div>

            <div className="text-center">
              <Link
                to="/sign-in"
                className="text-sm font-medium transition-all duration-200 macos-body"
                style={{
                  color: 'var(--primary)',
                  textDecoration: 'none'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.opacity = '0.8'
                  e.currentTarget.style.textDecoration = 'underline'
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.opacity = '1'
                  e.currentTarget.style.textDecoration = 'none'
                }}
              >
                Back to Sign In
              </Link>
            </div>
          </form>
        </div>
      </div>
    </div>
  )
}
