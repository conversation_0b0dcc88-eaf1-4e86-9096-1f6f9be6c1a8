import React, { useState } from 'react'
import { <PERSON> } from 'react-router-dom'
import { ArrowLeft, Mail, Loader2, CheckCircle } from 'lucide-react'
import { supabase } from '../lib/supabase'

export default function ForgotPassword() {
  const [email, setEmail] = useState('')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState(false)
  const [validationErrors, setValidationErrors] = useState<{[key: string]: string}>({})

  const validateForm = () => {
    const errors: {[key: string]: string} = {}

    if (!email.trim()) {
      errors.email = 'Email is required'
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
      errors.email = 'Please enter a valid email address'
    }

    setValidationErrors(errors)
    return Object.keys(errors).length === 0
  }

  const handlePasswordReset = async (e: React.FormEvent) => {
    e.preventDefault()
    console.log('Password reset form submitted with:', { email })

    if (!validateForm()) {
      return
    }

    setLoading(true)
    setError(null)
    setValidationErrors({})

    try {
      console.log('🔐 Attempting password reset for email:', email)
      
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/reset-password`,
      })

      if (error) {
        console.error('🔐 Password reset failed:', error)
        throw error
      }

      console.log('🔐 Password reset email sent successfully')
      setSuccess(true)

    } catch (error) {
      console.error('🔐 Password reset error details:', error)
      const errorMessage = error instanceof Error ? error.message : 'An error occurred while sending reset email'
      console.error('🔐 Setting error message:', errorMessage)
      setError(errorMessage)
    } finally {
      setLoading(false)
    }
  }

  if (success) {
    return (
      <div className="min-h-screen flex items-center justify-center px-4 sm:px-6 lg:px-8" style={{
        backgroundColor: 'var(--bg-secondary)'
      }}>
        <div className="max-w-md w-full space-y-8">
          <div className="text-center">
            <CheckCircle className="mx-auto h-16 w-16 mb-6" style={{ color: 'var(--success)' }} />
            <h2 className="text-3xl font-semibold mb-4 macos-title" style={{
              color: 'var(--text-primary)',
              fontWeight: '600',
              letterSpacing: '-0.01em'
            }}>
              Check Your Email
            </h2>
            <p className="text-lg macos-body mb-6" style={{
              color: 'var(--text-secondary)',
              fontWeight: '400',
              letterSpacing: '-0.005em'
            }}>
              We've sent a password reset link to <strong style={{ color: 'var(--text-primary)' }}>{email}</strong>
            </p>
            <p className="text-base macos-body mb-8" style={{
              color: 'var(--text-secondary)',
              fontWeight: '400'
            }}>
              Please check your email and click the link to reset your password. The link will expire in 1 hour.
            </p>
            <div className="space-y-4">
              <Link
                to="/sign-in"
                className="w-full flex justify-center py-3 px-4 rounded-xl text-base font-medium transition-all duration-200"
                style={{
                  backgroundColor: 'var(--primary)',
                  color: 'var(--bg-primary)',
                  boxShadow: 'var(--shadow-medium)'
                }}
              >
                Back to Sign In
              </Link>
              <button
                onClick={() => {
                  setSuccess(false)
                  setEmail('')
                  setError(null)
                }}
                className="w-full flex justify-center py-3 px-4 rounded-xl text-base font-medium transition-all duration-200"
                style={{
                  backgroundColor: 'var(--bg-secondary)',
                  color: 'var(--text-primary)',
                  border: '1px solid var(--border-light)'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.backgroundColor = 'var(--bg-primary)'
                  e.currentTarget.style.borderColor = 'var(--border-medium)'
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = 'var(--bg-secondary)'
                  e.currentTarget.style.borderColor = 'var(--border-light)'
                }}
              >
                Send Another Email
              </button>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen flex items-center justify-center px-4 sm:px-6 lg:px-8" style={{
      background: 'linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%)'
    }}>
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <Link
            to="/sign-in"
            className="inline-flex items-center text-sm font-medium mb-6 transition-all duration-200 macos-body"
            style={{
              color: 'var(--primary)',
              textDecoration: 'none'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.opacity = '0.8'
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.opacity = '1'
            }}
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Sign In
          </Link>
          
          <Mail className="mx-auto h-16 w-16 mb-6" style={{ color: 'var(--primary)' }} />
          
          <h2 className="text-3xl font-semibold mb-4 macos-title" style={{
            color: 'var(--text-primary)',
            fontWeight: '600',
            letterSpacing: '-0.01em'
          }}>
            Reset Your Password
          </h2>
          <p className="text-lg macos-body" style={{
            color: 'var(--text-secondary)',
            fontWeight: '400',
            letterSpacing: '-0.005em'
          }}>
            Enter your email address and we'll send you a link to reset your password
          </p>
        </div>

        <div
          className="rounded-2xl p-8 shadow-2xl"
          style={{
            backgroundColor: 'var(--bg-primary)',
            backdropFilter: 'blur(10px)',
            border: '1px solid var(--border-light)'
          }}
        >
          <form className="space-y-6" onSubmit={handlePasswordReset} noValidate>
            {error && (
              <div
                className="rounded-xl p-4 border"
                style={{
                  backgroundColor: 'var(--bg-error)',
                  borderColor: 'var(--border-error)'
                }}
              >
                <p className="text-sm" style={{ color: 'var(--text-error)' }}>{error}</p>
              </div>
            )}
            
            <div>
              <label htmlFor="email" className="block text-base font-medium mb-3 macos-body" style={{
                color: 'var(--text-primary)',
                fontWeight: '500',
                letterSpacing: '-0.005em'
              }}>
                Email Address
              </label>
              <div>
                <input
                  id="email"
                  name="email"
                  type="email"
                  autoComplete="email"
                  required
                  value={email}
                  onChange={(e) => {
                    setEmail(e.target.value)
                    if (validationErrors.email) {
                      setValidationErrors(prev => ({...prev, email: ''}))
                    }
                  }}
                  className="appearance-none block w-full px-4 py-4 border rounded-xl focus:outline-none transition-all duration-200 text-base macos-body"
                  style={{
                    backgroundColor: 'var(--bg-secondary)',
                    borderColor: 'var(--border-light)',
                    color: 'var(--text-primary)'
                  }}
                  onFocus={(e) => {
                    e.currentTarget.style.borderColor = 'var(--primary)'
                    e.currentTarget.style.boxShadow = 'var(--focus-shadow)'
                  }}
                  onBlur={(e) => {
                    e.currentTarget.style.borderColor = 'var(--border-light)'
                    e.currentTarget.style.boxShadow = 'none'
                  }}
                  placeholder="Enter your email address"
                  aria-describedby={validationErrors.email ? "email-error" : undefined}
                  aria-invalid={!!validationErrors.email}
                />
              </div>
              {validationErrors.email && (
                <p id="email-error" className="mt-1 text-sm" style={{ color: 'var(--text-error)' }} role="alert">
                  {validationErrors.email}
                </p>
              )}
            </div>

            <div>
              <button
                type="submit"
                disabled={loading}
                className="w-full flex justify-center py-4 px-4 rounded-xl text-base font-medium transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                style={{
                  backgroundColor: 'var(--primary)',
                  color: 'var(--bg-primary)',
                  boxShadow: loading ? 'none' : 'var(--shadow-medium)'
                }}
              >
                {loading ? (
                  <>
                    <Loader2 className="w-5 h-5 mr-2 animate-spin" />
                    Sending Reset Link...
                  </>
                ) : (
                  'Send Reset Link'
                )}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  )
}
