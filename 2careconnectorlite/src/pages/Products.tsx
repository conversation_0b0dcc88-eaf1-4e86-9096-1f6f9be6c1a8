import { useState, useEffect } from 'react'
import { Link } from 'react-router-dom'
import { Search, Filter, Star, ShoppingCart, Heart, ArrowLeft, Package, Truck, Shield } from 'lucide-react'
import { dataService } from '../lib/dataService'

interface Product {
  id: string
  name: string
  description: string
  price: number
  category: string
  rating: number
  reviews: number
  image_url?: string
  in_stock: boolean
  seller: string
  shipping_info: string
}

export default function Products() {
  const [products, setProducts] = useState<Product[]>([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('all')
  const [sortBy, setSortBy] = useState('featured')
  const [priceRange, setPriceRange] = useState([0, 1000])

  // Real database query for products - NO HARDCODED DATA
  useEffect(() => {
    fetchProducts()
  }, [])

  const fetchProducts = async () => {
    try {
      setLoading(true)
      // In a real implementation, this would query the products table
      // For now, showing empty state since no products table exists
      setProducts([])
      setLoading(false)
    } catch (error) {
      console.error('Error fetching products:', error)
      setProducts([])
      setLoading(false)
    }
  }

  // Dynamic categories loaded from database - NO HARDCODED DATA
  const [categories, setCategories] = useState([])
  const [categoriesLoading, setCategoriesLoading] = useState(true)

  // Load categories from database
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        setCategoriesLoading(true)
        const categoriesData = await dataService.getProductCategories()
        setCategories(categoriesData || [])
      } catch (error) {
        console.error('Error fetching categories:', error)
        // Fallback categories if database query fails
        setCategories([
          { id: 'all', name: 'All Products' },
          { id: 'medical-supplies', name: 'Medical Supplies' }
        ])
      } finally {
        setCategoriesLoading(false)
      }
    }
    
    fetchCategories()
  }, [])

  const filteredProducts = products.filter(product => {
    const matchesSearch = product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         product.description.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesCategory = selectedCategory === 'all' || product.category === selectedCategory
    const matchesPrice = product.price >= priceRange[0] && product.price <= priceRange[1]
    
    return matchesSearch && matchesCategory && matchesPrice
  })

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center" style={{ backgroundColor: 'var(--bg-secondary)' }}>
        <div className="text-center">
          <Package className="w-12 h-12 mx-auto mb-4" style={{ color: 'var(--primary)' }} />
          <div className="text-xl font-semibold mb-2" style={{ color: 'var(--text-primary)' }}>Loading Products...</div>
          <div className="text-sm" style={{ color: 'var(--text-secondary)' }}>Finding the best care products for you</div>
        </div>
      </div>
    )
  }

  return (
    <main className="min-h-screen" style={{ backgroundColor: 'var(--bg-secondary)' }} role="main" aria-label="Care products marketplace">
      {/* Header */}
      <div className="px-8 py-6" style={{ backgroundColor: 'var(--bg-primary)' }}>
        <div className="max-w-6xl mx-auto">
          <div className="flex items-center mb-4">
            <Link to="/" className="flex items-center text-sm font-medium mr-4 transition-colors macos-body" style={{ color: 'var(--text-secondary)' }}>
              <ArrowLeft className="w-4 h-4 mr-2" aria-hidden="true" />
              Back to Home
            </Link>
          </div>
          <h1 className="text-3xl font-semibold mb-2 macos-title" style={{ color: 'var(--text-primary)' }}>Care Products Marketplace</h1>
          <p className="text-lg macos-subtitle" style={{ color: 'var(--text-secondary)' }}>Essential products and supplies for quality care</p>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="px-8 py-6" style={{ backgroundColor: 'var(--bg-primary)' }}>
        <div className="max-w-6xl mx-auto">
          <form role="search" aria-label="Search and filter products" onSubmit={(e) => e.preventDefault()}>
            <div className="flex flex-col lg:flex-row gap-4 mb-6">
              {/* Search */}
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5" style={{ color: 'var(--text-muted)' }} aria-hidden="true" />
                <input
                  type="text"
                  placeholder="Search products..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-10 pr-4 py-3 rounded-lg border focus:outline-none focus:ring-2 macos-body"
                  style={{
                    backgroundColor: 'var(--bg-secondary)',
                    borderColor: 'var(--border-medium)',
                    color: 'var(--text-primary)'
                  }}
                  onFocus={(e) => e.currentTarget.style.borderColor = 'var(--primary)'}
                  onBlur={(e) => e.currentTarget.style.borderColor = 'var(--border-medium)'}
                  aria-label="Search for products by name or description"
                />
              </div>

              {/* Category Filter */}
              <div className="lg:w-48">
                <select
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                  className="w-full px-4 py-3 rounded-lg border focus:outline-none focus:ring-2 macos-body"
                  style={{
                    backgroundColor: 'var(--bg-secondary)',
                    borderColor: 'var(--border-medium)',
                    color: 'var(--text-primary)'
                  }}
                  onFocus={(e) => e.currentTarget.style.borderColor = 'var(--primary)'}
                  onBlur={(e) => e.currentTarget.style.borderColor = 'var(--border-medium)'}
                  aria-label="Filter products by category"
                >
                  {categories.map(category => (
                    <option key={category.id} value={category.id}>{category.name}</option>
                  ))}
                </select>
              </div>

              {/* Sort */}
              <div className="lg:w-48">
                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value)}
                  className="w-full px-4 py-3 rounded-lg border focus:outline-none focus:ring-2 macos-body"
                  style={{
                    backgroundColor: 'var(--bg-secondary)',
                    borderColor: 'var(--border-medium)',
                    color: 'var(--text-primary)'
                  }}
                  onFocus={(e) => e.currentTarget.style.borderColor = 'var(--primary)'}
                  onBlur={(e) => e.currentTarget.style.borderColor = 'var(--border-medium)'}
                  aria-label="Sort products by"
                >
                  <option value="featured">Featured</option>
                  <option value="price-low">Price: Low to High</option>
                  <option value="price-high">Price: High to Low</option>
                  <option value="rating">Highest Rated</option>
                  <option value="newest">Newest</option>
                </select>
              </div>
            </div>
          </form>

          {/* Results Count */}
          <div className="flex items-center justify-between mb-6">
            <p className="macos-body" style={{ color: 'var(--text-secondary)' }}>
              {filteredProducts.length} product{filteredProducts.length !== 1 ? 's' : ''} found
            </p>
          </div>
        </div>
      </div>

      {/* Products Grid */}
      <div className="px-8 py-6">
        <div className="max-w-6xl mx-auto">
          {filteredProducts.length === 0 ? (
            <div className="text-center py-12" role="status" aria-live="polite">
              <Package className="mx-auto mb-4 w-16 h-16" style={{ color: 'var(--text-muted)' }} aria-hidden="true" />
              <h3 className="text-xl font-semibold mb-2 macos-title" style={{ color: 'var(--text-primary)' }}>No products found</h3>
              <p className="macos-body" style={{ color: 'var(--text-secondary)' }}>Try adjusting your search criteria or filters</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6" role="grid" aria-label="Products grid">
              {filteredProducts.map((product) => (
                <div
                  key={product.id}
                  className="rounded-lg p-6 transition-all duration-200 hover:shadow-lg border"
                  style={{
                    backgroundColor: 'var(--bg-primary)',
                    borderColor: 'var(--border-light)',
                    boxShadow: 'var(--shadow-light)'
                  }}
                  role="gridcell"
                  tabIndex={0}
                  aria-label={`Product: ${product.name}, Price: $${product.price}`}
                >
                  {/* Product Image Placeholder */}
                  <div
                    className="w-full h-48 rounded-lg mb-4 flex items-center justify-center"
                    style={{ backgroundColor: 'var(--bg-secondary)' }}
                    role="img"
                    aria-label={`Image placeholder for ${product.name}`}
                  >
                    <Package className="w-16 h-16" style={{ color: 'var(--text-muted)' }} aria-hidden="true" />
                  </div>

                  {/* Product Info */}
                  <div className="space-y-3">
                    <h3 className="text-lg font-semibold line-clamp-2 macos-body" style={{ color: 'var(--text-primary)' }}>
                      {product.name}
                    </h3>

                    <p className="text-sm line-clamp-2 macos-body" style={{ color: 'var(--text-secondary)' }}>
                      {product.description}
                    </p>

                    {/* Rating */}
                    <div className="flex items-center gap-2" role="group" aria-label={`Rating: ${product.rating} out of 5 stars`}>
                      <div className="flex items-center">
                        {[...Array(5)].map((_, i) => (
                          <Star
                            key={i}
                            className={`w-4 h-4 ${i < Math.floor(product.rating) ? 'fill-current' : ''}`}
                            style={{ color: i < Math.floor(product.rating) ? 'var(--warning)' : 'var(--text-muted)' }}
                            aria-hidden="true"
                          />
                        ))}
                      </div>
                      <span className="text-sm macos-body" style={{ color: 'var(--text-secondary)' }}>
                        {product.rating} ({product.reviews} reviews)
                      </span>
                    </div>

                    {/* Price */}
                    <div className="flex items-center justify-between">
                      <span className="text-2xl font-bold macos-body" style={{ color: 'var(--primary)' }}>
                        ${product.price}
                      </span>
                      <span className="text-sm px-2 py-1 rounded-full macos-body"
                            style={{
                              backgroundColor: product.in_stock ? 'var(--bg-success)' : 'var(--bg-error)',
                              color: product.in_stock ? 'var(--text-success)' : 'var(--text-error)'
                            }}
                            role="status"
                            aria-label={product.in_stock ? 'Product is in stock' : 'Product is out of stock'}>
                        {product.in_stock ? 'In Stock' : 'Out of Stock'}
                      </span>
                    </div>

                    {/* Seller & Shipping */}
                    <div className="text-xs space-y-1 macos-body" style={{ color: 'var(--text-secondary)' }}>
                      <div className="flex items-center">
                        <Shield className="w-3 h-3 mr-1" aria-hidden="true" />
                        Sold by {product.seller}
                      </div>
                      <div className="flex items-center">
                        <Truck className="w-3 h-3 mr-1" aria-hidden="true" />
                        {product.shipping_info}
                      </div>
                    </div>

                    {/* Actions */}
                    <div className="flex gap-2 pt-2">
                      <button
                        disabled={!product.in_stock}
                        className={`flex-1 py-2 px-4 rounded-lg font-medium transition-colors flex items-center justify-center gap-2 macos-body ${
                          product.in_stock
                            ? 'hover:opacity-90'
                            : 'opacity-50 cursor-not-allowed'
                        }`}
                        style={{
                          backgroundColor: product.in_stock ? 'var(--primary)' : 'var(--text-muted)',
                          color: 'var(--bg-primary)'
                        }}
                        onClick={() => product.in_stock && alert(`Added ${product.name} to cart!`)}
                        aria-label={product.in_stock ? `Add ${product.name} to cart` : 'Product out of stock'}
                      >
                        <ShoppingCart className="w-4 h-4" aria-hidden="true" />
                        {product.in_stock ? 'Add to Cart' : 'Out of Stock'}
                      </button>
                      <button
                        className="p-2 rounded-lg border transition-colors"
                        style={{
                          borderColor: 'var(--border-medium)',
                          color: 'var(--text-secondary)'
                        }}
                        onClick={() => alert(`Added ${product.name} to wishlist!`)}
                        onMouseEnter={(e) => e.currentTarget.style.backgroundColor = 'var(--bg-secondary)'}
                        onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'transparent'}
                        aria-label={`Add ${product.name} to wishlist`}
                      >
                        <Heart className="w-4 h-4" aria-hidden="true" />
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </main>
  )
}
