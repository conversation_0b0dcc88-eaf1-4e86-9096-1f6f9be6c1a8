import { useState, useEffect } from 'react'
import { Mail, Phone, MapPin, Clock, Send, MessageSquare, HeadphonesIcon } from 'lucide-react'
import { dataService } from '../lib/dataService'

export default function Contact() {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    subject: '',
    message: '',
    urgency: 'normal'
  })
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [submitSuccess, setSubmitSuccess] = useState(false)
  const [faqData, setFaqData] = useState([])
  const [error, setError] = useState(null)

  // Load FAQ data from database - NO HARDCODED DATA (Holy Rule #1)
  useEffect(() => {
    const loadFaqData = async () => {
      try {
        const data = await dataService.getFaqData()
        setFaqData(data || [])
      } catch (err) {
        console.error('Error loading FAQ data:', err)
        setError('Failed to load FAQ data')
        setFaqData([])
      }
    }
    loadFaqData()
  }, [])

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError(null)

    // Client-side validation
    if (!formData.name.trim()) {
      setError('Please enter your full name')
      return
    }
    if (!formData.email.trim()) {
      setError('Please enter your email address')
      return
    }
    if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      setError('Please enter a valid email address')
      return
    }
    if (!formData.subject.trim()) {
      setError('Please enter a subject')
      return
    }
    if (!formData.message.trim()) {
      setError('Please enter your message')
      return
    }

    setIsSubmitting(true)

    try {
      // Submit contact form to database
      await dataService.submitContactForm({
        name: formData.name,
        email: formData.email,
        subject: formData.subject,
        message: formData.message,
        urgency: formData.urgency,
        submitted_at: new Date().toISOString()
      })
      
      setSubmitSuccess(true)
      setFormData({
        name: '',
        email: '',
        subject: '',
        message: '',
        urgency: 'normal'
      })

      // Auto-dismiss success message after 10 seconds
      setTimeout(() => {
        setSubmitSuccess(false)
      }, 10000)
    } catch (err) {
      console.error('Error submitting contact form:', err)
      setError('Failed to submit your message. Please try again.')
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <main className="min-h-screen" style={{ backgroundColor: 'var(--bg-primary)' }} role="main" aria-label="Contact Care Connector">
      {/* Hero Section */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 text-white" style={{ background: 'linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%)' }}>
        <div className="max-w-4xl mx-auto text-center">
          <div className="w-16 h-16 mx-auto mb-6 rounded-2xl bg-white/20 backdrop-blur-sm flex items-center justify-center">
            <MessageSquare className="w-8 h-8 text-white" />
          </div>
          <h1 className="text-4xl md:text-5xl font-semibold mb-6 macos-title">
            Contact Us
          </h1>
          <p className="text-xl md:text-2xl opacity-90 leading-relaxed">
            We're here to help you connect with the right healthcare professionals. Reach out anytime.
          </p>
        </div>
      </section>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="grid lg:grid-cols-3 gap-12">
          {/* Contact Information */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-2xl p-8 h-fit" style={{ boxShadow: 'var(--shadow-large)' }}>
              <h2 className="text-2xl font-bold mb-8 text-primary macos-title">
                Get in Touch
              </h2>
              
              <div className="space-y-6">
                <div className="flex items-start space-x-4">
                  <div className="w-12 h-12 rounded-xl flex items-center justify-center flex-shrink-0" style={{ backgroundColor: 'var(--primary-light)' }}>
                    <Phone className="w-6 h-6" style={{ color: 'var(--primary)' }} />
                  </div>
                  <div>
                    <h3 className="font-semibold mb-1" style={{ color: 'var(--text-primary)' }}>Phone Support</h3>
                    <p className="text-sm mb-2" style={{ color: 'var(--text-secondary)' }}>Contact support team</p>
                    <p className="font-medium" style={{ color: 'var(--text-primary)' }}>Contact info coming soon</p>
                    <p className="text-sm" style={{ color: 'var(--text-secondary)' }}>Please use contact form below</p>
                  </div>
                </div>

                <div className="flex items-start space-x-4">
                  <div className="w-12 h-12 rounded-xl flex items-center justify-center flex-shrink-0" style={{ backgroundColor: 'var(--primary-light)' }}>
                    <Mail className="w-6 h-6" style={{ color: 'var(--primary)' }} />
                  </div>
                  <div>
                    <h3 className="font-semibold mb-1" style={{ color: 'var(--text-primary)' }}>Email Support</h3>
                    <p className="text-sm mb-2" style={{ color: 'var(--text-secondary)' }}>We'll respond promptly</p>
                    <p className="font-medium" style={{ color: 'var(--text-primary)' }}>Use contact form below</p>
                  </div>
                </div>

                <div className="flex items-start space-x-4">
                  <div className="w-12 h-12 rounded-xl bg-primary/10 flex items-center justify-center flex-shrink-0">
                    <HeadphonesIcon className="w-6 h-6 text-primary" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-primary mb-1">Live Chat</h3>
                    <p className="text-secondary text-sm">Coming soon - use form below</p>
                    <p className="font-medium text-primary">Available in dashboard</p>
                  </div>
                </div>

                <div className="flex items-start space-x-4">
                  <div className="w-12 h-12 rounded-xl bg-primary/10 flex items-center justify-center flex-shrink-0">
                    <MapPin className="w-6 h-6 text-primary" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-primary mb-1">Headquarters</h3>
                    <p className="text-secondary text-sm mb-2">Location details coming soon</p>
                    <p className="font-medium text-primary">
                      Address to be announced<br />
                      Please use contact form below
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-4">
                  <div className="w-12 h-12 rounded-xl bg-primary/10 flex items-center justify-center flex-shrink-0">
                    <Clock className="w-6 h-6 text-primary" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-primary mb-1">Business Hours</h3>
                    <div className="text-sm text-secondary space-y-1">
                      <p><span className="font-medium">Hours:</span> Coming soon</p>
                      <p><span className="font-medium">Support:</span> Use contact form below</p>
                      <p className="text-primary font-medium mt-2">We'll respond to your inquiry promptly</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Contact Form */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-2xl p-8 shadow-lg">
              <h2 className="text-2xl font-bold mb-8 macos-title" style={{ color: 'var(--text-primary)' }}>
                Send Us a Message
              </h2>

              {/* Error State */}
              {error && (
                <div className="mb-6 bg-red-50 border border-red-200 rounded-xl p-4">
                  <p className="text-red-600 font-medium">{error}</p>
                  <button
                    onClick={() => setError(null)}
                    className="mt-2 text-sm text-red-500 hover:text-red-700"
                  >
                    Dismiss
                  </button>
                </div>
              )}

              {submitSuccess ? (
                <div
                  className="text-center py-8"
                  role="alert"
                  aria-live="polite"
                  id="contact-success"
                >
                  <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-green-100 flex items-center justify-center">
                    <Send className="w-8 h-8 text-green-600" />
                  </div>
                  <h3 className="text-xl font-semibold text-green-600 mb-2">Message Sent Successfully!</h3>
                  <p className="text-secondary mb-6">
                    Thank you for contacting us. We'll get back to you within 2 hours.
                  </p>
                  <button
                    onClick={() => setSubmitSuccess(false)}
                    className="px-6 py-3 rounded-xl font-semibold transition-all duration-200 hover:scale-105"
                    style={{
                      backgroundColor: 'var(--primary)',
                      color: 'var(--bg-primary)',
                      boxShadow: 'var(--shadow-medium)'
                    }}
                  >
                    Send Another Message
                  </button>
                </div>
              ) : (
                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="grid md:grid-cols-2 gap-6">
                    <div>
                      <label htmlFor="name" className="block text-sm font-medium text-primary mb-2">
                        Full Name *
                      </label>
                      <input
                        type="text"
                        id="name"
                        name="name"
                        value={formData.name}
                        onChange={handleInputChange}
                        required
                        aria-label="Full Name"
                        aria-describedby="name-help"
                        className="w-full px-4 py-3 rounded-xl focus:outline-none transition-colors"
                        style={{
                          border: '1px solid var(--border-medium)',
                          backgroundColor: 'var(--bg-primary)',
                          color: 'var(--text-primary)'
                        }}
                        onFocus={(e) => e.currentTarget.style.borderColor = 'var(--primary)'}
                        onBlur={(e) => e.currentTarget.style.borderColor = 'var(--border-medium)'}
                        placeholder="Enter your full name"
                      />
                    </div>

                    <div>
                      <label htmlFor="email" className="block text-sm font-medium text-primary mb-2">
                        Email Address *
                      </label>
                      <input
                        type="email"
                        id="email"
                        name="email"
                        value={formData.email}
                        onChange={handleInputChange}
                        required
                        className="w-full px-4 py-3 rounded-xl focus:outline-none transition-colors"
                        style={{
                          border: '1px solid var(--border-medium)',
                          backgroundColor: 'var(--bg-primary)',
                          color: 'var(--text-primary)'
                        }}
                        onFocus={(e) => e.currentTarget.style.borderColor = 'var(--primary)'}
                        onBlur={(e) => e.currentTarget.style.borderColor = 'var(--border-medium)'}
                        placeholder="Enter your email address"
                      />
                    </div>
                  </div>

                  <div className="grid md:grid-cols-2 gap-6">
                    <div>
                      <label htmlFor="subject" className="block text-sm font-medium text-primary mb-2">
                        Subject *
                      </label>
                      <input
                        type="text"
                        id="subject"
                        name="subject"
                        value={formData.subject}
                        onChange={handleInputChange}
                        required
                        className="w-full px-4 py-3 rounded-xl focus:outline-none transition-colors"
                        style={{
                          border: '1px solid var(--border-medium)',
                          backgroundColor: 'var(--bg-primary)',
                          color: 'var(--text-primary)'
                        }}
                        onFocus={(e) => e.currentTarget.style.borderColor = 'var(--primary)'}
                        onBlur={(e) => e.currentTarget.style.borderColor = 'var(--border-medium)'}
                        placeholder="What's this about?"
                      />
                    </div>

                    <div>
                      <label htmlFor="urgency" className="block text-sm font-medium text-primary mb-2">
                        Priority Level
                      </label>
                      <select
                        id="urgency"
                        name="urgency"
                        value={formData.urgency}
                        onChange={handleInputChange}
                        className="w-full px-4 py-3 rounded-xl focus:outline-none transition-colors"
                        style={{
                          border: '1px solid var(--border-medium)',
                          backgroundColor: 'var(--bg-primary)',
                          color: 'var(--text-primary)'
                        }}
                        onFocus={(e) => e.currentTarget.style.borderColor = 'var(--primary)'}
                        onBlur={(e) => e.currentTarget.style.borderColor = 'var(--border-medium)'}
                      >
                        <option value="low">Low - General inquiry</option>
                        <option value="normal">Normal - Standard support</option>
                        <option value="high">High - Urgent matter</option>
                        <option value="emergency">Emergency - Immediate attention</option>
                      </select>
                    </div>
                  </div>

                  <div>
                    <label htmlFor="message" className="block text-sm font-medium text-primary mb-2">
                      Message *
                    </label>
                    <textarea
                      id="message"
                      name="message"
                      value={formData.message}
                      onChange={handleInputChange}
                      required
                      rows={6}
                      className="w-full px-4 py-3 rounded-xl focus:outline-none transition-colors resize-none"
                      style={{
                        border: '1px solid var(--border-medium)',
                        backgroundColor: 'var(--bg-primary)',
                        color: 'var(--text-primary)'
                      }}
                      onFocus={(e) => e.currentTarget.style.borderColor = 'var(--primary)'}
                      onBlur={(e) => e.currentTarget.style.borderColor = 'var(--border-medium)'}
                      placeholder="Please provide details about your inquiry or how we can help you..."
                    />
                  </div>

                  <div className="bg-blue-50 p-4 rounded-xl">
                    <p className="text-sm text-blue-800">
                      <strong>For medical emergencies,</strong> please call 911 immediately. 
                      For urgent care needs, contact your healthcare provider directly or call our 24/7 support line.
                    </p>
                  </div>

                  <div className="flex space-x-4">
                    <button
                      type="button"
                      onClick={() => {
                        setFormData({
                          name: '',
                          email: '',
                          subject: '',
                          message: '',
                          urgency: 'normal'
                        })
                      }}
                      className="flex-1 py-4 rounded-xl font-semibold text-lg transition-colors"
                      style={{
                        backgroundColor: 'var(--bg-secondary)',
                        color: 'var(--text-primary)',
                        border: '1px solid var(--border-medium)'
                      }}
                      onMouseEnter={(e) => e.currentTarget.style.backgroundColor = 'var(--bg-tertiary)'}
                      onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'var(--bg-secondary)'}
                    >
                      Clear Form
                    </button>

                  <button
                    type="submit"
                    disabled={isSubmitting}
                    className="flex-1 py-4 rounded-xl font-semibold text-lg flex items-center justify-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                    style={{
                      backgroundColor: 'var(--primary)',
                      color: 'var(--bg-primary)'
                    }}
                    onMouseEnter={(e) => !e.currentTarget.disabled && (e.currentTarget.style.backgroundColor = 'var(--primary)')}
                    onMouseLeave={(e) => !e.currentTarget.disabled && (e.currentTarget.style.backgroundColor = 'var(--primary)')}
                  >
                    {isSubmitting ? (
                      <>
                        <div className="animate-spin rounded-full h-5 w-5 border-2 border-white border-t-transparent"></div>
                        <span>Sending Message...</span>
                      </>
                    ) : (
                      <>
                        <Send className="w-5 h-5" />
                        <span>Send Message</span>
                      </>
                    )}
                  </button>
                  </div>
                </form>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* FAQ Section */}
      <section className="py-16 px-4 sm:px-6 lg:px-8 bg-white">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold mb-6 text-primary macos-title">
              Frequently Asked Questions
            </h2>
            <p className="text-lg text-secondary">
              Quick answers to common questions about our services
            </p>
          </div>
          
          <div className="space-y-6">
            {faqData.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-gray-500">FAQ content will be available soon.</p>
              </div>
            ) : (
              faqData.map((faq, index) => (
                <div key={index} className="rounded-xl p-6" style={{ backgroundColor: 'var(--bg-primary)', boxShadow: 'var(--shadow-small)' }}>
                  <h3 className="font-semibold mb-2" style={{ color: 'var(--text-primary)' }}>{faq.question}</h3>
                  <p style={{ color: 'var(--text-secondary)' }}>{faq.answer}</p>
                </div>
              ))
            )}
          </div>
        </div>
      </section>
    </main>
  )
}
