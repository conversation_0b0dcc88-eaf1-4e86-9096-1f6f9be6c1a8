import React, { useState, useEffect } from 'react'
import { dataService } from '../lib/dataService'
import { supabase } from '../lib/supabase'
import { Search, Users, MapPin, Lock, Globe, Heart, CheckCircle } from 'lucide-react'
import { useNavigate } from 'react-router-dom'

interface CareGroup {
  id: string
  name: string
  description?: string
  category?: string
  location?: string
  privacy_setting?: string
  member_count?: number
  created_at?: string
  avatar_url?: string
  is_member?: boolean
}

export default function JoinGroup() {
  const [careGroups, setCareGroups] = useState<CareGroup[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('')
  const [joiningGroup, setJoiningGroup] = useState<string | null>(null)
  const [joinError, setJoinError] = useState('')
  const [joinSuccess, setJoinSuccess] = useState('')
  const navigate = useNavigate()

  useEffect(() => {
    async function fetchAvailableGroups() {
      try {
        const data = await dataService.getCareGroups()
        // Filter out groups user is already a member of
        const availableGroups = data.filter((group: CareGroup) => !group.is_member)
        setCareGroups(availableGroups)
      } catch (err) {
        console.error('Failed to load care groups:', err)
      } finally {
        setLoading(false)
      }
    }
    fetchAvailableGroups()
  }, [])

  const handleJoinGroup = async (groupId: string) => {
    setJoiningGroup(groupId)
    setJoinError('')
    setJoinSuccess('')

    try {
      // Get current user
      const { data: { session } } = await supabase.auth.getSession()
      if (!session) {
        setJoinError('You must be signed in to join a care group')
        return
      }

      // Check if already a member
      const { data: existingMember } = await supabase
        .from('care_group_members')
        .select('id')
        .eq('group_id', groupId)
        .eq('user_id', session.user.id)
        .single()

      if (existingMember) {
        setJoinError('You are already a member of this group')
        return
      }

      // Add user as member
      const { error } = await supabase
        .from('care_group_members')
        .insert({
          group_id: groupId,
          user_id: session.user.id,
          role: 'member',
          joined_at: new Date().toISOString()
        })

      if (error) throw error

      // Get current group data to update member count
      const { data: currentGroup } = await supabase
        .from('care_groups')
        .select('member_count')
        .eq('id', groupId)
        .single()

      // Update member count
      await supabase
        .from('care_groups')
        .update({ 
          member_count: (currentGroup?.member_count || 0) + 1
        })
        .eq('id', groupId)

      setJoinSuccess('Successfully joined the care group!')
      
      // Remove group from available list
      setCareGroups(prev => prev.filter(group => group.id !== groupId))

      console.log('Successfully joined care group:', groupId)
    } catch (error: any) {
      console.error('Error joining care group:', error)
      setJoinError(error.message || 'Failed to join care group')
    } finally {
      setJoiningGroup(null)
    }
  }

  const filteredGroups = careGroups.filter(group => {
    const matchesSearch = !searchTerm || 
      group.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (group.description && group.description.toLowerCase().includes(searchTerm.toLowerCase()))
    
    const matchesCategory = !selectedCategory || group.category === selectedCategory
    
    return matchesSearch && matchesCategory
  })

  if (loading) return <div style={{ padding: '2rem', textAlign: 'center' }}>Loading available groups...</div>

  return (
    <div style={{ padding: '2rem' }}>
      <h1 style={{
        fontSize: '1.75rem',
        fontWeight: '400',
        color: 'var(--text-primary)',
        marginBottom: '1rem',
        textAlign: 'center'
      }}>
        Join a Care Group
      </h1>
      
      <p style={{ 
        textAlign: 'center', 
        color: 'var(--text-secondary)', 
        marginBottom: '2rem',
        maxWidth: '600px',
        margin: '0 auto 2rem auto'
      }}>
        Find and join care groups in your community. Connect with others who share similar care journeys and experiences.
      </p>

      {/* Success Message */}
      {joinSuccess && (
        <div style={{
          backgroundColor: 'var(--bg-success-light)',
          border: '1px solid var(--success)',
          color: 'var(--success)',
          padding: '0.75rem',
          borderRadius: '0.375rem',
          marginBottom: '1.5rem',
          fontSize: '0.875rem',
          textAlign: 'center',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          gap: '0.5rem'
        }}>
          <CheckCircle style={{ width: '1rem', height: '1rem' }} />
          {joinSuccess}
        </div>
      )}

      {/* Error Message */}
      {joinError && (
        <div style={{
          backgroundColor: 'var(--bg-error-light)',
          border: '1px solid var(--error)',
          color: 'var(--error)',
          padding: '0.75rem',
          borderRadius: '0.375rem',
          marginBottom: '1.5rem',
          fontSize: '0.875rem',
          textAlign: 'center'
        }}>
          {joinError}
        </div>
      )}

      {/* Search and Filter Section */}
      <div style={{
        display: 'flex',
        gap: '1rem',
        maxWidth: '800px',
        margin: '0 auto 3rem auto',
        flexWrap: 'wrap'
      }}>
        {/* Search Bar */}
        <div style={{
          flex: '1',
          minWidth: '300px',
          position: 'relative'
        }}>
          <Search style={{
            position: 'absolute',
            left: '1rem',
            top: '50%',
            transform: 'translateY(-50%)',
            width: '1.25rem',
            height: '1.25rem',
            color: 'var(--text-secondary)'
          }} />
          <input
            type="text"
            placeholder="Search groups by name or description..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            style={{
              width: '100%',
              padding: '0.75rem 1rem 0.75rem 3rem',
              borderRadius: '0.5rem',
              border: '1px solid var(--border-medium)',
              fontSize: '1rem',
              backgroundColor: 'var(--bg-primary)'
            }}
          />
        </div>

        {/* Category Filter */}
        <div style={{ minWidth: '200px' }}>
          <select
            value={selectedCategory}
            onChange={(e) => setSelectedCategory(e.target.value)}
            style={{
              width: '100%',
              padding: '0.75rem',
              borderRadius: '0.5rem',
              border: '1px solid var(--border-medium)',
              fontSize: '1rem',
              backgroundColor: 'var(--bg-primary)'
            }}
          >
            <option value="">All Categories</option>
            <option value="alzheimers">Alzheimer's & Dementia</option>
            <option value="cancer">Cancer Support</option>
            <option value="chronic-illness">Chronic Illness</option>
            <option value="mental-health">Mental Health</option>
            <option value="senior-care">Senior Care</option>
            <option value="disability">Disability Support</option>
            <option value="family-caregivers">Family Caregivers</option>
            <option value="other">Other</option>
          </select>
        </div>
      </div>
      
      {filteredGroups.length === 0 ? (
        <div style={{ 
          textAlign: 'center', 
          padding: '4rem 2rem', 
          backgroundColor: 'var(--bg-secondary)', 
          borderRadius: '0.5rem',
          border: '1px solid var(--border-light)',
          maxWidth: '600px',
          margin: '0 auto'
        }}>
          <Heart style={{
            width: '4rem',
            height: '4rem',
            color: 'var(--text-muted)',
            margin: '0 auto 1.5rem auto'
          }} />
          
          <h3 style={{
            fontSize: '1.25rem',
            fontWeight: '600',
            color: 'var(--text-primary)',
            marginBottom: '0.5rem'
          }}>
            {careGroups.length === 0 ? 'No care groups available' : 'No groups match your search'}
          </h3>
          
          <p style={{ 
            color: 'var(--text-secondary)',
            marginBottom: '2rem'
          }}>
            {careGroups.length === 0 
              ? 'There are no care groups available to join at the moment.'
              : 'Try adjusting your search or category filter to find more groups.'
            }
          </p>

          <button 
            onClick={() => navigate('/care-groups')}
            style={{
              backgroundColor: 'var(--primary)',
              color: 'white',
              padding: '0.75rem 1.5rem',
              borderRadius: '0.375rem',
              border: 'none',
              fontWeight: '500',
              fontSize: '0.875rem',
              cursor: 'pointer',
              display: 'inline-flex',
              alignItems: 'center',
              gap: '0.5rem'
            }}
          >
            <Users style={{ width: '1rem', height: '1rem' }} />
            Browse All Care Groups
          </button>
        </div>
      ) : (
        <div style={{ 
          display: 'grid', 
          gridTemplateColumns: 'repeat(auto-fit, minmax(350px, 1fr))', 
          gap: '2rem',
          maxWidth: '1200px',
          margin: '0 auto'
        }}>
          {filteredGroups.map((group) => (
            <div key={group.id} style={{ 
              backgroundColor: 'var(--bg-primary)', 
              padding: '2rem', 
              borderRadius: '0.5rem', 
              border: '1px solid var(--border-light)',
              position: 'relative'
            }}>
              {/* Privacy Indicator */}
              <div style={{
                position: 'absolute',
                top: '1rem',
                right: '1rem',
                display: 'flex',
                alignItems: 'center',
                gap: '0.25rem',
                fontSize: '0.75rem',
                color: 'var(--text-muted)',
                backgroundColor: 'var(--bg-secondary)',
                padding: '0.25rem 0.5rem',
                borderRadius: '0.25rem'
              }}>
                {group.privacy_setting === 'private' ? (
                  <>
                    <Lock style={{ width: '0.75rem', height: '0.75rem' }} />
                    Private
                  </>
                ) : (
                  <>
                    <Globe style={{ width: '0.75rem', height: '0.75rem' }} />
                    Public
                  </>
                )}
              </div>

              <h3 style={{ 
                color: 'var(--text-primary)', 
                marginBottom: '1rem',
                fontSize: '1.25rem',
                fontWeight: '600',
                paddingRight: '4rem'
              }}>
                {group.name}
              </h3>
              
              {group.description && (
                <p style={{ 
                  color: 'var(--text-secondary)',
                  marginBottom: '1rem',
                  lineHeight: '1.4'
                }}>
                  {group.description}
                </p>
              )}

              {/* Group Details */}
              <div style={{ marginBottom: '1.5rem' }}>
                {group.category && (
                  <div style={{
                    display: 'inline-block',
                    backgroundColor: 'var(--bg-secondary)',
                    color: 'var(--text-secondary)',
                    padding: '0.25rem 0.5rem',
                    borderRadius: '0.25rem',
                    fontSize: '0.75rem',
                    marginBottom: '0.5rem'
                  }}>
                    {group.category.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                  </div>
                )}

                <div style={{
                  display: 'flex',
                  gap: '1rem',
                  fontSize: '0.875rem',
                  color: 'var(--text-secondary)',
                  flexWrap: 'wrap'
                }}>
                  {group.location && (
                    <div style={{ display: 'flex', alignItems: 'center', gap: '0.25rem' }}>
                      <MapPin style={{ width: '1rem', height: '1rem' }} />
                      {group.location}
                    </div>
                  )}
                  <div style={{ display: 'flex', alignItems: 'center', gap: '0.25rem' }}>
                    <Users style={{ width: '1rem', height: '1rem' }} />
                    {group.member_count || 0} members
                  </div>
                </div>
              </div>

              <button
                onClick={() => handleJoinGroup(group.id)}
                disabled={joiningGroup === group.id}
                style={{
                  backgroundColor: joiningGroup === group.id ? 'var(--text-muted)' : 'var(--primary)',
                  color: 'white',
                  padding: '0.75rem 1.5rem',
                  borderRadius: '0.375rem',
                  border: 'none',
                  fontWeight: '500',
                  fontSize: '0.875rem',
                  cursor: joiningGroup === group.id ? 'not-allowed' : 'pointer',
                  width: '100%',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  gap: '0.5rem'
                }}
              >
                {joiningGroup === group.id ? (
                  <>
                    <div style={{
                      width: '1rem',
                      height: '1rem',
                      border: '2px solid transparent',
                      borderTop: '2px solid white',
                      borderRadius: '50%',
                      animation: 'spin 1s linear infinite'
                    }} />
                    Joining...
                  </>
                ) : (
                  <>
                    <Users style={{ width: '1rem', height: '1rem' }} />
                    Join Group
                  </>
                )}
              </button>
            </div>
          ))}
        </div>
      )}
    </div>
  )
}
