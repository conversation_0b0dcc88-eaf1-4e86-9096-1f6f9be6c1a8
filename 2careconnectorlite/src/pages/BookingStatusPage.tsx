import React, { useState, useEffect } from 'react'
import { <PERSON> } from 'react-router-dom'
import { CheckCircle, Clock, AlertCircle, XCircle, Calendar, User, MapPin, Phone, Mail, MessageSquare, Star, Lock } from 'lucide-react'
import { supabase } from '../lib/supabase'

interface BookingStatus {
  id: string
  status: 'pending' | 'confirmed' | 'in_progress' | 'completed' | 'cancelled' | 'no_show'
  service_date: string
  start_time: string
  end_time: string
  duration_hours: number
  total_amount: number
  provider_id: string
  client_id: string
  special_instructions?: string
  created_at: string
  updated_at: string
  provider_info?: {
    full_name: string
    email: string
    phone?: string
    address?: string
    avatar_url?: string
    rating?: number
    specialties?: string[]
  }
  status_history?: {
    status: string
    changed_at: string
    notes?: string
  }[]
}

const BookingStatusPage: React.FC = () => {
  const [user, setUser] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [processing, setProcessing] = useState(false)
  const [bookings, setBookings] = useState<BookingStatus[]>([])
  const [selectedBooking, setSelectedBooking] = useState<BookingStatus | null>(null)
  const [showDetails, setShowDetails] = useState(false)
  const [filterStatus, setFilterStatus] = useState('all')

  useEffect(() => {
    const getUser = async () => {
      const { data: { user } } = await supabase.auth.getUser()
      setUser(user)
      if (user) {
        await loadBookings()
      }
      setLoading(false)
    }
    getUser()
  }, [filterStatus])

  const loadBookings = async () => {
    if (!user) return

    try {
      let query = supabase
        .from('bookings')
        .select(`
          id, status, service_date, start_time, end_time, duration_hours, 
          total_amount, provider_id, client_id, special_instructions, 
          created_at, updated_at,
          profiles!bookings_provider_id_fkey(
            full_name, email, phone, address, avatar_url, rating, specialties
          )
        `)
        .eq('client_id', user.id)
        .order('service_date', { ascending: false })

      if (filterStatus !== 'all') {
        query = query.eq('status', filterStatus)
      }

      const { data, error } = await query

      if (error) throw error

      const transformedBookings = (data || []).map(booking => ({
        id: booking.id,
        status: booking.status,
        service_date: booking.service_date,
        start_time: booking.start_time,
        end_time: booking.end_time,
        duration_hours: booking.duration_hours,
        total_amount: booking.total_amount,
        provider_id: booking.provider_id,
        client_id: booking.client_id,
        special_instructions: booking.special_instructions,
        created_at: booking.created_at,
        updated_at: booking.updated_at,
        provider_info: booking.profiles && booking.profiles[0] ? {
          full_name: booking.profiles[0].full_name,
          email: booking.profiles[0].email,
          phone: booking.profiles[0].phone,
          address: booking.profiles[0].address,
          avatar_url: booking.profiles[0].avatar_url,
          rating: booking.profiles[0].rating,
          specialties: booking.profiles[0].specialties
        } : undefined
      }))

      setBookings(transformedBookings)
    } catch (error) {
      console.error('Error loading bookings:', error)
    }
  }

  const updateBookingStatus = async (bookingId: string, newStatus: string) => {
    setProcessing(true)
    try {
      const { error } = await supabase
        .from('bookings')
        .update({ 
          status: newStatus,
          updated_at: new Date().toISOString()
        })
        .eq('id', bookingId)

      if (error) throw error

      await loadBookings()
      setShowDetails(false)
    } catch (error) {
      console.error('Error updating booking status:', error)
    } finally {
      setProcessing(false)
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'confirmed': return <CheckCircle className="w-5 h-5 text-green-600" />
      case 'pending': return <Clock className="w-5 h-5 text-yellow-600" />
      case 'in_progress': return <Clock className="w-5 h-5 text-blue-600" />
      case 'completed': return <CheckCircle className="w-5 h-5 text-green-600" />
      case 'cancelled': return <XCircle className="w-5 h-5 text-red-600" />
      case 'no_show': return <AlertCircle className="w-5 h-5 text-orange-600" />
      default: return <Clock className="w-5 h-5" style={{color: 'var(--text-secondary)'}} />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'confirmed': return 'bg-green-100 text-green-800'
      case 'pending': return 'bg-yellow-100 text-yellow-800'
      case 'in_progress': return 'bg-blue-100 text-blue-800'
      case 'completed': return 'bg-green-100 text-green-800'
      case 'cancelled': return 'bg-red-100 text-red-800'
      case 'no_show': return 'bg-orange-100 text-orange-800'
      default: return 'bg-green-100 text-green-800'
    }
  }

  const getStatusMessage = (status: string) => {
    switch (status) {
      case 'pending': return 'Waiting for provider confirmation'
      case 'confirmed': return 'Booking confirmed and scheduled'
      case 'in_progress': return 'Service currently in progress'
      case 'completed': return 'Service completed successfully'
      case 'cancelled': return 'Booking has been cancelled'
      case 'no_show': return 'Provider did not show up'
      default: return 'Status unknown'
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen 50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-logo-green mx-auto mb-4"></div>
          <p className="600">Loading booking status...</p>
        </div>
      </div>
    )
  }

  if (!user) {
    return (
      <div className="min-h-screen 50">
        {/* Header with Navigation */}
        <div className="bg-white border-b" style={{borderColor: 'var(--border-light)'}}>
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-2xl font-bold 900">Status Management</h1>
                <p className="600 mt-1">Track and manage your booking status</p>
              </div>
              <Link
                to="/dashboard"
                className="500 hover:700 transition-colors"
              >
                ← Back to Dashboard
              </Link>
            </div>
          </div>
        </div>
        
        {/* Authentication Error State */}
        <div className="auth-error-container">
          <div className="auth-error-content">
            <Lock className="auth-error-icon" />
            <h2 className="auth-error-title">Access Restricted</h2>
            <p className="auth-error-message">Please sign in to view your booking status.</p>
            <div className="auth-error-buttons">
              <button
                onClick={() => window.location.href = '/auth'}
                className="auth-error-primary-button"
              >
                Sign In
              </button>
              <button
                onClick={() => window.location.href = '/dashboard'}
                className="auth-error-secondary-button"
              >
                Back to Dashboard
              </button>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen 50">
      <div className="max-w-4xl mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold 900 mb-2">Booking Status</h1>
          <p className="600">Track and manage your care service bookings</p>
        </div>

        {/* Status Filter */}
        <div className="bg-white rounded-lg shadow-sm p-4 mb-6">
          <div className="flex items-center gap-4">
            <label className="text-sm font-medium 700">Filter by Status:</label>
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value)}
              className="px-3 py-2 border rounded-lg focus:ring-2 focus:ring-logo-green focus:border-transparent" style={{borderColor: 'var(--border-light)'}}
            >
              <option value="all">All Bookings</option>
              <option value="pending">Pending</option>
              <option value="confirmed">Confirmed</option>
              <option value="in_progress">In Progress</option>
              <option value="completed">Completed</option>
              <option value="cancelled">Cancelled</option>
              <option value="no_show">No Show</option>
            </select>
          </div>
        </div>

        {/* Booking List */}
        <div className="space-y-4">
          {bookings.length > 0 ? (
            bookings.map((booking) => (
              <div key={booking.id} className="bg-white rounded-lg shadow-sm p-6">
                <div className="flex justify-between items-start mb-4">
                  <div className="flex items-center gap-3">
                    {getStatusIcon(booking.status)}
                    <div>
                      <h3 className="text-lg font-semibold 900">
                        {booking.provider_info?.full_name || 'Unknown Provider'}
                      </h3>
                      <span className={`inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(booking.status)}`}>
                        {booking.status.replace('_', ' ')}
                      </span>
                    </div>
                  </div>
                  <button
                    onClick={() => {
                      setSelectedBooking(booking)
                      setShowDetails(true)
                    }}
                    className="text-logo-green hover:text-green-600 text-sm font-medium"
                  >
                    View Details
                  </button>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                  <div className="flex items-center gap-2">
                    <Calendar className="w-4 h-4 400" />
                    <span className="text-sm 600">
                      {new Date(booking.service_date).toLocaleDateString()}
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Clock className="w-4 h-4 400" />
                    <span className="text-sm 600">
                      {booking.start_time} - {booking.end_time}
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <User className="w-4 h-4 400" />
                    <span className="text-sm 600">
                      {booking.duration_hours} hours - ${booking.total_amount.toFixed(2)}
                    </span>
                  </div>
                </div>

                <div className="50 p-3 rounded-lg">
                  <p className="text-sm 700">{getStatusMessage(booking.status)}</p>
                </div>

                {booking.status === 'pending' && (
                  <div className="mt-4 flex gap-2">
                    <button
                      onClick={() => updateBookingStatus(booking.id, 'cancelled')}
                      disabled={processing}
                      className="bg-red-100 text-red-700 px-3 py-1 rounded text-sm hover:bg-red-200 transition-colors disabled:opacity-50"
                    >
                      Cancel Booking
                    </button>
                  </div>
                )}

                {booking.status === 'completed' && (
                  <div className="mt-4">
                    <button
                      onClick={() => window.location.href = `/submit-booking-review?booking_id=${booking.id}`}
                      className="bg-logo-green text-white px-4 py-2 rounded-lg text-sm hover:bg-green-600 transition-colors"
                    >
                      Leave Review
                    </button>
                  </div>
                )}
              </div>
            ))
          ) : (
            <div className="bg-white rounded-lg shadow-sm p-12 text-center">
              <Calendar className="w-16 h-16 300 mx-auto mb-4" />
              <h3 className="text-lg font-medium 900 mb-2">No bookings found</h3>
              <p className="600 mb-6">
                {filterStatus !== 'all' ? 
                  `No bookings with status "${filterStatus}" found.` : 
                  'You haven\'t made any bookings yet.'
                }
              </p>
              <button
                onClick={() => window.location.href = '/caregivers'}
                className="bg-logo-green text-white px-6 py-3 rounded-lg hover:bg-green-600 transition-colors"
              >
                Find Care Providers
              </button>
            </div>
          )}
        </div>

        {/* Booking Detail Modal */}
        {showDetails && selectedBooking && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-8 w-full max-w-2xl mx-4 max-h-[90vh] overflow-y-auto">
              <div className="flex justify-between items-center mb-6">
                <h3 className="text-xl font-bold 900">Booking Details</h3>
                <button
                  onClick={() => setShowDetails(false)}
                  className="400 hover:600"
                >
                  ×
                </button>
              </div>

              <div className="space-y-6">
                {/* Status */}
                <div>
                  <label className="block text-sm font-medium 700 mb-2">Current Status</label>
                  <div className="flex items-center gap-3">
                    {getStatusIcon(selectedBooking.status)}
                    <span className={`inline-flex items-center px-3 py-1 text-sm font-semibold rounded-full ${getStatusColor(selectedBooking.status)}`}>
                      {selectedBooking.status.replace('_', ' ')}
                    </span>
                  </div>
                  <p className="text-sm 600 mt-2">{getStatusMessage(selectedBooking.status)}</p>
                </div>

                {/* Provider Info */}
                <div>
                  <label className="block text-sm font-medium 700 mb-2">Care Provider</label>
                  <div className="50 p-4 rounded-lg">
                    <div className="flex items-center gap-3 mb-3">
                      {selectedBooking.provider_info?.avatar_url ? (
                        <img 
                          src={selectedBooking.provider_info.avatar_url} 
                          alt={selectedBooking.provider_info.full_name}
                          className="w-12 h-12 rounded-full object-cover"
                        />
                      ) : (
                        <div className="w-12 h-12 300 rounded-full flex items-center justify-center">
                          <User className="w-6 h-6 600" />
                        </div>
                      )}
                      <div>
                        <div className="font-medium 900">{selectedBooking.provider_info?.full_name}</div>
                        {selectedBooking.provider_info?.rating && (
                          <div className="flex items-center gap-1">
                            <Star className="w-4 h-4 text-yellow-400 fill-current" />
                            <span className="text-sm 600">{selectedBooking.provider_info.rating}</span>
                          </div>
                        )}
                      </div>
                    </div>
                    <div className="space-y-2 text-sm 600">
                      <div className="flex items-center gap-2">
                        <Mail className="w-4 h-4" />
                        <span>{selectedBooking.provider_info?.email}</span>
                      </div>
                      {selectedBooking.provider_info?.phone && (
                        <div className="flex items-center gap-2">
                          <Phone className="w-4 h-4" />
                          <span>{selectedBooking.provider_info.phone}</span>
                        </div>
                      )}
                      {selectedBooking.provider_info?.address && (
                        <div className="flex items-center gap-2">
                          <MapPin className="w-4 h-4" />
                          <span>{selectedBooking.provider_info.address}</span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                {/* Service Details */}
                <div>
                  <label className="block text-sm font-medium 700 mb-2">Service Details</label>
                  <div className="50 p-4 rounded-lg">
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="font-medium">Date:</span>
                        <div>{new Date(selectedBooking.service_date).toLocaleDateString()}</div>
                      </div>
                      <div>
                        <span className="font-medium">Time:</span>
                        <div>{selectedBooking.start_time} - {selectedBooking.end_time}</div>
                      </div>
                      <div>
                        <span className="font-medium">Duration:</span>
                        <div>{selectedBooking.duration_hours} hours</div>
                      </div>
                      <div>
                        <span className="font-medium">Total Amount:</span>
                        <div className="font-semibold">${selectedBooking.total_amount.toFixed(2)}</div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Special Instructions */}
                {selectedBooking.special_instructions && (
                  <div>
                    <label className="block text-sm font-medium 700 mb-2">Special Instructions</label>
                    <div className="50 p-3 rounded-lg text-sm 700">
                      {selectedBooking.special_instructions}
                    </div>
                  </div>
                )}

                {/* Actions */}
                <div className="flex justify-end gap-3">
                  {selectedBooking.status === 'confirmed' && (
                    <button
                      onClick={() => window.location.href = `/reschedule-booking?booking_id=${selectedBooking.id}`}
                      className="100 700 px-4 py-2 rounded-lg hover:200 transition-colors"
                    >
                      Reschedule
                    </button>
                  )}
                  {selectedBooking.provider_info?.email && (
                    <button
                      onClick={() => window.location.href = `/secure-messaging?provider_id=${selectedBooking.provider_id}`}
                      className="bg-blue-100 text-blue-700 px-4 py-2 rounded-lg hover:bg-blue-200 transition-colors flex items-center gap-2"
                    >
                      <MessageSquare className="w-4 h-4" />
                      Message Provider
                    </button>
                  )}
                  <button
                    onClick={() => setShowDetails(false)}
                    className="bg-logo-green text-white px-4 py-2 rounded-lg hover:bg-green-600 transition-colors"
                  >
                    Close
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default BookingStatusPage
