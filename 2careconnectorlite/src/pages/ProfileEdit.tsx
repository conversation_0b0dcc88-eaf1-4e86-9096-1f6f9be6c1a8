import { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { ArrowLeft, Save, User, Mail, Phone, MapPin, Calendar } from 'lucide-react'
import { supabase } from '../lib/supabase'

interface UserProfile {
  id: string
  email: string
  first_name?: string
  last_name?: string
  full_name?: string
  avatar_url?: string
  phone?: string
  location?: string
  bio?: string
  date_of_birth?: string
  created_at?: string
}

interface AuthUser {
  id: string
  email: string
}

export default function ProfileEdit() {
  const navigate = useNavigate()
  const [user, setUser] = useState<AuthUser | null>(null)
  const [profile, setProfile] = useState<UserProfile | null>(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)
  const [validationErrors, setValidationErrors] = useState<{[key: string]: string}>({})
  const [originalFormData, setOriginalFormData] = useState({
    first_name: '',
    last_name: '',
    email: '',
    phone: '',
    location: '',
    bio: '',
    date_of_birth: ''
  })
  const [formData, setFormData] = useState({
    first_name: '',
    last_name: '',
    email: '',
    phone: '',
    location: '',
    bio: '',
    date_of_birth: ''
  })

  useEffect(() => {
    checkUser()
  }, [])

  const checkUser = async () => {
    try {
      const { data: { session } } = await supabase.auth.getSession()
      
      if (!session) {
        navigate('/sign-in')
        return
      }

      setUser({
        id: session.user.id,
        email: session.user.email || ''
      })
      
      // Load user profile from care_connector schema
      const { data: profile, error } = await supabase
        .schema('care_connector')
        .from('profiles')
        .select('*')
        .eq('id', session.user.id)
        .single()

      if (error) {
        console.error('Error loading profile:', error)
        setError('Failed to load profile data')
      } else if (profile) {
        setProfile(profile)
        const initialData = {
          first_name: profile.first_name || '',
          last_name: profile.last_name || '',
          email: profile.email || session.user.email || '',
          phone: profile.phone || '',
          location: profile.location || '',
          bio: profile.bio || '',
          date_of_birth: profile.date_of_birth || ''
        }
        setFormData(initialData)
        setOriginalFormData(initialData)
      }
    } catch (error) {
      console.error('Error checking user:', error)
      setError('Failed to load user data')
    } finally {
      setLoading(false)
    }
  }

  const validateForm = () => {
    const errors: {[key: string]: string} = {}

    if (!formData.first_name.trim()) {
      errors.first_name = 'First name is required'
    }

    if (!formData.last_name.trim()) {
      errors.last_name = 'Last name is required'
    }

    if (formData.phone && !/^\(\d{3}\) \d{3}-\d{4}$/.test(formData.phone)) {
      errors.phone = 'Phone number must be in format (*************'
    }

    setValidationErrors(errors)
    return Object.keys(errors).length === 0
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))

    // Clear validation error when user starts typing
    if (validationErrors[name]) {
      setValidationErrors(prev => ({
        ...prev,
        [name]: ''
      }))
    }
  }

  const handleReset = () => {
    setFormData(originalFormData)
    setValidationErrors({})
    setError(null)
    setSuccess(null)
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
      e.preventDefault()
      const form = e.currentTarget.closest('form')
      if (form) {
        form.requestSubmit()
      }
    }
  }

  const handleSave = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!user) return

    // Validate form before submission
    if (!validateForm()) {
      setError('Please fix the validation errors below')
      return
    }

    setSaving(true)
    setError(null)
    setSuccess(null)

    try {
      const { error } = await supabase
        .from('profiles')
        .update({
          first_name: formData.first_name,
          last_name: formData.last_name,
          full_name: `${formData.first_name} ${formData.last_name}`.trim(),
          phone: formData.phone,
          location: formData.location,
          bio: formData.bio,
          date_of_birth: formData.date_of_birth,
          updated_at: new Date().toISOString()
        })
        .eq('id', user.id)

      if (error) throw error

      setSuccess('Profile updated successfully!')
      setTimeout(() => {
        navigate('/dashboard')
      }, 2000)
    } catch (error) {
      console.error('Error updating profile:', error)
      setError('Failed to update profile. Please try again.')
    } finally {
      setSaving(false)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center" style={{ backgroundColor: 'var(--bg-primary)' }}>
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 mx-auto mb-4" style={{ borderColor: 'var(--primary)' }}></div>
          <p style={{ color: 'var(--text-secondary)' }}>Loading profile...</p>
        </div>
      </div>
    )
  }

  return (
    <main className="min-h-screen" style={{ backgroundColor: 'var(--bg-primary)' }} role="main" aria-label="Edit profile page">
      {/* Screen reader announcements */}
      <div aria-live="polite" aria-atomic="true" className="sr-only">
        {error && `Error: ${error}`}
        {success && `Success: ${success}`}
        {saving && 'Saving profile changes...'}
      </div>

      <div className="max-w-5xl mx-auto px-4 sm:px-6 py-8 sm:py-12 md:py-16">
        {/* Header - Enhanced Apple Style - Mobile Optimized */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 sm:gap-0 mb-8 sm:mb-12">
          <div className="flex items-center gap-3 sm:gap-6">
            <button
              onClick={() => navigate('/dashboard')}
              className="flex items-center gap-2 sm:gap-3 px-4 sm:px-6 py-2 sm:py-3 rounded-lg sm:rounded-xl font-semibold transition-all duration-200 shadow-md hover:scale-105 focus:outline-none focus:ring-4 focus:ring-opacity-30 text-sm sm:text-base"
              style={{
                backgroundColor: 'var(--bg-primary)',
                color: 'var(--text-primary)',
                border: '2px solid var(--border-light)',
                boxShadow: 'var(--shadow-light)',
                fontWeight: '600'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor = 'var(--bg-primary)'
                e.currentTarget.style.borderColor = 'var(--primary)'
                e.currentTarget.style.boxShadow = 'var(--shadow-medium)'
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = 'var(--bg-primary)'
                e.currentTarget.style.boxShadow = 'var(--shadow-light)'
              }}
            >
              <ArrowLeft className="h-4 w-4 sm:h-5 sm:w-5" />
              <span className="hidden sm:inline">Dashboard</span>
              <span className="sm:hidden">Back</span>
            </button>
            <div className="flex items-center gap-3 sm:gap-4">
              <div className="w-10 h-10 sm:w-12 sm:h-12 rounded-xl sm:rounded-2xl flex items-center justify-center" style={{ backgroundColor: 'var(--primary)', boxShadow: 'var(--shadow-medium)' }}>
                <User className="h-5 w-5 sm:h-7 sm:w-7" style={{ color: 'var(--bg-primary)' }} />
              </div>
              <div>
                <h1 className="text-4xl font-light tracking-tight leading-tight" style={{
                  color: 'var(--text-primary)',
                  fontWeight: '300',
                  letterSpacing: '-0.025em'
                }}>Edit Profile</h1>
                <p className="text-lg font-normal mt-1" style={{
                  color: 'var(--text-secondary)',
                  fontWeight: '400'
                }}>Update your personal information and preferences</p>
              </div>
            </div>
          </div>
        </div>

        <div className="rounded-2xl sm:rounded-3xl p-6 sm:p-8 md:p-12" style={{ backgroundColor: 'var(--bg-primary)', border: 'none', boxShadow: 'var(--shadow-card-hover)', backdropFilter: 'blur(10px)' }}>
          <div className="mb-6 sm:mb-8 md:mb-10">
          </div>

          {error && (
            <div className="mb-4 sm:mb-6 p-3 sm:p-4 rounded-lg border" style={{ backgroundColor: 'var(--bg-error)', borderColor: 'var(--border-error)' }} role="alert" aria-live="polite">
              <p className="text-sm macos-body" style={{ color: 'var(--text-error)' }}>{error}</p>
            </div>
          )}

          {success && (
            <div className="mb-4 sm:mb-6 p-3 sm:p-4 rounded-lg border" style={{ backgroundColor: 'var(--bg-success)', borderColor: 'var(--success)' }} role="status" aria-live="polite">
              <p className="text-sm macos-body" style={{ color: 'var(--text-success)' }}>{success}</p>
            </div>
          )}

          <form onSubmit={handleSave} onKeyDown={handleKeyDown} className="space-y-6 sm:space-y-8" role="form" aria-label="Edit profile form">
            {/* Basic Information */}
            <fieldset className="border-0 p-0 m-0">
              <legend className="text-base sm:text-lg font-semibold mb-3 sm:mb-4" style={{ color: 'var(--text-primary)' }}>Basic Information</legend>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6">
              <div>
                <label htmlFor="first_name" className="block text-sm font-medium mb-2 macos-body" style={{ color: 'var(--text-primary)' }}>
                  First Name
                </label>
                <input
                  id="first_name"
                  name="first_name"
                  type="text"
                  required
                  value={formData.first_name}
                  onChange={handleInputChange}
                  className="w-full px-3 sm:px-4 py-2 sm:py-3 rounded-lg sm:rounded-xl border transition-all duration-200 focus:outline-none text-sm sm:text-base"
                  style={{
                    backgroundColor: 'var(--bg-primary)',
                    borderColor: validationErrors.first_name ? 'var(--error)' : 'var(--border-light)',
                    color: 'var(--text-primary)',
                    boxShadow: 'var(--shadow-input)',
                    fontSize: '16px',
                    fontWeight: '400'
                  }}
                  onFocus={(e) => {
                    e.currentTarget.style.borderColor = 'var(--primary)'
                    e.currentTarget.style.boxShadow = 'var(--focus-shadow)'
                    e.currentTarget.style.transform = 'scale(1.01)'
                  }}
                  onBlur={(e) => {
                    e.currentTarget.style.borderColor = validationErrors.first_name ? 'var(--error)' : 'var(--border-light)'
                    e.currentTarget.style.boxShadow = 'var(--shadow-input)'
                    e.currentTarget.style.transform = 'scale(1)'
                  }}
                  onMouseEnter={(e) => {
                    if (e.currentTarget !== document.activeElement) {
                      e.currentTarget.style.borderColor = 'var(--border-medium)'
                      e.currentTarget.style.boxShadow = 'var(--shadow-hover)'
                    }
                  }}
                  onMouseLeave={(e) => {
                    if (e.currentTarget !== document.activeElement) {
                      e.currentTarget.style.borderColor = validationErrors.first_name ? 'var(--error)' : 'var(--border-light)'
                      e.currentTarget.style.boxShadow = 'var(--shadow-input)'
                    }
                  }}
                  aria-describedby={validationErrors.first_name ? 'first_name_error' : undefined}
                  aria-invalid={!!validationErrors.first_name}
                />
                {validationErrors.first_name && (
                  <p id="first_name_error" className="text-sm mt-1" style={{ color: 'var(--error)' }} role="alert">
                    {validationErrors.first_name}
                  </p>
                )}
              </div>

              <div>
                <label htmlFor="last_name" className="block text-sm font-medium mb-2 macos-body" style={{ color: 'var(--text-primary)' }}>
                  Last Name
                </label>
                <input
                  id="last_name"
                  name="last_name"
                  type="text"
                  required
                  value={formData.last_name}
                  onChange={handleInputChange}
                  className="w-full px-4 py-3 rounded-xl border transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-opacity-50 hover:border-gray-400"
                  style={{
                    backgroundColor: 'var(--bg-primary)',
                    borderColor: validationErrors.last_name ? 'var(--error)' : 'var(--border-light)',
                    color: 'var(--text-primary)',
                    boxShadow: 'var(--shadow-input)',
                    fontSize: '16px',
                    fontWeight: '400'
                  }}
                  onFocus={(e) => {
                    e.currentTarget.style.borderColor = 'var(--primary)'
                    e.currentTarget.style.boxShadow = 'var(--shadow-input-focus)'
                  }}
                  onBlur={(e) => {
                    e.currentTarget.style.borderColor = validationErrors.last_name ? 'var(--error)' : 'var(--border-light)'
                    e.currentTarget.style.boxShadow = 'var(--shadow-input)'
                  }}
                  aria-describedby={validationErrors.last_name ? 'last_name_error' : undefined}
                  aria-invalid={!!validationErrors.last_name}
                />
                {validationErrors.last_name && (
                  <p id="last_name_error" className="text-sm mt-1" style={{ color: 'var(--error)' }} role="alert">
                    {validationErrors.last_name}
                  </p>
                )}
              </div>
              </div>
            </fieldset>

            {/* Contact Information - Enhanced Apple Style */}
            <fieldset className="border-0 p-0 m-0">
              <legend 
                className="text-lg font-semibold mb-6 macos-heading" 
                style={{ 
                  color: 'var(--text-primary)',
                  fontSize: '20px',
                  letterSpacing: '0.025em'
                }}
              >
                Contact Information
              </legend>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div className="space-y-3">
                <label 
                  htmlFor="email" 
                  className="block text-sm font-semibold mb-3 macos-body" 
                  style={{ 
                    color: 'var(--text-primary)',
                    fontSize: '15px',
                    fontWeight: '600'
                  }}
                >
                  Email Address
                </label>
                <div className="relative">
                  <Mail className="absolute left-4 top-4 h-5 w-5" style={{ color: 'var(--text-muted)' }} />
                  <input
                    id="email"
                    name="email"
                    type="email"
                    value={formData.email}
                    disabled
                    className="w-full pl-12 pr-4 py-4 rounded-xl border-2 transition-all duration-200 font-medium"
                    style={{
                      backgroundColor: 'var(--bg-primary)',
                      borderColor: 'var(--border-light)',
                      color: 'var(--text-muted)',
                      fontSize: '16px',
                      boxShadow: 'inset 0 1px 3px rgba(0,0,0,0.1)'
                    }}
                  />
                </div>
                <p 
                  className="text-xs mt-2" 
                  style={{ 
                    color: 'var(--text-muted)',
                    fontSize: '13px'
                  }}
                >
                  Email cannot be changed
                </p>
              </div>

              <div className="space-y-3">
                <label 
                  htmlFor="phone" 
                  className="block text-sm font-semibold mb-3 macos-body" 
                  style={{ 
                    color: 'var(--text-primary)',
                    fontSize: '15px',
                    fontWeight: '600'
                  }}
                >
                  Phone Number
                </label>
                <div className="relative">
                  <Phone className="absolute left-4 top-4 h-5 w-5" style={{ color: 'var(--text-muted)' }} />
                  <input
                    id="phone"
                    name="phone"
                    type="tel"
                    value={formData.phone}
                    onChange={handleInputChange}
                    className="w-full pl-12 pr-4 py-4 rounded-xl border-2 focus:outline-none transition-all duration-200 font-medium shadow-sm hover:shadow-md focus:shadow-lg"
                    style={{
                      backgroundColor: 'var(--bg-primary)',
                      borderColor: validationErrors.phone ? 'var(--error)' : 'var(--border-medium)',
                      color: 'var(--text-primary)',
                      fontSize: '16px',
                      boxShadow: 'var(--shadow-light)'
                    }}
                    onFocus={(e) => {
                      e.currentTarget.style.borderColor = 'var(--primary)'
                      e.currentTarget.style.boxShadow = 'var(--shadow-medium)'
                    }}
                    onBlur={(e) => {
                      e.currentTarget.style.borderColor = validationErrors.phone ? 'var(--error)' : 'var(--border-medium)'
                      e.currentTarget.style.boxShadow = 'var(--shadow-light)'
                    }}
                    placeholder="(*************"
                    aria-describedby={validationErrors.phone ? 'phone_error' : undefined}
                    aria-invalid={!!validationErrors.phone}
                  />
                </div>
                {validationErrors.phone && (
                  <p id="phone_error" className="text-sm mt-2" style={{ color: 'var(--error)', fontSize: '14px' }} role="alert">
                    {validationErrors.phone}
                  </p>
                )}
              </div>
              </div>
            </fieldset>

            {/* Additional Information - Enhanced Apple Style */}
            <fieldset className="border-0 p-0 m-0">
              <legend 
                className="text-lg font-semibold mb-6 macos-heading" 
                style={{ 
                  color: 'var(--text-primary)',
                  fontSize: '20px',
                  letterSpacing: '0.025em'
                }}
              >
                Additional Information
              </legend>
              <div className="space-y-8">
              <div className="space-y-3">
                <label 
                  htmlFor="location" 
                  className="block text-sm font-semibold mb-3 macos-body" 
                  style={{ 
                    color: 'var(--text-primary)',
                    fontSize: '15px',
                    fontWeight: '600'
                  }}
                >
                  Location
                </label>
                <div className="relative">
                  <MapPin className="absolute left-4 top-4 h-5 w-5" style={{ color: 'var(--text-muted)' }} />
                  <input
                    id="location"
                    name="location"
                    type="text"
                    value={formData.location}
                    onChange={handleInputChange}
                    className="w-full pl-12 pr-4 py-4 rounded-xl border-2 focus:outline-none transition-all duration-200 font-medium shadow-sm hover:shadow-md focus:shadow-lg"
                    style={{
                      backgroundColor: 'var(--bg-primary)',
                      borderColor: 'var(--border-medium)',
                      color: 'var(--text-primary)',
                      fontSize: '16px',
                      boxShadow: 'var(--shadow-light)'
                    }}
                    onFocus={(e) => {
                      e.currentTarget.style.borderColor = 'var(--primary)'
                      e.currentTarget.style.boxShadow = 'var(--shadow-medium)'
                    }}
                    onBlur={(e) => {
                      e.currentTarget.style.borderColor = 'var(--border-medium)'
                      e.currentTarget.style.boxShadow = 'var(--shadow-light)'
                    }}
                    placeholder="City, State"
                  />
                </div>
              </div>

              <div className="space-y-3">
                <label 
                  htmlFor="bio" 
                  className="block text-sm font-semibold mb-3 macos-body" 
                  style={{ 
                    color: 'var(--text-primary)',
                    fontSize: '15px',
                    fontWeight: '600'
                  }}
                >
                  Bio
                </label>
                <textarea
                  id="bio"
                  name="bio"
                  rows={5}
                  value={formData.bio}
                  onChange={handleInputChange}
                  className="w-full px-4 py-4 rounded-xl border-2 focus:outline-none transition-all duration-200 resize-none font-medium shadow-sm hover:shadow-md focus:shadow-lg"
                  style={{
                    backgroundColor: 'var(--bg-primary)',
                    borderColor: 'var(--border-medium)',
                    color: 'var(--text-primary)',
                    fontSize: '16px',
                    lineHeight: '1.6',
                    boxShadow: 'var(--shadow-light)'
                  }}
                  onFocus={(e) => {
                    e.currentTarget.style.borderColor = 'var(--primary)'
                    e.currentTarget.style.boxShadow = 'var(--shadow-medium)'
                  }}
                  onBlur={(e) => {
                    e.currentTarget.style.borderColor = 'var(--border-medium)'
                    e.currentTarget.style.boxShadow = 'var(--shadow-light)'
                  }}
                  placeholder="Tell us about yourself..."
                />
              </div>

              <div className="space-y-3">
                <label 
                  htmlFor="date_of_birth" 
                  className="block text-sm font-semibold mb-3 macos-body" 
                  style={{ 
                    color: 'var(--text-primary)',
                    fontSize: '15px',
                    fontWeight: '600'
                  }}
                >
                  Date of Birth
                </label>
                <div className="relative">
                  <Calendar className="absolute left-4 top-4 h-5 w-5" style={{ color: 'var(--text-muted)' }} />
                  <input
                    id="date_of_birth"
                    name="date_of_birth"
                    type="date"
                    value={formData.date_of_birth}
                    onChange={handleInputChange}
                    className="w-full pl-12 pr-4 py-4 rounded-xl border-2 focus:outline-none transition-all duration-200 font-medium shadow-sm hover:shadow-md focus:shadow-lg"
                    style={{
                      backgroundColor: 'var(--bg-primary)',
                      borderColor: 'var(--border-medium)',
                      color: 'var(--text-primary)',
                      fontSize: '16px',
                      boxShadow: 'var(--shadow-light)'
                    }}
                    onFocus={(e) => {
                      e.currentTarget.style.borderColor = 'var(--primary)'
                      e.currentTarget.style.boxShadow = 'var(--shadow-medium)'
                    }}
                    onBlur={(e) => {
                      e.currentTarget.style.borderColor = 'var(--border-medium)'
                      e.currentTarget.style.boxShadow = 'var(--shadow-light)'
                    }}
                  />
                </div>
              </div>
              </div>
            </fieldset>

            {/* Action Buttons - Enhanced Apple Style */}
            <div className="flex justify-between pt-8">
              <button
                type="button"
                onClick={handleReset}
                disabled={saving}
                className="flex items-center gap-3 px-10 py-4 rounded-xl font-semibold transition-all duration-200 shadow-md hover:scale-105 focus:outline-none focus:ring-4 focus:ring-opacity-30 disabled:opacity-50 disabled:hover:scale-100"
                style={{
                  backgroundColor: 'var(--bg-primary)',
                  color: 'var(--text-primary)',
                  border: '2px solid var(--border-light)',
                  boxShadow: 'var(--shadow-light)',
                  fontSize: '16px',
                  fontWeight: '600',
                  letterSpacing: '0.025em'
                }}
                onMouseEnter={(e) => {
                  if (!saving) {
                    e.currentTarget.style.backgroundColor = 'var(--bg-primary)'
                    e.currentTarget.style.borderColor = 'var(--primary)'
                    e.currentTarget.style.boxShadow = 'var(--shadow-medium)'
                  }
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = 'var(--bg-primary)'
                  e.currentTarget.style.boxShadow = 'var(--shadow-light)'
                }}
                aria-label="Reset form to original values"
              >
                Reset Changes
              </button>

              <button
                type="submit"
                disabled={saving}
                className="flex items-center gap-3 px-10 py-4 rounded-xl font-semibold transition-all duration-200 shadow-lg hover:scale-105 focus:outline-none focus:ring-4 focus:ring-opacity-30 disabled:opacity-50 disabled:hover:scale-100"
                style={{
                  backgroundColor: saving ? 'var(--text-muted)' : 'var(--primary)',
                  color: 'var(--bg-primary)',
                  boxShadow: 'var(--shadow-medium)',
                  fontSize: '16px',
                  fontWeight: '600',
                  letterSpacing: '0.025em'
                }}
                onMouseEnter={(e) => {
                  if (!saving) {
                    e.currentTarget.style.backgroundColor = 'var(--primary-hover)'
                    e.currentTarget.style.boxShadow = 'var(--shadow-card-hover)'
                  }
                }}
                onMouseLeave={(e) => {
                  if (!saving) {
                    e.currentTarget.style.backgroundColor = 'var(--primary)'
                    e.currentTarget.style.boxShadow = 'var(--shadow-medium)'
                  }
                }}
                aria-label="Save profile changes"
              >
                {saving ? (
                  <>
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2" style={{ borderColor: 'var(--bg-primary)' }}></div>
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="h-5 w-5" />
                    Save Changes
                  </>
                )}
              </button>
            </div>
          </form>
        </div>
      </div>
    </main>
  )
}
