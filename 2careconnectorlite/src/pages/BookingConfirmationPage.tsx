import React, { useState, useEffect } from 'react';
import { Link, useParams, useNavigate } from 'react-router-dom';
import { supabase } from '../lib/supabase';
import { Loader2, CheckCircle, Calendar as CalendarIcon, User, Clock, MapPin, DollarSign, MessageSquare, ArrowRight, Download, Share2 } from 'lucide-react';
import { format, parseISO } from 'date-fns';

interface BookingConfirmation {
  id: string;
  user_id: string;
  service_provider_id: string;
  provider_type?: string;
  service_details: string;
  booking_start_time: string;
  booking_end_time: string;
  status: string;
  total_cost: number;
  notes?: string;
  location?: string;
  created_at: string;
  duration_hours: number;
  provider_name?: string;
  provider_email?: string;
  provider_phone?: string;
  confirmation_number?: string;
}

const BookingConfirmationPage: React.FC = () => {
  const { bookingId } = useParams<{ bookingId: string }>();
  const navigate = useNavigate();
  
  const [booking, setBooking] = useState<BookingConfirmation | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchBookingConfirmation = async () => {
      if (!bookingId) {
        setError("Invalid booking ID.");
        setLoading(false);
        return;
      }

      setLoading(true);
      try {
        // Get current user
        const { data: { user } } = await supabase.auth.getUser();
        
        if (!user) {
          setError("Please sign in to view booking confirmation.");
          setLoading(false);
          return;
        }

        // Fetch booking confirmation details
        const { data, error: fetchError } = await supabase
          .schema('care_connector')
          .from('service_provider_bookings')
          .select('*')
          .eq('id', bookingId)
          .eq('user_id', user.id)
          .single();

        if (fetchError) {
          console.error('Supabase fetch error:', fetchError);
          throw fetchError;
        }

        if (!data) {
          setError("Booking confirmation not found.");
          setLoading(false);
          return;
        }

        // Fetch provider details
        const { data: providerData, error: providerError } = await supabase
          .schema('care_connector')
          .from('profiles')
          .select('full_name, email, phone')
          .eq('id', data.provider_id)
          .single();

        if (providerError) {
          console.warn('Could not fetch provider details:', providerError);
        }

        const bookingWithProvider = {
          ...data,
          provider_name: providerData?.full_name || 'Provider',
          provider_email: providerData?.email || '',
          provider_phone: providerData?.phone || '',
          confirmation_number: `CC-${data.id.slice(0, 8).toUpperCase()}`
        };

        console.log('Booking confirmation data fetched successfully:', bookingWithProvider);
        setBooking(bookingWithProvider);
      } catch (err: any) {
        console.error("Error fetching booking confirmation:", err.message);
        setError(err.message || "Failed to load booking confirmation.");
        setBooking(null);
      } finally {
        setLoading(false);
      }
    };

    fetchBookingConfirmation();
  }, [bookingId]);

  const handleDownloadConfirmation = () => {
    if (!booking) return;
    
    // Simple text-based confirmation download
    const confirmationText = `
CARE CONNECTOR BOOKING CONFIRMATION
==================================

Confirmation Number: ${booking.confirmation_number}
Booking Date: ${format(new Date(booking.created_at), 'MMMM d, yyyy')}

SERVICE DETAILS:
- Service Type: ${booking.service_details}
- Provider Type: ${booking.provider_type}
- Provider Name: ${booking.provider_name}

APPOINTMENT DETAILS:
- Date: ${format(parseISO(booking.booking_start_time), 'MMMM d, yyyy')}
- Time: ${format(parseISO(booking.booking_start_time), 'h:mm a')} - ${format(parseISO(booking.booking_end_time), 'h:mm a')}
- Duration: ${((new Date(booking.booking_end_time).getTime() - new Date(booking.booking_start_time).getTime()) / (1000 * 60 * 60)).toFixed(1)} hours
- Location: ${booking.location || 'Not specified'}

COST:
- Total Cost: $${booking.total_cost}
- Status: ${booking.status}

SPECIAL REQUIREMENTS:
${booking.notes || 'None specified'}

CONTACT INFORMATION:
- Provider Email: ${booking.provider_email}
- Provider Phone: ${booking.provider_phone}

Thank you for choosing Care Connector!
For support, contact <NAME_EMAIL>
    `;
    
    const blob = new Blob([confirmationText], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `booking-confirmation-${booking.confirmation_number}.txt`;
    link.click();
    URL.revokeObjectURL(url);
  };

  const handleShareConfirmation = async () => {
    if (!booking) return;
    
    const shareText = `My Care Connector booking is confirmed! 
Confirmation #: ${booking.confirmation_number}
Service: ${booking.service_details}
Date: ${format(parseISO(booking.booking_start_time), 'MMMM d, yyyy')} at ${format(parseISO(booking.booking_start_time), 'h:mm a')}
Provider: ${booking.provider_name}`;
    
    if (navigator.share) {
      try {
        await navigator.share({
          title: 'Care Connector Booking Confirmation',
          text: shareText,
        });
      } catch (err) {
        console.log('Error sharing:', err);
      }
    } else {
      // Fallback: copy to clipboard
      navigator.clipboard.writeText(shareText);
      alert('Booking confirmation copied to clipboard!');
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center" style={{ backgroundColor: 'var(--bg-primary)' }}>
        <div className="flex items-center space-x-3">
          <Loader2 className="h-6 w-6 animate-spin text-var(--logo-green)" />
          <span className="style={{color: 'var(--text-secondary)'}} font-medium">Loading booking confirmation...</span>
        </div>
      </div>
    );
  }

  if (error || !booking) {
    return (
      <div className="min-h-screen flex items-center justify-center" style={{ backgroundColor: 'var(--bg-primary)' }}>
        <div className="rounded-2xl shadow-sm p-8 max-w-md w-full mx-4" style={{ backgroundColor: 'var(--bg-primary)', borderColor: 'var(--border-light)' }}>
          <div className="text-center">
            <CheckCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h2 className="text-xl font-semibold style={{color: 'var(--text-primary)'}} mb-2">Confirmation Not Found</h2>
            <p className="style={{color: 'var(--text-secondary)'}} mb-6">{error}</p>
            <button
              onClick={() => navigate('/my-bookings')}
              className="button-primary px-6 py-3 rounded-xl font-medium transition-colors hover:opacity-90"
            >
              View My Bookings
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen" style={{ backgroundColor: 'var(--bg-primary)' }}>
      {/* Header */}
      <div style={{ backgroundColor: 'var(--bg-primary)', borderBottomColor: 'var(--border-light)' }} className="border-b">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="text-center">
            <div className="mb-4">
              <CheckCircle className="h-16 w-16 text-var(--logo-green) mx-auto" />
            </div>
            <h1 className="text-3xl font-bold style={{color: 'var(--text-primary)'}} mb-2">Booking Confirmed!</h1>
            <p className="text-lg style={{color: 'var(--text-secondary)'}}">Your care service has been successfully booked</p>
            <p className="text-sm  mt-2">
              Confirmation Number: <span className="font-mono font-semibold text-var(--logo-green)">{booking.confirmation_number}</span>
            </p>
          </div>
        </div>
      </div>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        
        {/* Main Confirmation Card */}
        <div className="rounded-2xl shadow-sm p-8 mb-8" style={{ backgroundColor: 'var(--bg-primary)', borderColor: 'var(--border-light)' }}>
          
          {/* Service Info */}
          <div className="mb-8">
            <h2 className="text-xl font-semibold style={{color: 'var(--text-primary)'}} mb-4">Service Details</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <p className="text-sm  mb-1">Service Type</p>
                <p className="text-lg font-medium style={{color: 'var(--text-primary)'}}">{booking.service_details}</p>
              </div>
              <div>
                <p className="text-sm  mb-1">Provider Type</p>
                <p className="text-lg font-medium style={{color: 'var(--text-primary)'}} capitalize">{booking.provider_type}</p>
              </div>
              <div>
                <p className="text-sm  mb-1">Provider Name</p>
                <p className="text-lg font-medium style={{color: 'var(--text-primary)'}}">{booking.provider_name}</p>
              </div>
              <div>
                <p className="text-sm  mb-1">Status</p>
                <span className="px-3 py-1 rounded-full text-sm font-medium" style={{ backgroundColor: 'var(--primary)', color: 'var(--bg-primary)' }}>
                  {booking.status === 'confirmed' ? 'Confirmed' : booking.status}
                </span>
              </div>
            </div>
          </div>

          {/* Appointment Details */}
          <div className="mb-8">
            <h2 className="text-xl font-semibold style={{color: 'var(--text-primary)'}} mb-4">Appointment Details</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="flex items-center">
                <CalendarIcon className="h-5 w-5  mr-3" />
                <div>
                  <p className="text-sm ">Date</p>
                  <p className="font-medium style={{color: 'var(--text-primary)'}}">{format(parseISO(booking.booking_start_time), 'MMMM d, yyyy')}</p>
                  <p className="text-sm ">{format(parseISO(booking.booking_start_time), 'EEEE')}</p>
                </div>
              </div>
              
              <div className="flex items-center">
                <Clock className="h-5 w-5  mr-3" />
                <div>
                  <p className="text-sm ">Time</p>
                  <p className="font-medium style={{color: 'var(--text-primary)'}}">
                    {format(parseISO(booking.booking_start_time), 'h:mm a')} - {format(parseISO(booking.booking_end_time), 'h:mm a')}
                  </p>
                  <p className="text-sm ">
                    {((new Date(booking.booking_end_time).getTime() - new Date(booking.booking_start_time).getTime()) / (1000 * 60 * 60)).toFixed(1)} hours
                  </p>
                </div>
              </div>
              
              <div className="flex items-center">
                <DollarSign className="h-5 w-5  mr-3" />
                <div>
                  <p className="text-sm ">Total Cost</p>
                  <p className="font-medium style={{color: 'var(--text-primary)'}}">${booking.total_cost}</p>
                  <p className="text-sm ">Payment pending</p>
                </div>
              </div>
            </div>
            
            {booking.location && (
              <div className="mt-6 flex items-center">
                <MapPin className="h-5 w-5  mr-3" />
                <div>
                  <p className="text-sm ">Location</p>
                  <p className="font-medium style={{color: 'var(--text-primary)'}}">{booking.location}</p>
                </div>
              </div>
            )}
          </div>

          {/* Special Requirements */}
          {booking.notes && (
            <div className="mb-8">
              <h2 className="text-xl font-semibold style={{color: 'var(--text-primary)'}} mb-4">Special Requirements</h2>
              <div className="border p-4 rounded-xl" style={{ backgroundColor: 'var(--bg-primary)', borderColor: 'var(--border-light)' }}>
                <p className="style={{color: 'var(--text-primary)'}}">{booking.notes}</p>
              </div>
            </div>
          )}

          {/* Provider Contact */}
          <div className="mb-8">
            <h2 className="text-xl font-semibold style={{color: 'var(--text-primary)'}} mb-4">Provider Contact</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {booking.provider_email && (
                <div>
                  <p className="text-sm  mb-1">Email</p>
                  <p className="font-medium style={{color: 'var(--text-primary)'}}">{booking.provider_email}</p>
                </div>
              )}
              {booking.provider_phone && (
                <div>
                  <p className="text-sm  mb-1">Phone</p>
                  <p className="font-medium style={{color: 'var(--text-primary)'}}">{booking.provider_phone}</p>
                </div>
              )}
            </div>
          </div>

          {/* Actions */}
          <div className="flex flex-col sm:flex-row gap-4">
            <button
              onClick={handleDownloadConfirmation}
              className="flex items-center justify-center px-6 py-3 border text-gray-700 rounded-xl font-medium hover:shadow-sm transition-colors"
              style={{ backgroundColor: 'var(--bg-primary)', borderColor: 'var(--border-light)' }}
              onMouseEnter={(e) => { e.currentTarget.style.borderColor = 'var(--primary)'; e.currentTarget.style.boxShadow = 'var(--shadow-card)' }}
              onMouseLeave={(e) => { e.currentTarget.style.borderColor = 'var(--border-light)'; e.currentTarget.style.boxShadow = 'none' }}
            >
              <Download className="mr-2 h-4 w-4" />
              Download Confirmation
            </button>
            
            <button
              onClick={handleShareConfirmation}
              className="flex items-center justify-center px-6 py-3 rounded-xl font-medium transition-colors"
              style={{ backgroundColor: 'var(--secondary)', color: 'var(--bg-primary)' }}
              onMouseEnter={(e) => e.currentTarget.style.backgroundColor = 'var(--secondary-dark)'}
              onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'var(--secondary)'}
            >
              <Share2 className="mr-2 h-4 w-4" />
              Share Confirmation
            </button>
            
            <Link
              to={`/messages/new?recipientId=${booking.service_provider_id}&providerName=${encodeURIComponent(booking.provider_name)}`}
              className="flex items-center justify-center px-6 py-3 rounded-xl font-medium transition-colors"
              style={{ backgroundColor: 'var(--primary)', color: 'var(--bg-primary)' }}
              onMouseEnter={(e) => e.currentTarget.style.backgroundColor = 'var(--primary)'}
              onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'var(--primary)'}
            >
              <MessageSquare className="mr-2 h-4 w-4" />
              Message Provider
            </Link>
          </div>
        </div>

        {/* Next Steps */}
        <div className="rounded-2xl shadow-sm p-6 mb-8" style={{ backgroundColor: 'var(--bg-primary)', borderColor: 'var(--border-light)' }}>
          <h2 className="text-xl font-semibold style={{color: 'var(--text-primary)'}} mb-4">What's Next?</h2>
          <div className="space-y-4">
            <div className="flex items-start">
              <div className="flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center mr-3" style={{ backgroundColor: 'var(--primary)' }}>
                <span className="text-sm font-medium" style={{ color: 'var(--bg-primary)' }}>1</span>
              </div>
              <div>
                <p className="font-medium style={{color: 'var(--text-primary)'}}">Payment Processing</p>
                <p className="style={{color: 'var(--text-secondary)'}} text-sm">Your payment will be processed automatically before the appointment.</p>
              </div>
            </div>
            
            <div className="flex items-start">
              <div className="flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center mr-3" style={{ backgroundColor: 'var(--primary)' }}>
                <span className="text-sm font-medium" style={{ color: 'var(--bg-primary)' }}>2</span>
              </div>
              <div>
                <p className="font-medium style={{color: 'var(--text-primary)'}}">Provider Contact</p>
                <p className="style={{color: 'var(--text-secondary)'}} text-sm">Your provider will contact you 24 hours before the appointment to confirm details.</p>
              </div>
            </div>
            
            <div className="flex items-start">
              <div className="flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center mr-3" style={{ backgroundColor: 'var(--primary)' }}>
                <span className="text-sm font-medium" style={{ color: 'var(--bg-primary)' }}>3</span>
              </div>
              <div>
                <p className="font-medium style={{color: 'var(--text-primary)'}}">Appointment Day</p>
                <p className="style={{color: 'var(--text-secondary)'}} text-sm">Be ready at the scheduled time. Your provider will arrive as confirmed.</p>
              </div>
            </div>
          </div>
        </div>

        {/* Navigation */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Link
            to="/my-bookings"
            className="flex items-center justify-center px-8 py-3 border text-gray-700 rounded-xl font-medium hover:shadow-sm transition-colors"
            style={{ backgroundColor: 'var(--bg-primary)', borderColor: 'var(--border-light)' }}
            onMouseEnter={(e) => { e.currentTarget.style.borderColor = 'var(--primary)'; e.currentTarget.style.boxShadow = 'var(--shadow-card)' }}
            onMouseLeave={(e) => { e.currentTarget.style.borderColor = 'var(--border-light)'; e.currentTarget.style.boxShadow = 'none' }}
          >
            View All Bookings
          </Link>
          
          <Link
            to="/dashboard"
            className="flex items-center justify-center px-8 py-3 rounded-xl font-medium transition-colors"
            style={{ backgroundColor: 'var(--primary)', color: 'var(--bg-primary)' }}
            onMouseEnter={(e) => e.currentTarget.style.backgroundColor = 'var(--primary)'}
            onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'var(--primary)'}
          >
            Go to Dashboard
            <ArrowRight className="ml-2 h-4 w-4" />
          </Link>
        </div>
      </div>
    </div>
  );
};

export default BookingConfirmationPage;
