import React, { useState, useEffect } from 'react'
import { CheckSquare, Plus, Clock, User, Flag, Calendar, Filter, X } from 'lucide-react'
import { dataService } from '../lib/dataService'

export default function TaskManagement() {
  const [activeTab, setActiveTab] = useState('all')
  const [showCompleted, setShowCompleted] = useState(false)
  const [showNewTaskModal, setShowNewTaskModal] = useState(false)
  const [showFilterModal, setShowFilterModal] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')
  const [priorityFilter, setPriorityFilter] = useState('')
  const [assigneeFilter, setAssigneeFilter] = useState('')
  const [newTask, setNewTask] = useState({
    title: '',
    description: '',
    dueTime: '',
    assignee: '',
    priority: 'medium',
    category: 'medication',
    recurring: false
  })

  // Dynamic tasks loaded from database - NO HARDCODED DATA
  const [tasks, setTasks] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  // Load tasks from database
  useEffect(() => {
    const fetchTasks = async () => {
      try {
        setLoading(true)
        setError(null)
        // Query tasks from database instead of hardcoded data
        const tasksData = await dataService.getTasks()
        setTasks(tasksData || [])
      } catch (error) {
        console.error('Error fetching tasks:', error)
        setError('Failed to load tasks. Please try again.')
        setTasks([])
      } finally {
        setLoading(false)
      }
    }
    
    fetchTasks()
  }, [])

  const categories = [
    { id: 'all', name: 'All Tasks', count: tasks.length },
    { id: 'medication', name: 'Medication', count: tasks.filter(t => t.category === 'medication').length },
    { id: 'therapy', name: 'Therapy', count: tasks.filter(t => t.category === 'therapy').length },
    { id: 'monitoring', name: 'Monitoring', count: tasks.filter(t => t.category === 'monitoring').length },
    { id: 'errands', name: 'Errands', count: tasks.filter(t => t.category === 'errands').length }
  ]

  const filteredTasks = tasks.filter(task => {
    // Category filter
    if (activeTab !== 'all' && task.category !== activeTab) return false

    // Completed filter
    if (!showCompleted && task.completed) return false

    // Search query filter
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase()
      const matchesTitle = task.title.toLowerCase().includes(query)
      const matchesDescription = task.description.toLowerCase().includes(query)
      const matchesAssignee = task.assignee.toLowerCase().includes(query)
      if (!matchesTitle && !matchesDescription && !matchesAssignee) return false
    }

    // Priority filter
    if (priorityFilter && task.priority !== priorityFilter) return false

    // Assignee filter
    if (assigneeFilter && task.assignee !== assigneeFilter) return false

    return true
  })

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return { backgroundColor: 'var(--error-light)', color: 'var(--error)' }
      case 'medium': return { backgroundColor: 'var(--warning-light)', color: 'var(--warning)' }
      case 'low': return { backgroundColor: 'var(--success-light)', color: 'var(--success)' }
      default: return { backgroundColor: 'var(--bg-accent)', color: 'var(--text-secondary)' }
    }
  }

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'medication': return '💊'
      case 'therapy': return '🏃‍♀️'
      case 'monitoring': return '📊'
      case 'errands': return '🛒'
      default: return '📋'
    }
  }

  const handleCreateTask = async () => {
    // Add new task to the tasks array (in production, this would be a database call)
    const newTaskWithId = {
      ...newTask,
      id: tasks.length + 1,
      completed: false
    }
    
    // Reset form and close modal
    setNewTask({
      title: '',
      description: '',
      dueTime: '',
      assignee: '',
      priority: 'medium',
      category: 'medication',
      recurring: false
    })
    setShowNewTaskModal(false)
    
    // In production, would call Supabase to create task
    console.log('Creating new task:', newTaskWithId)
  }

  return (
    <div className="min-h-screen py-8" style={{ backgroundColor: 'var(--bg-primary)' }}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold" style={{ color: 'var(--text-primary)' }}>Task Management</h1>
              <p className="mt-2" style={{ color: 'var(--text-secondary)' }}>Organize and track daily care activities</p>
            </div>
            <button
              onClick={() => setShowNewTaskModal(true)}
              className="px-4 py-2 rounded-lg flex items-center transition-colors"
              style={{ backgroundColor: 'var(--primary)', color: 'white' }}
            >
              <Plus className="w-4 h-4 mr-2" />
              New Task
            </button>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Sidebar */}
          <div className="lg:col-span-1">
            <div className="rounded-xl p-6" style={{ backgroundColor: 'var(--bg-primary)', border: '2px solid var(--border-light)' }}>
              <h3 className="text-lg font-semibold mb-4" style={{ color: 'var(--text-primary)' }}>Categories</h3>
              <div className="space-y-2">
                {categories.map((category) => (
                  <button
                    key={category.id}
                    onClick={() => setActiveTab(category.id)}
                    className="w-full text-left px-3 py-2 rounded-lg flex items-center justify-between transition-colors"
                    style={{
                      backgroundColor: activeTab === category.id ? 'var(--bg-accent)' : 'transparent',
                      color: activeTab === category.id ? 'var(--primary)' : 'var(--text-secondary)'
                    }}
                  >
                    <span>{category.name}</span>
                    <span
                      className="text-xs px-2 py-1 rounded-full"
                      style={{ backgroundColor: 'var(--bg-accent)', color: 'var(--text-secondary)' }}
                    >
                      {category.count}
                    </span>
                  </button>
                ))}
              </div>

              <div className="mt-6 pt-6" style={{ borderTop: '1px solid var(--border-light)' }}>
                <div className="flex items-center justify-between">
                  <span className="text-sm" style={{ color: 'var(--text-primary)' }}>Show Completed</span>
                  <button
                    onClick={() => setShowCompleted(!showCompleted)}
                    className="relative inline-flex h-6 w-11 items-center rounded-full transition-colors"
                    style={{ backgroundColor: showCompleted ? 'var(--primary)' : 'var(--bg-accent)' }}
                  >
                    <span
                      className={`inline-block h-4 w-4 transform rounded-full transition-transform ${
                        showCompleted ? 'translate-x-6' : 'translate-x-1'
                      }`}
                      style={{ backgroundColor: 'white' }}
                    />
                  </button>
                </div>
              </div>
            </div>

            {/* Quick Stats */}
            <div className="rounded-xl p-6 mt-6" style={{ backgroundColor: 'var(--bg-primary)', border: '2px solid var(--border-light)' }}>
              <h3 className="text-lg font-semibold mb-4" style={{ color: 'var(--text-primary)' }}>Today's Progress</h3>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm" style={{ color: 'var(--text-secondary)' }}>Completed</span>
                  <span className="font-medium" style={{ color: 'var(--success)' }}>
                    {tasks.filter(t => t.completed).length}/{tasks.length}
                  </span>
                </div>
                <div className="w-full rounded-full h-2" style={{ backgroundColor: 'var(--bg-accent)' }}>
                  <div
                    className="h-2 rounded-full"
                    style={{
                      backgroundColor: 'var(--success)',
                      width: `${(tasks.filter(t => t.completed).length / tasks.length) * 100}%`
                    }}
                  ></div>
                </div>
                <div className="text-xs" style={{ color: 'var(--text-muted)' }}>
                  {tasks.filter(t => !t.completed && t.priority === 'high').length} high priority remaining
                </div>
              </div>
            </div>
          </div>

          {/* Task List */}
          <div className="lg:col-span-3">
            <div className="rounded-xl" style={{ backgroundColor: 'var(--bg-primary)', border: '2px solid var(--border-light)' }}>
              {/* Filter Bar */}
              <div className="p-6" style={{ borderBottom: '1px solid var(--border-light)' }}>
                <div className="flex items-center justify-between">
                  <h2 className="text-xl font-semibold" style={{ color: 'var(--text-primary)' }}>
                    {activeTab === 'all' ? 'All Tasks' : categories.find(c => c.id === activeTab)?.name}
                  </h2>
                  <div className="flex items-center space-x-3">
                    <button
                      onClick={() => setShowFilterModal(true)}
                      className="flex items-center text-sm transition-colors px-3 py-2 rounded-lg"
                      style={{ backgroundColor: 'var(--bg-accent)', color: 'var(--primary)' }}
                    >
                      <Filter className="w-4 h-4 mr-1" />
                      Filter
                    </button>
                    <button
                      className="flex items-center text-sm transition-colors px-3 py-2 rounded-lg"
                      style={{ backgroundColor: 'var(--bg-accent)', color: 'var(--primary)' }}
                    >
                      <Calendar className="w-4 h-4 mr-1" />
                      Schedule
                    </button>
                  </div>
                </div>

                {/* Search Input */}
                <div className="mt-4">
                  <input
                    type="text"
                    placeholder="Search tasks..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="w-full px-4 py-2 rounded-lg focus:outline-none focus:ring-2"
                    style={{
                      backgroundColor: 'var(--bg-accent)',
                      border: '1px solid var(--border-light)',
                      color: 'var(--text-primary)'
                    }}
                  />
                </div>
              </div>

              {/* Task Items */}
              <div style={{ borderTop: '1px solid var(--border-light)' }}>
                {filteredTasks.map((task) => (
                  <div
                    key={task.id}
                    className="p-6 transition-colors"
                    style={{ borderBottom: '1px solid var(--border-light)' }}
                  >
                    <div className="flex items-start space-x-4">
                      <button
                        className="mt-1 w-5 h-5 rounded border-2 flex items-center justify-center transition-colors"
                        style={{
                          backgroundColor: task.completed ? 'var(--success)' : 'transparent',
                          borderColor: task.completed ? 'var(--success)' : 'var(--border-light)',
                          color: task.completed ? 'white' : 'transparent'
                        }}
                      >
                        {task.completed && <CheckSquare className="w-3 h-3" />}
                      </button>

                      <div className="flex-1 min-w-0">
                        <div className="flex items-start justify-between">
                          <div>
                            <h3
                              className={`text-lg font-medium ${task.completed ? 'line-through' : ''}`}
                              style={{ color: task.completed ? 'var(--text-muted)' : 'var(--text-primary)' }}
                            >
                              {task.title}
                            </h3>
                            <p className="text-sm mt-1" style={{ color: 'var(--text-secondary)' }}>{task.description}</p>

                            <div className="flex items-center space-x-4 mt-3">
                              <div className="flex items-center text-sm" style={{ color: 'var(--text-muted)' }}>
                                <Clock className="w-4 h-4 mr-1" />
                                {task.dueTime}
                              </div>
                              <div className="flex items-center text-sm" style={{ color: 'var(--text-muted)' }}>
                                <User className="w-4 h-4 mr-1" />
                                {task.assignee}
                              </div>
                              {task.recurring && (
                                <span
                                  className="text-xs px-2 py-1 rounded"
                                  style={{ backgroundColor: 'var(--info-light)', color: 'var(--info)' }}
                                >
                                  Recurring
                                </span>
                              )}
                            </div>
                          </div>

                          <div className="flex items-center space-x-2">
                            <span
                              className="text-xs px-2 py-1 rounded"
                              style={getPriorityColor(task.priority)}
                            >
                              <Flag className="w-3 h-3 inline mr-1" />
                              {task.priority}
                            </span>
                            <span className="text-lg">
                              {getCategoryIcon(task.category)}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {filteredTasks.length === 0 && (
                <div className="p-12 text-center">
                  <CheckSquare className="w-12 h-12 mx-auto mb-4" style={{ color: 'var(--text-muted)' }} />
                  <h3 className="text-lg font-medium mb-2" style={{ color: 'var(--text-primary)' }}>No tasks found</h3>
                  <p style={{ color: 'var(--text-secondary)' }}>
                    {showCompleted
                      ? "No completed tasks in this category."
                      : "All tasks in this category are complete!"}
                  </p>
                </div>
              )}
            </div>

            {/* Quick Actions */}
            <div className="mt-6 rounded-xl p-6" style={{ backgroundColor: 'var(--bg-primary)', border: '2px solid var(--border-light)' }}>
              <h3 className="text-lg font-semibold mb-4" style={{ color: 'var(--text-primary)' }}>Quick Actions</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <button
                  onClick={() => window.location.href = '/medication-management'}
                  className="p-4 rounded-lg text-center transition-colors flex flex-col items-center gap-2"
                  style={{ backgroundColor: 'var(--bg-accent)', color: 'var(--primary)', border: '1px solid var(--primary)' }}
                >
                  <Plus className="w-5 h-5" />
                  <span className="text-sm font-medium">Add Medication Reminder</span>
                </button>
                <button className="p-4 rounded-lg text-center transition-colors flex flex-col items-center gap-2"
                  style={{ backgroundColor: 'var(--bg-accent)', color: 'var(--primary)', border: '1px solid var(--primary)' }}
                >
                  <Calendar className="w-5 h-5" />
                  <span className="text-sm font-medium">Schedule Therapy</span>
                </button>
                <button className="p-4 rounded-lg text-center transition-colors flex flex-col items-center gap-2"
                  style={{ backgroundColor: 'var(--bg-accent)', color: 'var(--primary)', border: '1px solid var(--primary)' }}
                >
                  <CheckSquare className="w-5 h-5" />
                  <span className="text-sm font-medium">Create Care Plan</span>
                </button>
              </div>
            </div>
          </div>
        </div>
        
        {/* New Task Modal */}
        {showNewTaskModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="rounded-xl p-6 w-full max-w-md mx-4" style={{ backgroundColor: 'var(--bg-primary)' }}>
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-xl font-semibold" style={{ color: 'var(--text-primary)' }}>Create New Task</h2>
                <button
                  onClick={() => setShowNewTaskModal(false)}
                  className="transition-colors"
                  style={{ color: 'var(--text-muted)' }}
                >
                  <X className="w-5 h-5" />
                </button>
              </div>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium mb-1" style={{ color: 'var(--text-primary)' }}>
                    Task Title *
                  </label>
                  <input
                    type="text"
                    value={newTask.title}
                    onChange={(e) => setNewTask({...newTask, title: e.target.value})}
                    className="w-full px-3 py-2 rounded-lg focus:outline-none focus:ring-2"
                    style={{
                      backgroundColor: 'var(--bg-primary)',
                      border: '1px solid var(--border-light)',
                      color: 'var(--text-primary)'
                    }}
                    placeholder="Enter task title"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1" style={{ color: 'var(--text-primary)' }}>
                    Description
                  </label>
                  <textarea
                    value={newTask.description}
                    onChange={(e) => setNewTask({...newTask, description: e.target.value})}
                    className="w-full px-3 py-2 rounded-lg focus:outline-none focus:ring-2 h-20 resize-none"
                    style={{
                      backgroundColor: 'var(--bg-primary)',
                      border: '1px solid var(--border-light)',
                      color: 'var(--text-primary)'
                    }}
                    placeholder="Enter task description"
                  />
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium mb-1" style={{ color: 'var(--text-primary)' }}>
                      Due Time
                    </label>
                    <input
                      type="time"
                      value={newTask.dueTime}
                      onChange={(e) => setNewTask({...newTask, dueTime: e.target.value})}
                      className="w-full px-3 py-2 rounded-lg focus:outline-none focus:ring-2"
                      style={{
                        backgroundColor: 'var(--bg-primary)',
                        border: '1px solid var(--border-light)',
                        color: 'var(--text-primary)'
                      }}
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium mb-1" style={{ color: 'var(--text-primary)' }}>
                      Assignee
                    </label>
                    <input
                      type="text"
                      value={newTask.assignee}
                      onChange={(e) => setNewTask({...newTask, assignee: e.target.value})}
                      className="w-full px-3 py-2 rounded-lg focus:outline-none focus:ring-2"
                      style={{
                        backgroundColor: 'var(--bg-primary)',
                        border: '1px solid var(--border-light)',
                        color: 'var(--text-primary)'
                      }}
                      placeholder="Assignee"
                    />
                  </div>
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium mb-1" style={{ color: 'var(--text-primary)' }}>
                      Priority
                    </label>
                    <select
                      value={newTask.priority}
                      onChange={(e) => setNewTask({...newTask, priority: e.target.value})}
                      className="w-full px-3 py-2 rounded-lg focus:outline-none focus:ring-2"
                      style={{
                        backgroundColor: 'var(--bg-primary)',
                        border: '1px solid var(--border-light)',
                        color: 'var(--text-primary)'
                      }}
                    >
                      <option value="low">Low</option>
                      <option value="medium">Medium</option>
                      <option value="high">High</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium mb-1" style={{ color: 'var(--text-primary)' }}>
                      Category
                    </label>
                    <select
                      value={newTask.category}
                      onChange={(e) => setNewTask({...newTask, category: e.target.value})}
                      className="w-full px-3 py-2 rounded-lg focus:outline-none focus:ring-2"
                      style={{
                        backgroundColor: 'var(--bg-primary)',
                        border: '1px solid var(--border-light)',
                        color: 'var(--text-primary)'
                      }}
                    >
                      <option value="medication">Medication</option>
                      <option value="therapy">Therapy</option>
                      <option value="monitoring">Monitoring</option>
                      <option value="errands">Errands</option>
                    </select>
                  </div>
                </div>

                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="recurring"
                    checked={newTask.recurring}
                    onChange={(e) => setNewTask({...newTask, recurring: e.target.checked})}
                    className="h-4 w-4 rounded"
                    style={{
                      accentColor: 'var(--primary)',
                      borderColor: 'var(--border-light)'
                    }}
                  />
                  <label htmlFor="recurring" className="ml-2 block text-sm" style={{ color: 'var(--text-primary)' }}>
                    Recurring task
                  </label>
                </div>
              </div>
              
              <div className="flex space-x-3 mt-6">
                <button
                  onClick={() => setShowNewTaskModal(false)}
                  className="flex-1 px-4 py-2 rounded-lg transition-colors"
                  style={{
                    backgroundColor: 'var(--bg-accent)',
                    border: '1px solid var(--border-light)',
                    color: 'var(--text-primary)'
                  }}
                >
                  Cancel
                </button>
                <button
                  onClick={handleCreateTask}
                  disabled={!newTask.title.trim()}
                  className="flex-1 px-4 py-2 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                  style={{ backgroundColor: 'var(--primary)', color: 'white' }}
                >
                  Create Task
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
