import { useState, useEffect } from 'react'
import { Link, useNavigate, useLocation } from 'react-router-dom'
import { Mail, Lock, Eye, EyeOff } from 'lucide-react'
import { authService } from '../services/authService'

export default function Auth() {
  const location = useLocation()
  const searchParams = new URLSearchParams(location.search)
  const [isSignIn, setIsSignIn] = useState(location.pathname === '/sign-in' || searchParams.get('mode') === 'signin')
  const [showPassword, setShowPassword] = useState(false)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    confirmPassword: '',
    firstName: '',
    lastName: ''
  })
  const navigate = useNavigate()

  useEffect(() => {
    const searchParams = new URLSearchParams(location.search)
    setIsSignIn(location.pathname === '/sign-in' || searchParams.get('mode') === 'signin')
  }, [location.pathname, location.search])

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    console.log('Form submitted, isSignIn:', isSignIn)
    console.log('Form data:', formData)
    setLoading(true)
    setError('')

    // Validate required fields
    if (!formData.email.trim()) {
      setError('Email address is required')
      setLoading(false)
      return
    }

    if (!formData.password.trim()) {
      setError('Password is required')
      setLoading(false)
      return
    }

    if (!isSignIn) {
      if (!formData.firstName.trim()) {
        setError('First name is required')
        setLoading(false)
        return
      }
      if (!formData.lastName.trim()) {
        setError('Last name is required')
        setLoading(false)
        return
      }
      if (formData.password !== formData.confirmPassword) {
        setError('Passwords do not match')
        setLoading(false)
        return
      }
    }

    try {
      if (isSignIn) {
        console.log('🔐 Attempting sign in with authService...')
        console.log('🔗 Testing Supabase connection before authentication...')
        
        // Enhanced error handling with timeout
        const authPromise = authService.signIn(formData.email, formData.password)
        const timeoutPromise = new Promise((_, reject) => 
          setTimeout(() => reject(new Error('Authentication request timed out after 30 seconds')), 30000)
        )
        
        const { user, error } = await Promise.race([authPromise, timeoutPromise]) as any
        
        console.log('📤 Sign in response received:', { userExists: !!user, errorExists: !!error })
        
        if (error) {
          console.error('❌ Authentication error:', error)
          // More specific error handling
          if (error.includes('Invalid login credentials')) {
            throw new Error('Invalid email or password. Please check your credentials and try again.')
          } else if (error.includes('Email not confirmed')) {
            throw new Error('Please check your email and click the confirmation link before signing in.')
          } else {
            throw new Error(`Authentication failed: ${error}`)
          }
        }

        if (user) {
          console.log('✅ Sign in successful:', user)
          console.log('🚀 Navigating to dashboard...')
          // Force page reload after navigation to ensure clean state
          navigate('/dashboard')
          setTimeout(() => window.location.reload(), 100)
        } else {
          console.error('❌ Login failed - no user returned despite no error')
          throw new Error('Login failed - authentication did not return a user. Please check if your account exists or try creating an account.')
        }
      } else {
        console.log('📝 Attempting sign up with authService...')
        console.log('🔗 Testing Supabase connection before registration...')
        
        // Enhanced error handling with timeout for sign up
        const signUpPromise = authService.signUp({
          email: formData.email,
          password: formData.password,
          first_name: formData.firstName,
          last_name: formData.lastName,
          user_type: 'client' // Default user type for new registrations
        })
        const timeoutPromise = new Promise((_, reject) => 
          setTimeout(() => reject(new Error('Registration request timed out after 30 seconds')), 30000)
        )
        
        const { user, error } = await Promise.race([signUpPromise, timeoutPromise]) as any
        
        console.log('📤 Sign up response received:', { userExists: !!user, errorExists: !!error })

        if (error) {
          console.error('❌ Registration error:', error)
          // More specific error handling for sign up
          if (error.includes('User already registered')) {
            throw new Error('An account with this email already exists. Please try signing in instead.')
          } else if (error.includes('Password')) {
            throw new Error('Password must be at least 6 characters long and contain both letters and numbers.')
          } else {
            throw new Error(`Registration failed: ${error}`)
          }
        }

        if (user) {
          console.log('✅ Sign up successful:', user)
          console.log('🚀 Navigating to dashboard...')
          // Force page reload after navigation to ensure clean state
          navigate('/dashboard')
          setTimeout(() => window.location.reload(), 100)
        } else {
          console.error('❌ Registration failed - no user returned despite no error')
          throw new Error('Registration failed - no user returned. Please try again or contact support.')
        }
      }
    } catch (error: any) {
      console.error('🚨 Authentication error caught:', error)
      console.error('🚨 Error details:', {
        message: error.message,
        name: error.name,
        stack: error.stack,
        timestamp: new Date().toISOString()
      })
      
      // Enhanced error display with actionable messages
      const errorMessage = error.message || 'An unexpected error occurred during authentication'
      setError(`${errorMessage}${errorMessage.includes('try again') ? '' : ' Please try again or contact support if the problem persists.'}`)
      
      // Force a small delay to ensure error is visible
      setTimeout(() => {
        if (error.message && error.message.includes('timeout')) {
          setError(error.message + ' The server may be experiencing issues. Please try again in a few moments.')
        }
      }, 100)
    } finally {
      setLoading(false)
      console.log('🏁 Form submission completed at:', new Date().toISOString())
    }
  }

  return (
    <main className="min-h-screen flex flex-col justify-center py-6 sm:py-8 lg:py-12 px-4 sm:px-6 lg:px-8" style={{backgroundColor: 'var(--bg-secondary)'}} role="main" aria-label="Authentication page">
      <div className="sm:mx-auto sm:w-full sm:max-w-md lg:max-w-lg">
        {/* Apple Mac Desktop Style Header */}
        <div className="flex justify-center mb-8 sm:mb-10">
          <Link to="/" className="group">
            <div
              className="rounded-2xl sm:rounded-3xl px-8 sm:px-10 py-4 sm:py-5 text-2xl sm:text-3xl font-semibold tracking-wide macos-title transition-all duration-300 group-hover:scale-105"
              style={{
                backgroundColor: 'var(--primary)',
                color: 'var(--bg-primary)',
                boxShadow: 'var(--shadow-large)',
                backdropFilter: 'blur(10px)',
                border: '1px solid rgba(255,255,255,0.1)',
                letterSpacing: '-0.01em',
                fontWeight: '600'
              }}
            >
              2CareConnector
            </div>
          </Link>
        </div>

        <div className="text-center mb-8 sm:mb-10">
          <h1 className="text-2xl sm:text-3xl lg:text-4xl font-semibold mb-3 sm:mb-4 macos-title" style={{
            color: 'var(--text-primary)',
            fontWeight: '600',
            letterSpacing: '-0.01em'
          }}>
            {isSignIn ? 'Welcome Back' : 'Join Our Care Community'}
          </h1>
          <p className="text-base sm:text-lg macos-body mb-4 sm:mb-6" style={{
            color: 'var(--text-secondary)',
            fontWeight: '400',
            letterSpacing: '-0.005em'
          }}>
            {isSignIn
              ? "Sign in to access your care network"
              : "Create your account to connect with verified care professionals"
            }
          </p>
          <p className="text-sm sm:text-base macos-body" style={{color: 'var(--text-secondary)'}}>
            {isSignIn ? "Don't have an account? " : "Already have an account? "}
            <button
              onClick={() => setIsSignIn(!isSignIn)}
              className="font-medium transition-all duration-200 hover:underline"
              style={{color: 'var(--primary)', textDecoration: 'none'}}
              onMouseEnter={(e) => e.currentTarget.style.opacity = '0.8'}
              onMouseLeave={(e) => e.currentTarget.style.opacity = '1'}
            >
              {isSignIn ? 'Create one here' : 'Sign in instead'}
            </button>
          </p>
        </div>
      </div>

      <div className="sm:mx-auto sm:w-full sm:max-w-md lg:max-w-lg">
        <div className="py-8 sm:py-10 lg:py-12 px-6 sm:px-8 lg:px-10 rounded-2xl sm:rounded-3xl" style={{
          backgroundColor: 'var(--bg-primary)',
          boxShadow: 'var(--shadow-large)',
          backdropFilter: 'blur(10px)',
          border: '1px solid var(--border-light)'
        }}>
          {error && (
            <div
              className="mb-4 p-3 rounded-md border"
              style={{backgroundColor: 'var(--bg-error)', borderColor: 'var(--border-error)'}}
              role="alert"
              aria-live="polite"
              id="auth-error"
            >
              <p className="text-sm" style={{color: 'var(--text-error)'}}>{error}</p>
            </div>
          )}
          <form onSubmit={handleSubmit} className="space-y-6" role="form" aria-label={isSignIn ? 'Sign in form' : 'Sign up form'}>
            {!isSignIn && (
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label htmlFor="firstName" className="block text-sm font-semibold" style={{color: 'var(--text-primary)'}}>
                    First name
                  </label>
                  <div className="mt-1">
                    <input
                      id="firstName"
                      name="firstName"
                      type="text"
                      required={!isSignIn}
                      value={formData.firstName}
                      onChange={handleInputChange}
                      className="w-full px-4 py-3 border rounded-lg shadow-sm focus:outline-none transition-all duration-200"
                      style={{
                        borderColor: 'var(--border-medium)',
                        color: 'var(--text-primary)'
                      }}
                      onFocus={(e) => {
                        e.currentTarget.style.borderColor = 'var(--primary)'
                        e.currentTarget.style.boxShadow = 'var(--focus-shadow)'
                      }}
                      onBlur={(e) => {
                        e.currentTarget.style.borderColor = 'var(--border-medium)'
                        e.currentTarget.style.boxShadow = 'none'
                      }}
                      placeholder="First name"
                      aria-describedby={error && error.includes('first name') ? 'auth-error' : undefined}
                      aria-invalid={error && error.includes('first name') ? 'true' : 'false'}
                    />
                  </div>
                </div>
                <div>
                  <label htmlFor="lastName" className="block text-sm font-semibold" style={{color: 'var(--text-primary)'}}>
                    Last name
                  </label>
                  <div className="mt-1">
                    <input
                      id="lastName"
                      name="lastName"
                      type="text"
                      required={!isSignIn}
                      value={formData.lastName}
                      onChange={handleInputChange}
                      className="w-full px-4 py-3 border rounded-lg shadow-sm focus:outline-none transition-all duration-200"
                      style={{
                        borderColor: 'var(--border-medium)',
                        color: 'var(--text-primary)'
                      }}
                      onFocus={(e) => {
                        e.currentTarget.style.borderColor = 'var(--primary)'
                        e.currentTarget.style.boxShadow = 'var(--focus-shadow)'
                      }}
                      onBlur={(e) => {
                        e.currentTarget.style.borderColor = 'var(--border-medium)'
                        e.currentTarget.style.boxShadow = 'none'
                      }}
                      placeholder="Last name"
                    />
                  </div>
                </div>
              </div>
            )}

            <div>
              <label htmlFor="email" className="block text-sm font-semibold" style={{color: 'var(--text-primary)'}}>
                Email address
              </label>
              <div className="mt-1 relative">
                <input
                  id="email"
                  name="email"
                  type="email"
                  autoComplete="email"
                  required
                  pattern="[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,}$"
                  title="Please enter a valid email address"
                  value={formData.email}
                  onChange={handleInputChange}
                  className="w-full pl-10 pr-3 py-3 border rounded-lg shadow-sm focus:outline-none transition-all duration-200"
                  style={{
                    borderColor: 'var(--border-medium)',
                    color: 'var(--text-primary)'
                  }}
                  onFocus={(e) => {
                    e.currentTarget.style.borderColor = 'var(--primary)'
                    e.currentTarget.style.boxShadow = 'var(--focus-shadow)'
                  }}
                  onBlur={(e) => {
                    e.currentTarget.style.borderColor = 'var(--border-medium)'
                    e.currentTarget.style.boxShadow = 'none'
                  }}
                  placeholder="Email"
                  aria-describedby={error && error.includes('email') ? 'auth-error' : undefined}
                  aria-invalid={error && error.includes('email') ? 'true' : 'false'}
                />
                <Mail className="w-5 h-5 absolute left-3 top-3" style={{color: 'var(--text-muted)'}} />
              </div>
            </div>

            <div>
              <label htmlFor="password" className="block text-sm font-semibold" style={{color: 'var(--text-primary)'}}>
                Password
              </label>
              <div className="mt-1 relative">
                <input
                  id="password"
                  name="password"
                  type={showPassword ? 'text' : 'password'}
                  autoComplete={isSignIn ? 'current-password' : 'new-password'}
                  required
                  minLength={8}
                  title="Password must be at least 8 characters long"
                  value={formData.password}
                  onChange={handleInputChange}
                  className="w-full pl-10 pr-10 py-3 border rounded-lg shadow-sm focus:outline-none transition-all duration-200"
                  style={{
                    borderColor: 'var(--border-medium)',
                    color: 'var(--text-primary)'
                  }}
                  onFocus={(e) => {
                    e.currentTarget.style.borderColor = 'var(--primary)'
                    e.currentTarget.style.boxShadow = 'var(--focus-shadow)'
                  }}
                  onBlur={(e) => {
                    e.currentTarget.style.borderColor = 'var(--border-medium)'
                    e.currentTarget.style.boxShadow = 'none'
                  }}
                  placeholder="Password"
                />
                <Lock className="w-5 h-5 absolute left-3 top-3" style={{color: 'var(--text-muted)'}} />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 transition-colors"
                  style={{
                    color: 'var(--text-muted)'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.color = 'var(--text-secondary)'
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.color = 'var(--text-muted)'
                  }}
                  onFocus={(e) => {
                    e.currentTarget.style.color = 'var(--primary)'
                    e.currentTarget.style.boxShadow = 'var(--focus-shadow)'
                  }}
                  onBlur={(e) => {
                    e.currentTarget.style.color = 'var(--text-muted)'
                    e.currentTarget.style.boxShadow = 'none'
                  }}
                  aria-label={showPassword ? 'Hide password' : 'Show password'}
                  tabIndex={0}
                >
                  {showPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                </button>
              </div>
            </div>

            {!isSignIn && (
              <div>
                <label htmlFor="confirmPassword" className="block text-sm font-semibold" style={{color: 'var(--text-primary)'}}>
                  Confirm password
                </label>
                <div className="mt-1 relative">
                  <input
                    id="confirmPassword"
                    name="confirmPassword"
                    type={showPassword ? 'text' : 'password'}
                    autoComplete="new-password"
                    required={!isSignIn}
                    value={formData.confirmPassword}
                    onChange={handleInputChange}
                    className="w-full pl-10 pr-3 py-3 border rounded-lg shadow-sm focus:outline-none transition-all duration-200"
                    style={{
                      borderColor: 'var(--border-medium)',
                      color: 'var(--text-primary)'
                    }}
                    onFocus={(e) => {
                      e.currentTarget.style.borderColor = 'var(--primary)'
                      e.currentTarget.style.boxShadow = 'var(--focus-shadow)'
                    }}
                    onBlur={(e) => {
                      e.currentTarget.style.borderColor = 'var(--border-medium)'
                      e.currentTarget.style.boxShadow = 'none'
                    }}
                    placeholder="Re-enter your password"
                  />
                  <Lock className="w-5 h-5 absolute left-3 top-3" style={{color: 'var(--text-muted)'}} />
                </div>
              </div>
            )}

            {!isSignIn && (
              <div className="space-y-4">
                <div className="flex items-start">
                  <input
                    id="terms-acceptance"
                    name="terms-acceptance"
                    type="checkbox"
                    required
                    className="h-4 w-4 rounded mt-1 focus:outline-none"
                    style={{
                      accentColor: 'var(--primary)',
                      borderColor: 'var(--border-medium)'
                    }}
                  />
                  <label htmlFor="terms-acceptance" className="ml-3 block text-sm leading-5" style={{color: 'var(--text-secondary)'}}>
                    I agree to the{' '}
                    <a href="/terms-of-service" className="font-medium transition-colors hover:opacity-80" style={{color: 'var(--primary)'}}>
                      Terms of Service
                    </a>{' '}
                    and{' '}
                    <a href="/privacy-policy" className="font-medium transition-colors hover:opacity-80" style={{color: 'var(--primary)'}}>
                      Privacy Policy
                    </a>
                  </label>
                </div>
                <div className="flex items-start">
                  <input
                    id="hipaa-acknowledgment"
                    name="hipaa-acknowledgment"
                    type="checkbox"
                    required
                    className="h-4 w-4 rounded mt-1 focus:outline-none"
                    style={{
                      accentColor: 'var(--primary)',
                      borderColor: 'var(--border-medium)'
                    }}
                  />
                  <label htmlFor="hipaa-acknowledgment" className="ml-3 block text-sm leading-5" style={{color: 'var(--text-secondary)'}}>
                    I acknowledge receipt of the{' '}
                    <a href="/hipaa-notice" className="font-medium transition-colors hover:opacity-80" style={{color: 'var(--primary)'}}>
                      HIPAA Notice of Privacy Practices
                    </a>{' '}
                    and understand how my health information may be used and disclosed
                  </label>
                </div>
              </div>
            )}

            {isSignIn && (
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <input
                    id="remember-me"
                    name="remember-me"
                    type="checkbox"
                    className="h-4 w-4 rounded focus:outline-none"
                    style={{
                      accentColor: 'var(--primary)',
                      borderColor: 'var(--border-medium)'
                    }}
                  />
                  <label htmlFor="remember-me" className="ml-2 block text-sm" style={{color: 'var(--text-primary)'}}>
                    Remember me
                  </label>
                </div>

                <div className="text-sm">
                  <a href="#" className="font-medium transition-colors hover:opacity-80" style={{color: 'var(--primary)'}}>
                    Forgot your password?
                  </a>
                </div>
              </div>
            )}

            <div>
              <button
                type="submit"
                disabled={loading}
                className="button-primary w-full max-w-xs mx-auto flex justify-center py-3 px-6 border border-transparent rounded-lg shadow-sm text-sm font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed hover:opacity-90 focus:ring-2 focus:ring-primary focus:ring-opacity-20"
              >
                {loading ? (
                  <div className="flex items-center justify-center">
                    <div className="animate-spin rounded-full h-4 w-4 border-2 mr-3" style={{borderColor: 'var(--text-white)', borderTopColor: 'transparent'}}></div>
                    <div className="flex flex-col items-start">
                      <span className="text-sm font-medium">
                        {isSignIn ? 'Authenticating...' : 'Creating your secure account...'}
                      </span>
                      <span className="text-xs opacity-80 mt-1">
                        {isSignIn ? 'Verifying credentials' : 'Setting up your care profile'}
                      </span>
                    </div>
                  </div>
                ) : (
                  isSignIn ? 'Sign in to your care network' : 'Create your care account'
                )}
              </button>
            </div>
          </form>

          <div className="mt-6">
            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t" style={{borderColor: 'var(--border-light)'}} />
              </div>
              <div className="relative flex justify-center text-sm">
                <span className="px-2 text-sm" style={{backgroundColor: 'var(--bg-primary)', color: 'var(--text-secondary)'}}>Or continue with</span>
              </div>
            </div>

            <div className="mt-6 grid grid-cols-2 gap-3">
              <button
                type="button"
                className="w-full inline-flex justify-center py-3 px-4 border rounded-lg text-sm font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-opacity-20 hover:shadow-md hover:bg-gray-50"
                style={{
                  borderColor: 'var(--border-medium)',
                  backgroundColor: 'var(--bg-primary)',
                  color: 'var(--text-secondary)'
                }}
                aria-label="Sign in with Google"
              >
                <svg className="w-5 h-5" viewBox="0 0 24 24">
                  <path fill="currentColor" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                  <path fill="currentColor" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                  <path fill="currentColor" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                  <path fill="currentColor" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                </svg>
                <span className="ml-2">Google</span>
              </button>

              <button
                type="button"
                className="w-full inline-flex justify-center py-2 px-4 border rounded-md shadow-sm text-sm font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-opacity-20"
                style={{
                  borderColor: 'var(--border-medium)',
                  backgroundColor: 'var(--bg-primary)',
                  color: 'var(--text-secondary)'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.backgroundColor = 'var(--bg-secondary)'
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = 'var(--bg-primary)'
                }}
                aria-label="Sign in with Facebook"
              >
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                </svg>
                <span className="ml-2">Facebook</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </main>
  )
}
