import React, { useState, useEffect } from 'react'
import { Link, useNavigate } from 'react-router-dom'
import { Eye, EyeOff, Loader2 } from 'lucide-react'
import { supabase } from '../lib/supabase'
import { InputSanitizer } from '../utils/inputSanitizer'
import { csrfService } from '../services/csrfService'
import { authService } from '../services/authService'

export default function GetStarted() {
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [confirmPassword, setConfirmPassword] = useState('')
  const [firstName, setFirstName] = useState('')
  const [lastName, setLastName] = useState('')
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)
  const [validationErrors, setValidationErrors] = useState<{[key: string]: string}>({})
  const [csrfToken, setCsrfToken] = useState<string>('')
  const navigate = useNavigate()

  // Initialize CSRF protection
  useEffect(() => {
    csrfService.initialize()
    setCsrfToken(csrfService.getTokenForForm())
  }, [])

  const validateForm = () => {
    const errors: {[key: string]: string} = {}

    try {
      // Check for malicious input patterns
      const formData = { firstName, lastName, email, password, confirmPassword }

      for (const [field, value] of Object.entries(formData)) {
        if (typeof value === 'string') {
          const securityCheck = InputSanitizer.detectMaliciousInput(value)
          if (securityCheck.isMalicious) {
            errors[field] = `Invalid input detected: ${securityCheck.threats.join(', ')}`
            continue
          }
        }
      }

      // Validate and sanitize first name
      if (!firstName.trim()) {
        errors.firstName = 'First name is required'
      } else {
        try {
          InputSanitizer.sanitizeName(firstName)
        } catch (error) {
          errors.firstName = error instanceof Error ? error.message : 'Invalid first name'
        }
      }

      // Validate and sanitize last name
      if (!lastName.trim()) {
        errors.lastName = 'Last name is required'
      } else {
        try {
          InputSanitizer.sanitizeName(lastName)
        } catch (error) {
          errors.lastName = error instanceof Error ? error.message : 'Invalid last name'
        }
      }

      // Validate and sanitize email
      if (!email.trim()) {
        errors.email = 'Email is required'
      } else {
        try {
          InputSanitizer.sanitizeEmail(email)
        } catch (error) {
          errors.email = error instanceof Error ? error.message : 'Invalid email format'
        }
      }

      // Validate password with comprehensive security checks
      if (!password.trim()) {
        errors.password = 'Password is required'
      } else {
        const passwordValidation = InputSanitizer.validatePassword(password)
        if (!passwordValidation.isValid) {
          errors.password = passwordValidation.errors[0] // Show first error
        }
      }

      // Validate password confirmation
      if (!confirmPassword.trim()) {
        errors.confirmPassword = 'Please confirm your password'
      } else if (password !== confirmPassword) {
        errors.confirmPassword = 'Passwords do not match'
      }

    } catch (error) {
      errors.general = 'Form validation failed. Please check your input.'
    }

    setValidationErrors(errors)
    return Object.keys(errors).length === 0
  }

  const handleSignUp = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) {
      return
    }

    setLoading(true)
    setError(null)
    setValidationErrors({})

    try {
      // Validate CSRF token
      const formData = {
        email,
        password,
        firstName,
        lastName,
        csrf_token: csrfToken
      }

      if (!csrfService.validateRequestToken(formData)) {
        setError('Security validation failed. Please refresh the page and try again.')
        setLoading(false)
        return
      }

      // Sanitize input data
      const sanitizedData = {
        email: InputSanitizer.sanitizeEmail(email),
        first_name: InputSanitizer.sanitizeName(firstName),
        last_name: InputSanitizer.sanitizeName(lastName),
        user_type: 'client' as const,
        password // Don't sanitize password as it might contain special characters intentionally
      }

      // Use authService for secure signup with all security features
      const result = await authService.signUp(sanitizedData)

      if (result.error) {
        setError(result.error)
        // Generate new CSRF token for retry
        setCsrfToken(csrfService.getTokenForForm())
        return
      }

      if (result.requiresVerification) {
        setSuccess('Account created! Please check your email to verify your account before signing in.')
        setTimeout(() => {
          navigate('/sign-in')
        }, 3000)
      } else if (result.user) {
        setSuccess('Account created successfully! Redirecting to dashboard...')
        setTimeout(() => {
          navigate('/dashboard')
        }, 2000)
      }

    } catch (error) {
      console.error('Signup error:', error)
      setError(error instanceof Error ? error.message : 'An unexpected error occurred')
      // Generate new CSRF token for retry
      setCsrfToken(csrfService.getTokenForForm())
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen flex flex-col justify-center py-6 sm:py-8 lg:py-12 px-4 sm:px-6 lg:px-8" style={{ backgroundColor: 'var(--bg-secondary)' }}>
      <div className="sm:mx-auto sm:w-full sm:max-w-md lg:max-w-lg">
        {/* Apple Mac Desktop Style Header */}
        <div className="flex justify-center mb-8 sm:mb-10">
          <div
            className="rounded-2xl sm:rounded-3xl px-8 sm:px-10 py-4 sm:py-5 text-2xl sm:text-3xl font-semibold tracking-wide macos-title"
            style={{
              backgroundColor: 'var(--primary)',
              color: 'var(--bg-primary)',
              boxShadow: 'var(--shadow-large)',
              backdropFilter: 'blur(10px)',
              border: '1px solid rgba(255,255,255,0.1)',
              letterSpacing: '-0.01em',
              fontWeight: '600'
            }}
          >
            2CareConnector
          </div>
        </div>

        <div className="text-center mb-8 sm:mb-10">
          <h2 className="text-2xl sm:text-3xl lg:text-4xl font-semibold mb-3 sm:mb-4 macos-title" style={{
            color: 'var(--text-primary)',
            fontWeight: '600',
            letterSpacing: '-0.01em'
          }}>
            Start Your Care Journey
          </h2>
          <p className="text-base sm:text-lg macos-body mb-4 sm:mb-6" style={{
            color: 'var(--text-secondary)',
            fontWeight: '400',
            letterSpacing: '-0.005em'
          }}>
            Create your account to connect with trusted care professionals
          </p>
          <p className="text-sm sm:text-base macos-body" style={{ color: 'var(--text-secondary)' }}>
            Already have an account?{' '}
            <Link
              to="/sign-in"
              className="font-medium transition-all duration-200 hover:underline"
              style={{ color: 'var(--primary)', textDecoration: 'none' }}
              onMouseEnter={(e) => e.currentTarget.style.opacity = '0.8'}
              onMouseLeave={(e) => e.currentTarget.style.opacity = '1'}
            >
              Sign in here
            </Link>
          </p>
        </div>
      </div>

      <div className="sm:mx-auto sm:w-full sm:max-w-md lg:max-w-lg">
        <div className="py-8 sm:py-10 lg:py-12 px-6 sm:px-8 lg:px-10 rounded-2xl sm:rounded-3xl" style={{ backgroundColor: 'var(--bg-primary)', border: '1px solid var(--border-light)', boxShadow: 'var(--shadow-large)', backdropFilter: 'blur(10px)' }}>
          <form className="space-y-8" onSubmit={handleSignUp}>
            {/* CSRF Protection */}
            <input type="hidden" name="csrf_token" value={csrfToken} />

            {error && (
              <div className="rounded-xl p-4 shadow-md" style={{ backgroundColor: 'var(--bg-error)', border: '1px solid var(--border-error)' }}>
                <p className="text-sm font-medium" style={{ color: 'var(--text-error)' }}>{error}</p>
              </div>
            )}

            {success && (
              <div className="rounded-xl p-4 shadow-md" style={{ backgroundColor: 'var(--bg-success)', border: '1px solid var(--success)' }}>
                <p className="text-sm font-medium" style={{ color: 'var(--text-success)' }}>{success}</p>
              </div>
            )}
            
            <div className="grid grid-cols-2 gap-6">
              <div>
                <label htmlFor="firstName" className="block text-sm font-semibold mb-2" style={{ color: 'var(--text-primary)' }}>
                  First name
                </label>
                <input
                  id="firstName"
                  name="firstName"
                  type="text"
                  required
                  value={firstName}
                  onChange={(e) => setFirstName(e.target.value)}
                  className="block w-full px-4 py-3 rounded-xl focus:outline-none transition-all duration-300"
                  style={{
                    border: '1px solid var(--border-medium)',
                    backgroundColor: 'var(--bg-primary)',
                    color: 'var(--text-primary)',
                    boxShadow: 'none'
                  }}
                  onFocus={(e) => {
                    e.currentTarget.style.borderColor = 'var(--primary)'
                    e.currentTarget.style.boxShadow = '0 0 0 3px var(--primary-light)'
                  }}
                  onBlur={(e) => {
                    e.currentTarget.style.borderColor = 'var(--border-medium)'
                    e.currentTarget.style.boxShadow = 'none'
                  }}
                  placeholder="First name"
                />
              </div>

              <div>
                <label htmlFor="lastName" className="block text-sm font-semibold mb-2" style={{ color: 'var(--text-primary)' }}>
                  Last name
                </label>
                <input
                  id="lastName"
                  name="lastName"
                  type="text"
                  required
                  value={lastName}
                  onChange={(e) => setLastName(e.target.value)}
                  className="block w-full px-4 py-3 rounded-xl focus:outline-none focus:ring-2 transition-all duration-300"
                  style={{
                    border: '1px solid var(--border-medium)',
                    backgroundColor: 'var(--bg-primary)',
                    color: 'var(--text-primary)'
                  }}
                  onFocus={(e) => e.currentTarget.style.borderColor = 'var(--primary)'}
                  onBlur={(e) => e.currentTarget.style.borderColor = 'var(--border-medium)'}
                  placeholder="Last name"
                />
              </div>
            </div>

            <div>
              <label htmlFor="email" className="block text-sm font-semibold mb-2" style={{ color: 'var(--text-primary)' }}>
                Email address
              </label>
              <input
                id="email"
                name="email"
                type="email"
                autoComplete="email"
                required
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="block w-full px-4 py-3 rounded-xl focus:outline-none focus:ring-2 transition-all duration-300"
                style={{
                  border: '1px solid var(--border-medium)',
                  backgroundColor: 'var(--bg-primary)',
                  color: 'var(--text-primary)'
                }}
                onFocus={(e) => e.currentTarget.style.borderColor = 'var(--primary)'}
                onBlur={(e) => e.currentTarget.style.borderColor = 'var(--border-medium)'}
                placeholder="Enter your email"
              />
            </div>

            <div>
              <label htmlFor="password" className="block text-sm font-semibold mb-2" style={{ color: 'var(--text-primary)' }}>
                Password
              </label>
              <div className="relative">
                <input
                  id="password"
                  name="password"
                  type={showPassword ? 'text' : 'password'}
                  autoComplete="new-password"
                  required
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="block w-full px-4 py-3 pr-12 rounded-xl focus:outline-none focus:ring-2 transition-all duration-300"
                  style={{
                    border: '1px solid var(--border-medium)',
                    backgroundColor: 'var(--bg-primary)',
                    color: 'var(--text-primary)'
                  }}
                  onFocus={(e) => e.currentTarget.style.borderColor = 'var(--primary)'}
                  onBlur={(e) => e.currentTarget.style.borderColor = 'var(--border-medium)'}
                  placeholder="Create a password"
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-3 top-3 transition-colors"
                  style={{ color: 'var(--text-muted)' }}
                  onMouseEnter={(e) => e.currentTarget.style.color = 'var(--text-secondary)'}
                  onMouseLeave={(e) => e.currentTarget.style.color = 'var(--text-muted)'}
                  aria-label={showPassword ? 'Hide password' : 'Show password'}
                >
                  {showPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                </button>
              </div>
              <p className="mt-2 text-xs" style={{ color: 'var(--text-secondary)' }}>
                Must be at least 6 characters
              </p>
            </div>

            <div>
              <label htmlFor="confirmPassword" className="block text-sm font-semibold mb-2" style={{ color: 'var(--text-primary)' }}>
                Confirm Password
              </label>
              <div className="relative">
                <input
                  id="confirmPassword"
                  name="confirmPassword"
                  type={showConfirmPassword ? 'text' : 'password'}
                  autoComplete="new-password"
                  required
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                  className="block w-full px-4 py-3 pr-12 rounded-xl focus:outline-none focus:ring-2 transition-all duration-300"
                  style={{
                    border: '1px solid var(--border-medium)',
                    backgroundColor: 'var(--bg-primary)',
                    color: 'var(--text-primary)'
                  }}
                  onFocus={(e) => e.currentTarget.style.borderColor = 'var(--primary)'}
                  onBlur={(e) => e.currentTarget.style.borderColor = 'var(--border-medium)'}
                  placeholder="Confirm your password"
                />
                <button
                  type="button"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  className="absolute right-3 top-3 transition-colors"
                  style={{ color: 'var(--text-muted)' }}
                  onMouseEnter={(e) => e.currentTarget.style.color = 'var(--text-secondary)'}
                  onMouseLeave={(e) => e.currentTarget.style.color = 'var(--text-muted)'}
                  aria-label={showConfirmPassword ? 'Hide password' : 'Show password'}
                >
                  {showConfirmPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                </button>
              </div>
              {confirmPassword && password !== confirmPassword && (
                <p className="mt-2 text-xs" style={{ color: 'var(--text-error)' }}>
                  Passwords do not match
                </p>
              )}
            </div>

            <div>
              <button
                type="submit"
                disabled={loading}
                className="w-full flex justify-center py-3 px-6 rounded-lg font-semibold text-base transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                style={{
                  backgroundColor: loading ? 'var(--text-muted)' : 'var(--primary)',
                  color: 'var(--bg-primary)',
                  border: 'none'
                }}
                onMouseEnter={(e) => {
                  if (!loading) {
                    e.currentTarget.style.opacity = '0.9'
                  }
                }}
                onMouseLeave={(e) => {
                  if (!loading) {
                    e.currentTarget.style.opacity = '1'
                  }
                }}
              >
                {loading ? (
                  <div className="flex items-center gap-3">
                    <Loader2 className="w-5 h-5 animate-spin" />
                    Creating account...
                  </div>
                ) : (
                  'Create account →'
                )}
              </button>
            </div>
          </form>

          <div className="mt-8">
            <p className="text-xs text-center" style={{ color: 'var(--text-secondary)' }}>
              By creating an account, you agree to our{' '}
              <a
                href="#"
                className="transition-colors"
                style={{ color: 'var(--primary)' }}
                onMouseEnter={(e) => e.currentTarget.style.opacity = '0.8'}
                onMouseLeave={(e) => e.currentTarget.style.opacity = '1'}
              >
                Terms of Service
              </a>{' '}
              and{' '}
              <a
                href="#"
                className="transition-colors"
                style={{ color: 'var(--primary)' }}
                onMouseEnter={(e) => e.currentTarget.style.opacity = '0.8'}
                onMouseLeave={(e) => e.currentTarget.style.opacity = '1'}
              >
                Privacy Policy
              </a>
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
