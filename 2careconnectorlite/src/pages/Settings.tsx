import React, { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { supabase } from '../lib/supabase'
import {
  User,
  Bell,
  Shield,
  Globe,
  Smartphone,
  Mail,
  Lock,
  Eye,
  EyeOff,
  Save,
  AlertCircle,
  ArrowLeft
} from 'lucide-react'

interface UserProfile {
  id: string
  email: string
  full_name: string | null
  phone: string | null
  location: string | null
  bio: string | null
  role: string
  profile_image_url: string | null
  notification_preferences: any
  privacy_settings: any
}

const getNotificationDescription = (key: string): string => {
  const descriptions: { [key: string]: string } = {
    emailNotifications: 'Receive important updates via email',
    pushNotifications: 'Get instant push notifications on your device',
    smsNotifications: 'Receive text messages for urgent notifications',
    appointmentReminders: 'Get reminded about upcoming appointments',
    messageNotifications: 'Be notified when you receive new messages',
    careGroupUpdates: 'Stay updated on care group activities',
    marketingEmails: 'Receive promotional emails and newsletters'
  }
  return descriptions[key] || 'Manage this notification preference'
}

const getPrivacyDescription = (key: string): string => {
  const descriptions: { [key: string]: string } = {
    showEmail: 'Allow others to see your email address',
    showPhone: 'Display your phone number on your profile',
    allowMessages: 'Enable direct messages from other users',
    allowConnectionRequests: 'Allow others to send you connection requests'
  }
  return descriptions[key] || 'Manage this privacy setting'
}

const Settings: React.FC = () => {
  const navigate = useNavigate()
  const [user, setUser] = useState<UserProfile | null>(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [activeSection, setActiveSection] = useState('profile')
  const [showPassword, setShowPassword] = useState(false)
  const [passwordData, setPasswordData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  })
  // Dynamic settings loaded from database - NO HARDCODED DEFAULTS
  const [notifications, setNotifications] = useState({
    emailNotifications: false,
    pushNotifications: false,
    smsNotifications: false,
    appointmentReminders: false,
    messageNotifications: false,
    careGroupUpdates: false,
    marketingEmails: false
  })
  const [privacy, setPrivacy] = useState({
    profileVisibility: '',
    showEmail: false,
    showPhone: false,
    allowMessages: false,
    allowConnectionRequests: false
  })

  useEffect(() => {
    getCurrentUser()
  }, [])

  const getCurrentUser = async () => {
    try {
      const { data: { user: authUser } } = await supabase.auth.getUser()
      if (authUser) {
        const { data: profile } = await supabase
          .from('profiles')
          .select('*')
          .eq('id', authUser.id)
          .single()
        
        if (profile) {
          setUser(profile)
          // Load notification preferences
          if (profile.notification_preferences) {
            setNotifications({ ...notifications, ...profile.notification_preferences })
          }
          // Load privacy settings
          if (profile.privacy_settings) {
            setPrivacy({ ...privacy, ...profile.privacy_settings })
          }
        }
      }
    } catch (error) {
      console.error('Error getting current user:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleProfileUpdate = async (field: string, value: string) => {
    if (!user) return

    try {
      setSaving(true)
      const { error } = await supabase
        .from('profiles')
        .update({ [field]: value })
        .eq('id', user.id)

      if (error) throw error
      
      setUser({ ...user, [field]: value })
    } catch (error) {
      console.error('Error updating profile:', error)
    } finally {
      setSaving(false)
    }
  }

  const handleNotificationUpdate = async () => {
    if (!user) return

    try {
      setSaving(true)
      const { error } = await supabase
        .from('profiles')
        .update({ notification_preferences: notifications })
        .eq('id', user.id)

      if (error) throw error
    } catch (error) {
      console.error('Error updating notifications:', error)
    } finally {
      setSaving(false)
    }
  }

  const handlePrivacyUpdate = async () => {
    if (!user) return

    try {
      setSaving(true)
      const { error } = await supabase
        .from('profiles')
        .update({ privacy_settings: privacy })
        .eq('id', user.id)

      if (error) throw error
    } catch (error) {
      console.error('Error updating privacy settings:', error)
    } finally {
      setSaving(false)
    }
  }

  const handlePasswordChange = async () => {
    if (passwordData.newPassword !== passwordData.confirmPassword) {
      alert('New passwords do not match')
      return
    }

    try {
      setSaving(true)
      const { error } = await supabase.auth.updateUser({
        password: passwordData.newPassword
      })

      if (error) throw error
      
      setPasswordData({
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
      })
      alert('Password updated successfully')
    } catch (error) {
      console.error('Error updating password:', error)
      alert('Error updating password')
    } finally {
      setSaving(false)
    }
  }

  const sections = [
    { id: 'profile', label: 'Profile', icon: User },
    { id: 'notifications', label: 'Notifications', icon: Bell },
    { id: 'privacy', label: 'Privacy', icon: Shield },
    { id: 'security', label: 'Security', icon: Lock },
    { id: 'preferences', label: 'Preferences', icon: Globe }
  ]

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center" style={{ backgroundColor: 'var(--bg-primary)' }}>
        <div className="flex flex-col items-center gap-4">
          <div className="relative">
            <div 
              className="animate-spin rounded-full h-12 w-12" 
              style={{ 
                border: '3px solid var(--border-light)',
                borderTop: '3px solid var(--primary)',
                borderRadius: '50%'
              }}
            ></div>
            <div 
              className="absolute inset-0 rounded-full" 
              style={{ 
                background: 'conic-gradient(from 0deg, transparent, var(--primary))',
                mask: 'radial-gradient(circle, transparent 60%, black 61%)',
                animation: 'spin 1.5s linear infinite'
              }}
            ></div>
          </div>
          <p 
            className="text-sm font-medium" 
            style={{ 
              color: 'var(--text-secondary)',
              letterSpacing: '0.01em'
            }}
          >
            Loading Settings...
          </p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen" style={{ backgroundColor: 'var(--bg-primary)' }}>
      {/* Header - Mobile Optimized */}
      <div className="px-4 sm:px-6 py-6 sm:py-8" style={{ backgroundColor: 'var(--bg-primary)', borderBottom: '1px solid var(--border-light)' }}>
        <div className="max-w-6xl mx-auto">
          {/* Back Button */}
          <button
            onClick={() => navigate(-1)}
            className="flex items-center gap-2 mb-4 px-3 py-2 rounded-lg transition-all duration-200 hover:shadow-sm"
            style={{
              color: 'var(--text-secondary)',
              backgroundColor: 'transparent'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.color = 'var(--primary)'
              e.currentTarget.style.backgroundColor = 'var(--bg-primary)'
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.color = 'var(--text-secondary)'
              e.currentTarget.style.backgroundColor = 'transparent'
            }}
          >
            <ArrowLeft className="w-5 h-5" />
            <span className="font-medium">Back</span>
          </button>

          <h1 
            className="text-4xl font-light mb-3 transition-all duration-300 hover:scale-[1.01]" 
            style={{ 
              color: 'var(--text-primary)',
              fontWeight: '300',
              letterSpacing: '-0.02em',
              lineHeight: '1.1',
              textShadow: '0 1px 2px rgba(0,0,0,0.05)',
              transformOrigin: 'left center'
            }}
          >
            Settings
          </h1>
          <p 
            className="text-lg leading-relaxed transition-all duration-500 hover:opacity-100" 
            style={{ 
              color: 'var(--text-secondary)',
              fontWeight: '400',
              letterSpacing: '0.01em',
              opacity: '0.85',
              lineHeight: '1.6'
            }}
          >
            Manage your account preferences and privacy settings
          </p>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 py-6 sm:py-10">
        <div className="flex flex-col lg:flex-row gap-6 lg:gap-10 min-h-[calc(100vh-200px)]" style={{ marginTop: '8px' }}>
          {/* Sidebar - Mobile Optimized */}
          <div className="w-full lg:w-72 lg:flex-shrink-0">
            <div
              className="p-4 sm:p-6 lg:p-7 rounded-lg sm:rounded-xl shadow-md lg:sticky lg:top-4"
              style={{
                backgroundColor: 'var(--bg-primary)',
                border: '1px solid var(--border-light)',
                boxShadow: '0 4px 16px rgba(0,0,0,0.06)',
                borderRadius: '16px'
              }}
            >
              <nav className="flex lg:flex-col gap-2 lg:gap-1 overflow-x-auto lg:overflow-x-visible pb-2 lg:pb-0">
                {sections.map((section) => {
                  const Icon = section.icon
                  return (
                    <button
                      key={section.id}
                      onClick={() => setActiveSection(section.id)}
                      className={`flex-shrink-0 lg:w-full flex items-center gap-2 lg:gap-3 px-3 lg:px-4 py-2 lg:py-3.5 rounded-lg lg:rounded-xl text-left transition-all duration-300 group hover:scale-105 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500/30 whitespace-nowrap lg:whitespace-normal text-sm lg:text-base ${
                        activeSection === section.id ? 'shadow-lg transform scale-105' : 'hover:shadow-sm'
                      }`}
                      style={{
                        backgroundColor: activeSection === section.id ? 'var(--primary)' : 'transparent',
                        color: activeSection === section.id ? 'white' : 'var(--text-secondary)',
                        border: 'none',
                        fontWeight: activeSection === section.id ? '500' : '400',
                        letterSpacing: '0.01em'
                      }}
                      onMouseEnter={(e) => {
                        if (activeSection !== section.id) {
                          e.currentTarget.style.backgroundColor = 'var(--bg-primary)'
                          e.currentTarget.style.borderColor = 'var(--primary)'
                          e.currentTarget.style.boxShadow = 'var(--shadow-card)'
                          e.currentTarget.style.color = 'var(--text-primary)'
                        }
                      }}
                      onMouseLeave={(e) => {
                        if (activeSection !== section.id) {
                          e.currentTarget.style.backgroundColor = 'transparent'
                          e.currentTarget.style.color = 'var(--text-secondary)'
                        }
                      }}
                    >
                      <Icon className="w-5 h-5" />
                      {section.label}
                    </button>
                  )
                })}
              </nav>
            </div>
          </div>

          {/* Content */}
          <div className="flex-1">
            <div 
              className="p-8 rounded-xl" 
              style={{ 
                backgroundColor: 'var(--bg-primary)', 
                border: '1px solid var(--border-light)',
                borderRadius: '16px',
                boxShadow: 'var(--shadow-card)',
                minHeight: 'calc(100vh - 300px)'
              }}
            >
              {activeSection === 'profile' && (
                <div className="space-y-8">
                  <div className="border-b pb-6" style={{ borderColor: 'var(--border-light)' }}>
                    <h2 
                      className="text-3xl font-light mb-2" 
                      style={{ 
                        color: 'var(--text-primary)',
                        fontWeight: '300',
                        letterSpacing: '-0.01em'
                      }}
                    >
                      Profile Information
                    </h2>
                    <p 
                      className="text-sm" 
                      style={{ 
                        color: 'var(--text-secondary)',
                        opacity: '0.8'
                      }}
                    >
                      Update your personal information and profile details
                    </p>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium mb-2" style={{ color: 'var(--text-primary)' }}>
                        Full Name
                      </label>
                      <input
                        type="text"
                        value={user?.full_name || ''}
                        onChange={(e) => handleProfileUpdate('full_name', e.target.value)}
                        className="w-full px-4 py-3.5 rounded-xl border transition-all duration-300 focus:outline-none focus:ring-0 focus:shadow-lg hover:shadow-sm"
                        style={{
                          borderColor: 'var(--border-medium)',
                          backgroundColor: 'var(--bg-primary)',
                          color: 'var(--text-primary)',
                          boxShadow: 'var(--shadow-input)',
                          fontSize: '16px',
                          fontWeight: '400'
                        }}
                        onFocus={(e) => {
                          e.target.style.borderColor = 'var(--primary)'
                          e.target.style.boxShadow = 'var(--focus-shadow)'
                        }}
                        onBlur={(e) => {
                          e.target.style.borderColor = 'var(--border-medium)'
                          e.target.style.boxShadow = 'var(--shadow-input)'
                        }}
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium mb-2" style={{ color: 'var(--text-primary)' }}>
                        Email
                      </label>
                      <input
                        type="email"
                        value={user?.email || ''}
                        disabled
                        className="w-full px-4 py-3.5 rounded-xl border opacity-60 cursor-not-allowed"
                        style={{
                          borderColor: 'var(--border-light)',
                          backgroundColor: 'var(--bg-primary)',
                          color: 'var(--text-secondary)',
                          boxShadow: 'var(--shadow-sm)',
                          fontSize: '16px',
                          fontWeight: '400'
                        }}
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium mb-2" style={{ color: 'var(--text-primary)' }}>
                        Phone
                      </label>
                      <input
                        type="tel"
                        value={user?.phone || ''}
                        onChange={(e) => handleProfileUpdate('phone', e.target.value)}
                        className="w-full px-4 py-3.5 rounded-xl border transition-all duration-300 focus:outline-none focus:ring-0 focus:shadow-lg hover:shadow-sm"
                        style={{
                          borderColor: 'var(--border-medium)',
                          backgroundColor: 'var(--bg-primary)',
                          color: 'var(--text-primary)'
                        }}
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium mb-2" style={{ color: 'var(--text-primary)' }}>
                        Location
                      </label>
                      <input
                        type="text"
                        value={user?.location || ''}
                        onChange={(e) => handleProfileUpdate('location', e.target.value)}
                        className="w-full px-4 py-3.5 rounded-xl border transition-all duration-300 focus:outline-none focus:ring-0 focus:shadow-lg hover:shadow-sm"
                        style={{
                          borderColor: 'var(--border-medium)',
                          backgroundColor: 'var(--bg-primary)',
                          color: 'var(--text-primary)'
                        }}
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium mb-2" style={{ color: 'var(--text-primary)' }}>
                      Bio
                    </label>
                    <textarea
                      value={user?.bio || ''}
                      onChange={(e) => handleProfileUpdate('bio', e.target.value)}
                      rows={4}
                      className="w-full px-4 py-3.5 rounded-xl border transition-all duration-300 focus:outline-none focus:ring-0 focus:shadow-lg hover:shadow-sm"
                      style={{
                        borderColor: 'var(--border-medium)',
                        backgroundColor: 'var(--bg-primary)',
                        color: 'var(--text-primary)'
                      }}
                      placeholder="Tell us about yourself..."
                    />
                  </div>
                </div>
              )}

              {activeSection === 'notifications' && (
                <div className="space-y-8">
                  <div className="border-b pb-6" style={{ borderColor: 'var(--border-light)' }}>
                    <h2 
                      className="text-3xl font-light mb-2" 
                      style={{ 
                        color: 'var(--text-primary)',
                        fontWeight: '300',
                        letterSpacing: '-0.01em'
                      }}
                    >
                      Notification Preferences
                    </h2>
                    <p 
                      className="text-sm" 
                      style={{ 
                        color: 'var(--text-secondary)',
                        opacity: '0.8'
                      }}
                    >
                      Choose how you want to be notified about important updates
                    </p>
                  </div>
                  
                  <div className="space-y-6">
                    {Object.entries(notifications).map(([key, value]) => (
                      <div key={key} className="flex items-center justify-between p-4 rounded-xl transition-all duration-200 hover:shadow-sm" style={{ backgroundColor: 'var(--bg-primary)', border: '1px solid var(--border-light)' }}>
                        <div className="flex-1">
                          <h3 
                            className="font-medium text-base mb-1" 
                            style={{ 
                              color: 'var(--text-primary)',
                              letterSpacing: '0.01em'
                            }}
                          >
                            {key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                          </h3>
                          <p 
                            className="text-sm" 
                            style={{ 
                              color: 'var(--text-secondary)',
                              opacity: '0.7'
                            }}
                          >
                            {getNotificationDescription(key)}
                          </p>
                        </div>
                        <label className="relative inline-flex items-center cursor-pointer ml-4">
                          <input
                            type="checkbox"
                            checked={value}
                            onChange={(e) => setNotifications({ ...notifications, [key]: e.target.checked })}
                            className="sr-only peer"
                          />
                          <div 
                            className="w-12 h-7 rounded-full transition-all duration-300 peer peer-checked:after:translate-x-full after:content-[''] after:absolute after:top-[3px] after:left-[3px] after:border after:rounded-full after:h-5 after:w-5 after:transition-all after:duration-300 after:shadow-sm" 
                            style={{
                              backgroundColor: value ? 'var(--primary)' : 'var(--border-medium)',
                              boxShadow: 'inset 0 1px 3px rgba(0,0,0,0.1)',
                              '--after-bg': 'white',
                              '--after-border': value ? 'white' : 'var(--border-medium)',
                              '--after-shadow': '0 2px 4px rgba(0,0,0,0.15)'
                            } as any}
                          >
                            <div 
                              className="absolute top-[3px] left-[3px] w-5 h-5 bg-white border rounded-full transition-all duration-300 shadow-sm"
                              style={{
                                transform: value ? 'translateX(20px)' : 'translateX(0)',
                                borderColor: value ? 'white' : 'var(--border-medium)'
                              }}
                            />
                          </div>
                        </label>
                      </div>
                    ))}
                  </div>

                  <button
                    onClick={handleNotificationUpdate}
                    disabled={saving}
                    className="flex items-center gap-3 px-8 py-4 rounded-xl font-medium transition-all duration-300 hover:shadow-lg transform hover:scale-[1.02] active:scale-[0.98] disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none disabled:shadow-none"
                    style={{
                      backgroundColor: 'var(--primary)',
                      color: 'var(--bg-primary)',
                      boxShadow: '0 4px 12px rgba(16, 185, 129, 0.25)',
                      fontSize: '16px',
                      fontWeight: '500',
                      letterSpacing: '0.01em'
                    }}
                  >
                    <Save className="w-5 h-5" />
                    {saving ? 'Saving...' : 'Save Preferences'}
                  </button>
                </div>
              )}

              {activeSection === 'privacy' && (
                <div className="space-y-8">
                  <div className="border-b pb-6" style={{ borderColor: 'var(--border-light)' }}>
                    <h2 
                      className="text-3xl font-light mb-2" 
                      style={{ 
                        color: 'var(--text-primary)',
                        fontWeight: '300',
                        letterSpacing: '-0.01em'
                      }}
                    >
                      Privacy Settings
                    </h2>
                    <p 
                      className="text-sm" 
                      style={{ 
                        color: 'var(--text-secondary)',
                        opacity: '0.8'
                      }}
                    >
                      Control your privacy preferences and data visibility
                    </p>
                  </div>
                  
                  <div className="space-y-6">
                    <div className="p-4 rounded-xl" style={{ backgroundColor: 'var(--bg-primary)', border: '1px solid var(--border-light)' }}>
                      <label 
                        className="block text-sm font-medium mb-3" 
                        style={{ 
                          color: 'var(--text-primary)',
                          letterSpacing: '0.01em'
                        }}
                      >
                        Profile Visibility
                      </label>
                      <select
                        value={privacy.profileVisibility}
                        onChange={(e) => setPrivacy({ ...privacy, profileVisibility: e.target.value })}
                        className="w-full px-4 py-3.5 rounded-xl border transition-all duration-300 focus:outline-none focus:ring-0 focus:shadow-lg hover:shadow-sm cursor-pointer"
                        style={{
                          borderColor: 'var(--border-medium)',
                          backgroundColor: 'var(--bg-primary)',
                          color: 'var(--text-primary)',
                          boxShadow: '0 1px 3px rgba(0,0,0,0.05)',
                          fontSize: '16px',
                          fontWeight: '400'
                        }}
                        onFocus={(e) => {
                          e.target.style.borderColor = 'var(--primary)'
                          e.target.style.boxShadow = '0 0 0 3px rgba(16,185,129,0.1), 0 4px 12px rgba(0,0,0,0.1)'
                        }}
                        onBlur={(e) => {
                          e.target.style.borderColor = 'var(--border-medium)'
                          e.target.style.boxShadow = '0 1px 3px rgba(0,0,0,0.05)'
                        }}
                      >
                        <option value="public">Public - Visible to everyone</option>
                        <option value="connections">Connections Only - Visible to your connections</option>
                        <option value="private">Private - Only visible to you</option>
                      </select>
                    </div>

                    {Object.entries(privacy).filter(([key]) => key !== 'profileVisibility').map(([key, value]) => (
                      <div key={key} className="flex items-center justify-between p-4 rounded-xl transition-all duration-200 hover:shadow-sm" style={{ backgroundColor: 'var(--bg-primary)', border: '1px solid var(--border-light)' }}>
                        <div className="flex-1">
                          <h3 
                            className="font-medium text-base mb-1" 
                            style={{ 
                              color: 'var(--text-primary)',
                              letterSpacing: '0.01em'
                            }}
                          >
                            {key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                          </h3>
                          <p 
                            className="text-sm" 
                            style={{ 
                              color: 'var(--text-secondary)',
                              opacity: '0.7'
                            }}
                          >
                            {getPrivacyDescription(key)}
                          </p>
                        </div>
                        <label className="relative inline-flex items-center cursor-pointer ml-4">
                          <input
                            type="checkbox"
                            checked={value as boolean}
                            onChange={(e) => setPrivacy({ ...privacy, [key]: e.target.checked })}
                            className="sr-only peer"
                          />
                          <div 
                            className="w-12 h-7 rounded-full transition-all duration-300 peer peer-checked:after:translate-x-full after:content-[''] after:absolute after:top-[3px] after:left-[3px] after:border after:rounded-full after:h-5 after:w-5 after:transition-all after:duration-300 after:shadow-sm" 
                            style={{
                              backgroundColor: (value as boolean) ? 'var(--primary)' : 'var(--border-medium)',
                              boxShadow: 'inset 0 1px 3px rgba(0,0,0,0.1)',
                              '--after-bg': 'white',
                              '--after-border': (value as boolean) ? 'white' : 'var(--border-medium)',
                              '--after-shadow': '0 2px 4px rgba(0,0,0,0.15)'
                            } as any}
                          >
                            <div 
                              className="absolute top-[3px] left-[3px] w-5 h-5 bg-white border rounded-full transition-all duration-300 shadow-sm"
                              style={{
                                transform: (value as boolean) ? 'translateX(20px)' : 'translateX(0)',
                                borderColor: (value as boolean) ? 'white' : 'var(--border-medium)'
                              }}
                            />
                          </div>
                        </label>
                      </div>
                    ))}
                  </div>

                  <button
                    onClick={handlePrivacyUpdate}
                    disabled={saving}
                    className="flex items-center gap-3 px-8 py-4 rounded-xl font-medium transition-all duration-300 hover:shadow-lg transform hover:scale-[1.02] active:scale-[0.98] disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none disabled:shadow-none"
                    style={{
                      backgroundColor: 'var(--primary)',
                      color: 'var(--bg-primary)',
                      boxShadow: '0 4px 12px rgba(16, 185, 129, 0.25)',
                      fontSize: '16px',
                      fontWeight: '500',
                      letterSpacing: '0.01em'
                    }}
                  >
                    <Save className="w-5 h-5" />
                    {saving ? 'Saving...' : 'Save Settings'}
                  </button>
                </div>
              )}

              {activeSection === 'security' && (
                <div className="space-y-8">
                  <div className="border-b pb-6" style={{ borderColor: 'var(--border-light)' }}>
                    <h2 
                      className="text-3xl font-light mb-2" 
                      style={{ 
                        color: 'var(--text-primary)',
                        fontWeight: '300',
                        letterSpacing: '-0.01em'
                      }}
                    >
                      Security Settings
                    </h2>
                    <p 
                      className="text-sm" 
                      style={{ 
                        color: 'var(--text-secondary)',
                        opacity: '0.8'
                      }}
                    >
                      Update your password and manage account security
                    </p>
                  </div>
                  
                  <div className="p-6 rounded-xl" style={{ backgroundColor: 'var(--bg-primary)', border: '1px solid var(--border-light)' }}>
                    <div className="space-y-6">
                      <div>
                        <label 
                          className="block text-sm font-medium mb-3" 
                          style={{ 
                            color: 'var(--text-primary)',
                            letterSpacing: '0.01em'
                          }}
                        >
                          Current Password
                        </label>
                        <input
                          type="password"
                          value={passwordData.currentPassword}
                          onChange={(e) => setPasswordData({ ...passwordData, currentPassword: e.target.value })}
                          className="w-full px-4 py-3.5 rounded-xl border transition-all duration-300 focus:outline-none focus:ring-0 focus:shadow-lg hover:shadow-sm"
                          style={{
                            borderColor: 'var(--border-medium)',
                            backgroundColor: 'var(--bg-primary)',
                            color: 'var(--text-primary)',
                            boxShadow: '0 1px 3px rgba(0,0,0,0.05)',
                            fontSize: '16px',
                            fontWeight: '400'
                          }}
                          onFocus={(e) => {
                            e.target.style.borderColor = 'var(--primary)'
                            e.target.style.boxShadow = '0 0 0 3px rgba(16,185,129,0.1), 0 4px 12px rgba(0,0,0,0.1)'
                          }}
                          onBlur={(e) => {
                            e.target.style.borderColor = 'var(--border-medium)'
                            e.target.style.boxShadow = '0 1px 3px rgba(0,0,0,0.05)'
                          }}
                          placeholder="Enter your current password"
                        />
                      </div>

                      <div>
                        <label 
                          className="block text-sm font-medium mb-3" 
                          style={{ 
                            color: 'var(--text-primary)',
                            letterSpacing: '0.01em'
                          }}
                        >
                          New Password
                        </label>
                        <div className="relative">
                          <input
                            type={showPassword ? 'text' : 'password'}
                            value={passwordData.newPassword}
                            onChange={(e) => setPasswordData({ ...passwordData, newPassword: e.target.value })}
                            className="w-full px-4 py-3.5 pr-12 rounded-xl border transition-all duration-300 focus:outline-none focus:ring-0 focus:shadow-lg hover:shadow-sm"
                            style={{
                              borderColor: 'var(--border-medium)',
                              backgroundColor: 'var(--bg-primary)',
                              color: 'var(--text-primary)',
                              boxShadow: '0 1px 3px rgba(0,0,0,0.05)',
                              fontSize: '16px',
                              fontWeight: '400'
                            }}
                            onFocus={(e) => {
                              e.target.style.borderColor = 'var(--primary)'
                              e.target.style.boxShadow = '0 0 0 3px rgba(16,185,129,0.1), 0 4px 12px rgba(0,0,0,0.1)'
                            }}
                            onBlur={(e) => {
                              e.target.style.borderColor = 'var(--border-medium)'
                              e.target.style.boxShadow = '0 1px 3px rgba(0,0,0,0.05)'
                            }}
                            placeholder="Enter a strong new password"
                          />
                          <button
                            type="button"
                            onClick={() => setShowPassword(!showPassword)}
                            className="absolute right-3 top-1/2 transform -translate-y-1/2 hover:bg-gray-100 p-1 rounded transition-colors duration-200"
                          >
                            {showPassword ? (
                              <EyeOff className="w-5 h-5" style={{ color: 'var(--text-secondary)' }} />
                            ) : (
                              <Eye className="w-5 h-5" style={{ color: 'var(--text-secondary)' }} />
                            )}
                          </button>
                        </div>
                      </div>

                      <div>
                        <label 
                          className="block text-sm font-medium mb-3" 
                          style={{ 
                            color: 'var(--text-primary)',
                            letterSpacing: '0.01em'
                          }}
                        >
                          Confirm New Password
                        </label>
                        <input
                          type="password"
                          value={passwordData.confirmPassword}
                          onChange={(e) => setPasswordData({ ...passwordData, confirmPassword: e.target.value })}
                          className="w-full px-4 py-3.5 rounded-xl border transition-all duration-300 focus:outline-none focus:ring-0 focus:shadow-lg hover:shadow-sm"
                          style={{
                            borderColor: 'var(--border-medium)',
                            backgroundColor: 'var(--bg-primary)',
                            color: 'var(--text-primary)',
                            boxShadow: '0 1px 3px rgba(0,0,0,0.05)',
                            fontSize: '16px',
                            fontWeight: '400'
                          }}
                          onFocus={(e) => {
                            e.target.style.borderColor = 'var(--primary)'
                            e.target.style.boxShadow = '0 0 0 3px rgba(16,185,129,0.1), 0 4px 12px rgba(0,0,0,0.1)'
                          }}
                          onBlur={(e) => {
                            e.target.style.borderColor = 'var(--border-medium)'
                            e.target.style.boxShadow = '0 1px 3px rgba(0,0,0,0.05)'
                          }}
                          placeholder="Confirm your new password"
                        />
                      </div>
                    </div>
                  </div>

                  <button
                    onClick={handlePasswordChange}
                    disabled={saving || !passwordData.newPassword || passwordData.newPassword !== passwordData.confirmPassword}
                    className="flex items-center gap-3 px-8 py-4 rounded-xl font-medium transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed hover:shadow-lg hover:scale-105 focus:outline-none focus:ring-2 focus:ring-offset-2"
                    style={{
                      backgroundColor: 'var(--primary)',
                      color: 'white',
                      boxShadow: '0 4px 12px rgba(16,185,129,0.2)',
                      fontSize: '16px',
                      fontWeight: '500',
                      letterSpacing: '0.01em'
                    }}
                  >
                    <Lock className="w-5 h-5" />
                    {saving ? 'Updating Password...' : 'Update Password'}
                  </button>
                </div>
              )}

              {activeSection === 'preferences' && (
                <div className="space-y-8">
                  <div className="border-b pb-6" style={{ borderColor: 'var(--border-light)' }}>
                    <h2 
                      className="text-3xl font-light mb-2" 
                      style={{ 
                        color: 'var(--text-primary)',
                        fontWeight: '300',
                        letterSpacing: '-0.01em'
                      }}
                    >
                      App Preferences
                    </h2>
                    <p 
                      className="text-sm" 
                      style={{ 
                        color: 'var(--text-secondary)',
                        opacity: '0.8'
                      }}
                    >
                      Customize your Care Connector experience and app behavior
                    </p>
                  </div>
                  
                  <div className="space-y-6">
                    {/* Language & Region */}
                    <div className="p-6 rounded-xl transition-all duration-200 hover:shadow-sm" style={{ backgroundColor: 'var(--bg-primary)', border: '1px solid var(--border-light)' }}>
                      <div className="flex items-center gap-3 mb-4">
                        <Globe className="w-6 h-6" style={{ color: 'var(--primary)' }} />
                        <h3 
                          className="text-lg font-medium" 
                          style={{ 
                            color: 'var(--text-primary)',
                            letterSpacing: '0.01em'
                          }}
                        >
                          Language & Region
                        </h3>
                      </div>
                      <div className="space-y-4">
                        <div>
                          <label 
                            className="block text-sm font-medium mb-2" 
                            style={{ 
                              color: 'var(--text-primary)',
                              letterSpacing: '0.01em'
                            }}
                          >
                            Display Language
                          </label>
                          <select
                            className="w-full px-4 py-3.5 rounded-xl border transition-all duration-300 focus:outline-none focus:ring-0 focus:shadow-lg hover:shadow-sm cursor-pointer"
                            style={{
                              borderColor: 'var(--border-medium)',
                              backgroundColor: 'var(--bg-primary)',
                              color: 'var(--text-primary)',
                              boxShadow: '0 1px 3px rgba(0,0,0,0.05)',
                              fontSize: '16px',
                              fontWeight: '400'
                            }}
                            defaultValue="en-US"
                          >
                            <option value="en-US">English (United States)</option>
                            <option value="en-GB">English (United Kingdom)</option>
                            <option value="es-ES">Español</option>
                            <option value="fr-FR">Français</option>
                            <option value="de-DE">Deutsch</option>
                          </select>
                        </div>
                        <div>
                          <label 
                            className="block text-sm font-medium mb-2" 
                            style={{ 
                              color: 'var(--text-primary)',
                              letterSpacing: '0.01em'
                            }}
                          >
                            Time Zone
                          </label>
                          <select
                            className="w-full px-4 py-3.5 rounded-xl border transition-all duration-300 focus:outline-none focus:ring-0 focus:shadow-lg hover:shadow-sm cursor-pointer"
                            style={{
                              borderColor: 'var(--border-medium)',
                              backgroundColor: 'var(--bg-primary)',
                              color: 'var(--text-primary)',
                              boxShadow: '0 1px 3px rgba(0,0,0,0.05)',
                              fontSize: '16px',
                              fontWeight: '400'
                            }}
                            defaultValue="America/New_York"
                          >
                            <option value="America/New_York">Eastern Time</option>
                            <option value="America/Chicago">Central Time</option>
                            <option value="America/Denver">Mountain Time</option>
                            <option value="America/Los_Angeles">Pacific Time</option>
                            <option value="UTC">UTC</option>
                          </select>
                        </div>
                      </div>
                    </div>

                    {/* Display Preferences */}
                    <div className="p-6 rounded-xl transition-all duration-200 hover:shadow-sm" style={{ backgroundColor: 'var(--bg-primary)', border: '1px solid var(--border-light)' }}>
                      <div className="flex items-center gap-3 mb-4">
                        <Eye className="w-6 h-6" style={{ color: 'var(--primary)' }} />
                        <h3 
                          className="text-lg font-medium" 
                          style={{ 
                            color: 'var(--text-primary)',
                            letterSpacing: '0.01em'
                          }}
                        >
                          Display Preferences
                        </h3>
                      </div>
                      <div className="space-y-4">
                        <div className="flex items-center justify-between p-4 rounded-xl" style={{ backgroundColor: 'var(--bg-primary)', border: '1px solid var(--border-light)' }}>
                          <div>
                            <h4 
                              className="font-medium text-base mb-1" 
                              style={{ 
                                color: 'var(--text-primary)',
                                letterSpacing: '0.01em'
                              }}
                            >
                              Compact View
                            </h4>
                            <p 
                              className="text-sm" 
                              style={{ 
                                color: 'var(--text-secondary)',
                                opacity: '0.7'
                              }}
                            >
                              Show more content by reducing spacing
                            </p>
                          </div>
                          <label className="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" className="sr-only peer" />
                            <div 
                              className="w-12 h-7 rounded-full transition-all duration-300" 
                              style={{
                                backgroundColor: 'var(--border-medium)',
                                boxShadow: 'inset 0 1px 3px rgba(0,0,0,0.1)'
                              }}
                            >
                              <div className="absolute top-[3px] left-[3px] w-5 h-5 bg-white border rounded-full transition-all duration-300 shadow-sm" />
                            </div>
                          </label>
                        </div>
                        <div className="flex items-center justify-between p-4 rounded-xl" style={{ backgroundColor: 'var(--bg-primary)', border: '1px solid var(--border-light)' }}>
                          <div>
                            <h4 
                              className="font-medium text-base mb-1" 
                              style={{ 
                                color: 'var(--text-primary)',
                                letterSpacing: '0.01em'
                              }}
                            >
                              Show Profile Pictures
                            </h4>
                            <p 
                              className="text-sm" 
                              style={{ 
                                color: 'var(--text-secondary)',
                                opacity: '0.7'
                              }}
                            >
                              Display profile images in listings
                            </p>
                          </div>
                          <label className="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" className="sr-only peer" defaultChecked />
                            <div 
                              className="w-12 h-7 rounded-full transition-all duration-300" 
                              style={{
                                backgroundColor: 'var(--primary)',
                                boxShadow: 'inset 0 1px 3px rgba(0,0,0,0.1)'
                              }}
                            >
                              <div className="absolute top-[3px] left-[3px] w-5 h-5 bg-white border rounded-full transition-all duration-300 shadow-sm" style={{ transform: 'translateX(20px)' }} />
                            </div>
                          </label>
                        </div>
                      </div>
                    </div>

                    {/* Accessibility */}
                    <div className="p-6 rounded-xl transition-all duration-200 hover:shadow-sm" style={{ backgroundColor: 'var(--bg-primary)', border: '1px solid var(--border-light)' }}>
                      <div className="flex items-center gap-3 mb-4">
                        <Shield className="w-6 h-6" style={{ color: 'var(--primary)' }} />
                        <h3 
                          className="text-lg font-medium" 
                          style={{ 
                            color: 'var(--text-primary)',
                            letterSpacing: '0.01em'
                          }}
                        >
                          Accessibility
                        </h3>
                      </div>
                      <div className="space-y-4">
                        <div className="flex items-center justify-between p-4 rounded-xl" style={{ backgroundColor: 'var(--bg-primary)', border: '1px solid var(--border-light)' }}>
                          <div>
                            <h4 
                              className="font-medium text-base mb-1" 
                              style={{ 
                                color: 'var(--text-primary)',
                                letterSpacing: '0.01em'
                              }}
                            >
                              High Contrast Mode
                            </h4>
                            <p 
                              className="text-sm" 
                              style={{ 
                                color: 'var(--text-secondary)',
                                opacity: '0.7'
                              }}
                            >
                              Improve text visibility and readability
                            </p>
                          </div>
                          <label className="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" className="sr-only peer" />
                            <div 
                              className="w-12 h-7 rounded-full transition-all duration-300" 
                              style={{
                                backgroundColor: 'var(--border-medium)',
                                boxShadow: 'inset 0 1px 3px rgba(0,0,0,0.1)'
                              }}
                            >
                              <div className="absolute top-[3px] left-[3px] w-5 h-5 bg-white border rounded-full transition-all duration-300 shadow-sm" />
                            </div>
                          </label>
                        </div>
                        <div className="flex items-center justify-between p-4 rounded-xl" style={{ backgroundColor: 'var(--bg-primary)', border: '1px solid var(--border-light)' }}>
                          <div>
                            <h4 
                              className="font-medium text-base mb-1" 
                              style={{ 
                                color: 'var(--text-primary)',
                                letterSpacing: '0.01em'
                              }}
                            >
                              Reduce Motion
                            </h4>
                            <p 
                              className="text-sm" 
                              style={{ 
                                color: 'var(--text-secondary)',
                                opacity: '0.7'
                              }}
                            >
                              Minimize animations and transitions
                            </p>
                          </div>
                          <label className="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" className="sr-only peer" />
                            <div 
                              className="w-12 h-7 rounded-full transition-all duration-300" 
                              style={{
                                backgroundColor: 'var(--border-medium)',
                                boxShadow: 'inset 0 1px 3px rgba(0,0,0,0.1)'
                              }}
                            >
                              <div className="absolute top-[3px] left-[3px] w-5 h-5 bg-white border rounded-full transition-all duration-300 shadow-sm" />
                            </div>
                          </label>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Settings
