import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { supabase } from '../lib/supabase';
import { Loader2, BarChart3, TrendingUp, TrendingDown, Users, DollarSign, Clock, Calendar, Filter, Download, Eye, Activity } from 'lucide-react';
import { format, parseISO, startOfMonth, endOfMonth, subMonths, startOfYear, endOfYear, isSameMonth, differenceInHours } from 'date-fns';

interface BookingMetrics {
  totalBookings: number;
  totalRevenue: number;
  totalHours: number;
  averageBookingValue: number;
  completedBookings: number;
  cancelledBookings: number;
  pendingBookings: number;
  monthlyGrowth: number;
  revenueGrowth: number;
}

const BookingAnalyticsPage: React.FC = () => {
  const navigate = useNavigate();
  
  const [metrics, setMetrics] = useState<BookingMetrics | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [dateRange, setDateRange] = useState('current_year');
  const [showFilters, setShowFilters] = useState(false);

  const dateRangeOptions = [
    { value: 'current_month', label: 'Current Month' },
    { value: 'last_3_months', label: 'Last 3 Months' },
    { value: 'current_year', label: 'Current Year' },
    { value: 'all_time', label: 'All Time' }
  ];

  useEffect(() => {
    const fetchAnalytics = async () => {
      setLoading(true);
      try {
        const { data: { user } } = await supabase.auth.getUser();
        
        if (!user) {
          setError("Please sign in to view booking analytics.");
          setLoading(false);
          return;
        }

        // Calculate date range
        const now = new Date();
        let startDate: Date;
        let endDate: Date = now;

        switch (dateRange) {
          case 'current_month':
            startDate = startOfMonth(now);
            endDate = endOfMonth(now);
            break;
          case 'last_3_months':
            startDate = startOfMonth(subMonths(now, 2));
            break;
          case 'current_year':
            startDate = startOfYear(now);
            endDate = endOfYear(now);
            break;
          default:
            startDate = new Date('2020-01-01');
        }

        let query = supabase
          .schema('care_connector')
          .from('service_provider_bookings')
          .select('*')
          .gte('start_time', startDate.toISOString());

        if (dateRange !== 'all_time') {
          query = query.lte('start_time', endDate.toISOString());
        }

        const { data: bookings, error: bookingsError } = await query.order('start_time', { ascending: true });

        if (bookingsError) {
          console.error('Bookings fetch error:', bookingsError);
          throw bookingsError;
        }

        const completedBookings = bookings?.filter(b => b.status === 'completed') || [];
        const cancelledBookings = bookings?.filter(b => b.status === 'cancelled_by_user' || b.status === 'cancelled_by_provider') || [];
        const pendingBookings = bookings?.filter(b => b.status === 'pending') || [];

        const totalRevenue = completedBookings.reduce((sum, booking) => sum + (booking.total_cost || 0), 0);
        const totalHours = completedBookings.reduce((sum, booking) => {
          if (booking.start_time && booking.end_time) {
            return sum + differenceInHours(parseISO(booking.end_time), parseISO(booking.start_time));
          }
          return sum;
        }, 0);

        const calculatedMetrics: BookingMetrics = {
          totalBookings: bookings?.length || 0,
          totalRevenue,
          totalHours,
          averageBookingValue: completedBookings.length > 0 ? totalRevenue / completedBookings.length : 0,
          completedBookings: completedBookings.length,
          cancelledBookings: cancelledBookings.length,
          pendingBookings: pendingBookings.length,
          monthlyGrowth: 0,
          revenueGrowth: 0
        };

        setMetrics(calculatedMetrics);
        console.log('Analytics data fetched successfully:', calculatedMetrics);

      } catch (err: any) {
        console.error("Error fetching analytics:", err.message);
        setError(err.message || "Failed to load analytics data.");
      } finally {
        setLoading(false);
      }
    };

    fetchAnalytics();
  }, [dateRange]);

  const exportData = () => {
    if (!metrics) return;
    
    const csvContent = [
      ['Booking Analytics Report'],
      ['Generated:', new Date().toISOString()],
      ['Date Range:', dateRange],
      [''],
      ['Summary Metrics'],
      ['Total Bookings', metrics.totalBookings],
      ['Total Revenue', `$${metrics.totalRevenue}`],
      ['Total Hours', metrics.totalHours],
      ['Average Booking Value', `$${metrics.averageBookingValue.toFixed(2)}`],
      ['Completed Bookings', metrics.completedBookings],
      ['Cancelled Bookings', metrics.cancelledBookings],
      ['Pending Bookings', metrics.pendingBookings]
    ].map(row => row.join(',')).join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.style.display = 'none';
    a.href = url;
    a.download = `booking-analytics-${format(new Date(), 'yyyy-MM-dd')}.csv`;
    document.body.appendChild(a);
    a.click();
    window.URL.revokeObjectURL(url);
    document.body.removeChild(a);
  };

  if (loading) {
    return (
      <div className="min-h-screen 50 flex items-center justify-center">
        <div className="flex items-center space-x-3">
          <Loader2 className="h-6 w-6 animate-spin text-var(--logo-green)" />
          <span className="600 font-medium">Loading analytics...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen 50 flex items-center justify-center">
        <div className="bg-white rounded-2xl shadow-sm border 200 p-8 max-w-md w-full mx-4">
          <div className="text-center">
            <BarChart3 className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h2 className="text-xl font-semibold 900 mb-2">Unable to Load Analytics</h2>
            <p className="600 mb-6">{error}</p>
            <div className="flex gap-3">
              <button
                onClick={() => navigate('/dashboard')}
                className="flex-1 bg-var(--logo-green) text-white px-4 py-3 rounded-xl font-medium hover:bg-green-600 transition-colors"
              >
                Dashboard
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen 50">
      {/* Header */}
      <div className="bg-white border-b 200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <BarChart3 className="h-8 w-8 text-var(--logo-green) mr-3" />
              <div>
                <h1 className="text-2xl font-bold 900">Booking Analytics</h1>
                <p className="600">Comprehensive booking performance insights</p>
              </div>
            </div>
            <div className="flex gap-3">
              <button
                onClick={() => setShowFilters(!showFilters)}
                className="flex items-center px-4 py-2 100 700 rounded-xl hover:200 transition-colors"
              >
                <Filter className="h-4 w-4 mr-2" />
                Filters
              </button>
              <button
                onClick={exportData}
                className="flex items-center px-4 py-2 bg-var(--logo-green) text-white rounded-xl hover:bg-green-600 transition-colors"
              >
                <Download className="h-4 w-4 mr-2" />
                Export
              </button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        
        {/* Filters */}
        {showFilters && (
          <div className="bg-white rounded-2xl shadow-sm border 200 p-6 mb-6">
            <h3 className="text-lg font-semibold 900 mb-4">Filter Analytics</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium 700 mb-2">Date Range</label>
                <select
                  value={dateRange}
                  onChange={(e) => setDateRange(e.target.value)}
                  className="w-full p-2 border 300 rounded-xl focus:outline-none focus:ring-2 focus:ring-var(--logo-green) focus:border-transparent"
                >
                  {dateRangeOptions.map((option) => (
                    <option key={option.value} value={option.value}>{option.label}</option>
                  ))}
                </select>
              </div>
            </div>
          </div>
        )}

        {/* Key Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-2xl shadow-sm border 200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium 600">Total Bookings</p>
                <p className="text-2xl font-bold 900">{metrics?.totalBookings || 0}</p>
              </div>
              <div className="h-12 w-12 bg-blue-100 rounded-xl flex items-center justify-center">
                <Calendar className="h-6 w-6 text-blue-600" />
              </div>
            </div>
            <div className="mt-4">
              <span className="text-sm 600">Across selected period</span>
            </div>
          </div>

          <div className="bg-white rounded-2xl shadow-sm border 200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium 600">Total Revenue</p>
                <p className="text-2xl font-bold 900">${metrics?.totalRevenue || 0}</p>
              </div>
              <div className="h-12 w-12 bg-green-100 rounded-xl flex items-center justify-center">
                <DollarSign className="h-6 w-6 text-green-600" />
              </div>
            </div>
            <div className="mt-4">
              <span className="text-sm 600">From completed bookings</span>
            </div>
          </div>

          <div className="bg-white rounded-2xl shadow-sm border 200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium 600">Total Hours</p>
                <p className="text-2xl font-bold 900">{metrics?.totalHours || 0}</p>
              </div>
              <div className="h-12 w-12 bg-purple-100 rounded-xl flex items-center justify-center">
                <Clock className="h-6 w-6 text-purple-600" />
              </div>
            </div>
            <div className="mt-4">
              <span className="text-sm 600">
                Avg: {metrics?.totalHours && metrics?.completedBookings ? 
                  (metrics.totalHours / metrics.completedBookings).toFixed(1) : 0}h per booking
              </span>
            </div>
          </div>

          <div className="bg-white rounded-2xl shadow-sm border 200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium 600">Avg Booking Value</p>
                <p className="text-2xl font-bold 900">${metrics?.averageBookingValue.toFixed(2) || 0}</p>
              </div>
              <div className="h-12 w-12 bg-orange-100 rounded-xl flex items-center justify-center">
                <Activity className="h-6 w-6 text-orange-600" />
              </div>
            </div>
            <div className="mt-4">
              <span className="text-sm 600">
                {metrics?.completedBookings || 0} completed bookings
              </span>
            </div>
          </div>
        </div>

        {/* Status Distribution */}
        <div className="bg-white rounded-2xl shadow-sm border 200 p-6 mb-8">
          <h3 className="text-lg font-semibold 900 mb-6">Booking Status Distribution</h3>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm 600">Completed</span>
              <div className="flex items-center">
                <div className="w-32 200 rounded-full h-3 mr-3">
                  <div 
                    className="bg-green-500 h-3 rounded-full" 
                    style={{ 
                      width: `${metrics?.totalBookings ? (metrics.completedBookings / metrics.totalBookings) * 100 : 0}%` 
                    }}
                  ></div>
                </div>
                <span className="text-sm font-medium 900 w-12">{metrics?.completedBookings || 0}</span>
                <span className="text-xs 500 ml-2">
                  ({metrics?.totalBookings ? ((metrics.completedBookings / metrics.totalBookings) * 100).toFixed(1) : 0}%)
                </span>
              </div>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-sm 600">Pending</span>
              <div className="flex items-center">
                <div className="w-32 200 rounded-full h-3 mr-3">
                  <div 
                    className="bg-yellow-500 h-3 rounded-full" 
                    style={{ 
                      width: `${metrics?.totalBookings ? (metrics.pendingBookings / metrics.totalBookings) * 100 : 0}%` 
                    }}
                  ></div>
                </div>
                <span className="text-sm font-medium 900 w-12">{metrics?.pendingBookings || 0}</span>
                <span className="text-xs 500 ml-2">
                  ({metrics?.totalBookings ? ((metrics.pendingBookings / metrics.totalBookings) * 100).toFixed(1) : 0}%)
                </span>
              </div>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-sm 600">Cancelled</span>
              <div className="flex items-center">
                <div className="w-32 200 rounded-full h-3 mr-3">
                  <div 
                    className="bg-red-500 h-3 rounded-full" 
                    style={{ 
                      width: `${metrics?.totalBookings ? (metrics.cancelledBookings / metrics.totalBookings) * 100 : 0}%` 
                    }}
                  ></div>
                </div>
                <span className="text-sm font-medium 900 w-12">{metrics?.cancelledBookings || 0}</span>
                <span className="text-xs 500 ml-2">
                  ({metrics?.totalBookings ? ((metrics.cancelledBookings / metrics.totalBookings) * 100).toFixed(1) : 0}%)
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="bg-blue-50 rounded-2xl p-6">
          <h3 className="text-lg font-semibold 900 mb-4">Quick Actions</h3>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Link
              to="/my-bookings"
              className="bg-white 700 px-6 py-4 rounded-xl font-medium hover:50 transition-colors border 200 text-center"
            >
              View All Bookings
            </Link>
            <Link
              to="/booking-history"
              className="bg-white 700 px-6 py-4 rounded-xl font-medium hover:50 transition-colors border 200 text-center"
            >
              Booking History
            </Link>
            <Link
              to="/provider-calendar/1"
              className="bg-white 700 px-6 py-4 rounded-xl font-medium hover:50 transition-colors border 200 text-center"
            >
              Provider Calendar
            </Link>
            <Link
              to="/dashboard"
              className="bg-white 700 px-6 py-4 rounded-xl font-medium hover:50 transition-colors border 200 text-center"
            >
              Back to Dashboard
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BookingAnalyticsPage;
