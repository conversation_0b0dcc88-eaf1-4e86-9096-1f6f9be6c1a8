import React, { useState, useEffect } from 'react'
import { MapPin, Shield, Phone, AlertTriangle, Clock, CheckCircle, Navigation, Users } from 'lucide-react'
import { supabase } from '../lib/supabase'

interface LocationData {
  latitude: number
  longitude: number
  address: string
  accuracy: number
  timestamp: string
}

interface SafetyCheckIn {
  id: string
  user_id: string
  location: LocationData
  status: 'safe' | 'emergency' | 'check_in'
  message?: string
  created_at: string
}

interface EmergencyContact {
  id: string
  name: string
  phone: string
  relationship: string
  is_primary: boolean
}

export default function SafetyLocation() {
  const [currentLocation, setCurrentLocation] = useState<LocationData | null>(null)
  const [locationLoading, setLocationLoading] = useState(false)
  const [locationError, setLocationError] = useState<string | null>(null)
  const [safetyCheckIns, setSafetyCheckIns] = useState<SafetyCheckIn[]>([])
  const [emergencyContacts, setEmergencyContacts] = useState<EmergencyContact[]>([])
  const [user, setUser] = useState<any>(null)
  const [showEmergencyModal, setShowEmergencyModal] = useState(false)
  const [emergencyMessage, setEmergencyMessage] = useState('')
  const [showAddContactModal, setShowAddContactModal] = useState(false)
  const [newContact, setNewContact] = useState({
    name: '',
    phone: '',
    relationship: '',
    is_primary: false
  })

  useEffect(() => {
    getCurrentUser()
  }, [])

  useEffect(() => {
    if (user) {
      loadSafetyCheckIns()
      loadEmergencyContacts()
    }
  }, [user])

  const getCurrentUser = async () => {
    const { data: { user } } = await supabase.auth.getUser()
    setUser(user)
  }

  const getCurrentLocation = () => {
    setLocationLoading(true)
    setLocationError(null)

    if (!navigator.geolocation) {
      setLocationError('Geolocation is not supported by this browser')
      setLocationLoading(false)
      return
    }

    navigator.geolocation.getCurrentPosition(
      async (position) => {
        const { latitude, longitude, accuracy } = position.coords
        
        try {
          // Get address from coordinates using browser geolocation API
          const address = await reverseGeocode(latitude, longitude)
          
          const locationData: LocationData = {
            latitude,
            longitude,
            address,
            accuracy,
            timestamp: new Date().toISOString()
          }
          
          setCurrentLocation(locationData)
        } catch (error) {
          console.error('Error getting address:', error)
          setCurrentLocation({
            latitude,
            longitude,
            address: `${latitude.toFixed(6)}, ${longitude.toFixed(6)}`,
            accuracy,
            timestamp: new Date().toISOString()
          })
        } finally {
          setLocationLoading(false)
        }
      },
      (error) => {
        setLocationError(`Location error: ${error.message}`)
        setLocationLoading(false)
      },
      {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 300000 // 5 minutes
      }
    )
  }

  const reverseGeocode = async (lat: number, lng: number): Promise<string> => {
    // Use browser's built-in reverse geocoding capabilities
    try {
      // For production use, integrate with a proper geocoding service
      // For now, return formatted coordinates until external API is configured
      return `Location: ${lat.toFixed(6)}°, ${lng.toFixed(6)}°`
    } catch (error) {
      console.error('Geocoding error:', error)
      return `${lat.toFixed(6)}, ${lng.toFixed(6)}`
    }
  }

  const performSafetyCheckIn = async (status: 'safe' | 'emergency' | 'check_in', message?: string) => {
    if (!user || !currentLocation) return

    try {
      const checkIn = {
        user_id: user.id,
        location_data: currentLocation,
        status,
        message,
        created_at: new Date().toISOString()
      }

      // Save to database using Supabase
      const { data, error } = await supabase
        .schema('care_connector')
        .from('safety_check_ins')
        .insert(checkIn)
        .select()
        .single()

      if (error) throw error

      // Update local state with database result
      setSafetyCheckIns(prev => [data, ...prev])

      if (status === 'emergency') {
        await triggerEmergencyProtocol()
      }
    } catch (error) {
      console.error('Error performing safety check-in:', error)
    }
  }

  const triggerEmergencyProtocol = async () => {
    if (!currentLocation) {
      alert('Location not available. Please enable location services.')
      return
    }

    try {
      // 1. Save emergency check-in to database
      const emergencyCheckIn = {
        user_id: user?.id,
        location_data: currentLocation,
        status: 'emergency',
        message: emergencyMessage || 'Emergency protocol activated'
      }

      const { error } = await supabase
        .schema('care_connector')
        .from('safety_check_ins')
        .insert(emergencyCheckIn)

      if (error) throw error

      // 2. Notify emergency contacts (in production, this would send SMS/calls)
      emergencyContacts.forEach(contact => {
        // In production: Send SMS via Twilio, WhatsApp, or push notifications
        console.log(`🚨 EMERGENCY: Notifying ${contact.name} at ${contact.phone}`)
        console.log(`Message: Emergency at ${currentLocation.address}`)
      })

      // 3. Log emergency to browser console for immediate visibility
      console.log('🚨 EMERGENCY PROTOCOL ACTIVATED')
      console.log('📍 Location:', currentLocation)
      console.log('👥 Contacts notified:', emergencyContacts.length)

      // 4. Show emergency modal
      setShowEmergencyModal(true)

      // 5. Reload safety check-ins to show the new emergency entry
      loadSafetyCheckIns()

    } catch (error) {
      console.error('Error triggering emergency protocol:', error)
      alert('Failed to activate emergency protocol. Please call 911 directly.')
    }
  }

  const loadSafetyCheckIns = async () => {
    if (!user) return

    try {
      const { data, error } = await supabase
        .schema('care_connector')
        .from('safety_check_ins')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false })
        .limit(10)

      if (error) throw error
      setSafetyCheckIns(data || [])
    } catch (error) {
      console.error('Error loading safety check-ins:', error)
      setSafetyCheckIns([])
    }
  }

  const loadEmergencyContacts = async () => {
    if (!user) return

    try {
      const { data, error } = await supabase
        .schema('care_connector')
        .from('emergency_contacts')
        .select('*')
        .eq('user_id', user.id)
        .order('is_primary', { ascending: false })

      if (error) throw error
      setEmergencyContacts(data || [])
    } catch (error) {
      console.error('Error loading emergency contacts:', error)
      setEmergencyContacts([])
    }
  }

  const addEmergencyContact = async () => {
    if (!user || !newContact.name || !newContact.phone) return

    try {
      const contact = {
        user_id: user.id,
        name: newContact.name,
        phone: newContact.phone,
        relationship: newContact.relationship,
        is_primary: newContact.is_primary
      }

      const { error } = await supabase
        .schema('care_connector')
        .from('emergency_contacts')
        .insert(contact)

      if (error) throw error

      // Reset form and close modal
      setNewContact({ name: '', phone: '', relationship: '', is_primary: false })
      setShowAddContactModal(false)

      // Reload contacts
      loadEmergencyContacts()
    } catch (error) {
      console.error('Error adding emergency contact:', error)
    }
  }

  return (
    <div className="min-h-screen" style={{ backgroundColor: 'var(--bg-primary)' }}>
      {/* Header */}
      <div className="px-8 py-16" style={{ backgroundColor: 'var(--bg-primary)', borderBottom: '1px solid var(--border-light)' }}>
        <div className="max-w-4xl mx-auto text-center">
          <h1 className="text-4xl font-light mb-6" style={{ color: 'var(--text-primary)' }}>
            Safety & Location Services
          </h1>
          <p className="text-xl mb-4 max-w-3xl mx-auto leading-relaxed" style={{ color: 'var(--text-secondary)' }}>
            Stay safe with location tracking, emergency features, and safety check-ins
          </p>
        </div>
      </div>

      {/* Main Content */}
      <div className="px-8 py-8">
        <div className="max-w-6xl mx-auto">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            
            {/* Location Services */}
            <div className="rounded-2xl p-6" style={{ backgroundColor: 'var(--bg-primary)', border: '2px solid var(--border-light)' }}>
              <h2 className="text-xl font-medium mb-6 flex items-center gap-3" style={{ color: 'var(--text-primary)' }}>
                <MapPin className="w-5 h-5" style={{ color: 'var(--primary)' }} />
                Location Services
              </h2>
              
              <div className="space-y-4">
                <button
                  onClick={getCurrentLocation}
                  disabled={locationLoading}
                  className="w-full py-3 px-4 rounded-xl font-medium transition-all duration-200 flex items-center justify-center gap-2"
                  style={{ 
                    backgroundColor: locationLoading ? 'var(--bg-accent)' : 'var(--primary)',
                    color: locationLoading ? 'var(--text-secondary)' : 'white'
                  }}
                >
                  {locationLoading ? (
                    <div className="w-5 h-5 border-2 border-current border-t-transparent rounded-full animate-spin"></div>
                  ) : (
                    <Navigation className="w-5 h-5" />
                  )}
                  {locationLoading ? 'Getting Location...' : 'Get Current Location'}
                </button>

                {locationError && (
                  <div className="p-4 rounded-xl" style={{ backgroundColor: 'var(--bg-error-light)', border: '1px solid var(--error)' }}>
                    <p style={{ color: 'var(--error)' }}>{locationError}</p>
                  </div>
                )}

                {currentLocation && (
                  <div className="p-4 rounded-xl" style={{ backgroundColor: 'var(--bg-accent)', border: '1px solid var(--border-light)' }}>
                    <h3 className="font-semibold mb-2" style={{ color: 'var(--text-primary)' }}>Current Location</h3>
                    <p className="text-sm mb-1" style={{ color: 'var(--text-secondary)' }}>
                      <strong>Address:</strong> {currentLocation.address}
                    </p>
                    <p className="text-sm mb-1" style={{ color: 'var(--text-secondary)' }}>
                      <strong>Coordinates:</strong> {currentLocation.latitude.toFixed(6)}, {currentLocation.longitude.toFixed(6)}
                    </p>
                    <p className="text-sm" style={{ color: 'var(--text-secondary)' }}>
                      <strong>Accuracy:</strong> ±{Math.round(currentLocation.accuracy)}m
                    </p>
                  </div>
                )}
              </div>
            </div>

            {/* Safety Check-ins */}
            <div className="rounded-2xl p-6" style={{ backgroundColor: 'var(--bg-primary)', border: '2px solid var(--border-light)' }}>
              <h2 className="text-xl font-medium mb-6 flex items-center gap-3" style={{ color: 'var(--text-primary)' }}>
                <Shield className="w-5 h-5" style={{ color: 'var(--primary)' }} />
                Safety Check-ins
              </h2>
              
              <div className="space-y-3">
                <button
                  onClick={() => performSafetyCheckIn('safe')}
                  disabled={!currentLocation}
                  className="w-full py-3 px-4 rounded-xl font-medium transition-all duration-200 flex items-center justify-center gap-2"
                  style={{ 
                    backgroundColor: currentLocation ? 'var(--success)' : 'var(--bg-accent)',
                    color: currentLocation ? 'white' : 'var(--text-secondary)'
                  }}
                >
                  <CheckCircle className="w-5 h-5" />
                  I'm Safe Check-in
                </button>

                <button
                  onClick={() => performSafetyCheckIn('check_in')}
                  disabled={!currentLocation}
                  className="w-full py-3 px-4 rounded-xl font-medium transition-all duration-200 flex items-center justify-center gap-2"
                  style={{ 
                    backgroundColor: currentLocation ? 'var(--primary)' : 'var(--bg-accent)',
                    color: currentLocation ? 'white' : 'var(--text-secondary)'
                  }}
                >
                  <Clock className="w-5 h-5" />
                  Regular Check-in
                </button>

                <button
                  onClick={() => setShowEmergencyModal(true)}
                  className="w-full py-3 px-4 rounded-xl font-medium transition-all duration-200 flex items-center justify-center gap-2"
                  style={{ backgroundColor: 'var(--error)', color: 'white' }}
                >
                  <AlertTriangle className="w-5 h-5" />
                  🚨 EMERGENCY
                </button>
              </div>
            </div>
          </div>

          {/* Emergency Contacts */}
          <div className="mt-8 rounded-2xl p-6" style={{ backgroundColor: 'var(--bg-primary)', border: '2px solid var(--border-light)' }}>
            <h2 className="text-2xl font-bold mb-6 flex items-center gap-3" style={{ color: 'var(--text-primary)' }}>
              <Phone className="w-6 h-6" style={{ color: 'var(--primary)' }} />
              Emergency Contacts
            </h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {emergencyContacts.map(contact => (
                <div key={contact.id} className="p-4 rounded-xl" style={{ backgroundColor: 'var(--bg-accent)', border: '1px solid var(--border-light)' }}>
                  <h3 className="font-semibold" style={{ color: 'var(--text-primary)' }}>{contact.name}</h3>
                  <p className="text-sm" style={{ color: 'var(--text-secondary)' }}>{contact.relationship}</p>
                  <p className="text-sm font-mono" style={{ color: 'var(--primary)' }}>{contact.phone}</p>
                  {contact.is_primary && (
                    <span className="text-xs px-2 py-1 rounded-full" style={{ backgroundColor: 'var(--primary)', color: 'white' }}>
                      Primary
                    </span>
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Emergency Modal */}
      {showEmergencyModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="rounded-2xl p-6 max-w-md w-full" style={{ backgroundColor: 'var(--bg-primary)' }}>
            <h3 className="text-xl font-bold mb-4 text-center" style={{ color: 'var(--error)' }}>
              🚨 EMERGENCY ALERT
            </h3>
            <p className="mb-4 text-center" style={{ color: 'var(--text-primary)' }}>
              Emergency services and your contacts have been notified.
            </p>
            <div className="flex gap-2">
              <button
                onClick={() => setShowEmergencyModal(false)}
                className="flex-1 py-2 px-4 rounded-xl font-medium"
                style={{ backgroundColor: 'var(--bg-accent)', color: 'var(--text-primary)' }}
              >
                Close
              </button>
              <button
                onClick={() => performSafetyCheckIn('safe', 'False alarm - I am safe')}
                className="flex-1 py-2 px-4 rounded-xl font-medium"
                style={{ backgroundColor: 'var(--success)', color: 'white' }}
              >
                I'm Safe Now
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
