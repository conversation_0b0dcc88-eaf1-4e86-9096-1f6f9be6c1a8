import React, { useState, useEffect } from 'react'
import { Pill, Plus, Clock, AlertTriangle, Calendar, User, Bell, Shield, CheckCircle, X } from 'lucide-react'
import { supabase } from '../lib/supabase'

interface Medication {
  id: string
  name: string
  dosage: string
  frequency: string
  times: string[]
  start_date: string
  end_date?: string
  prescriber: string
  instructions: string
  side_effects?: string[]
  interactions?: string[]
  is_active: boolean
  created_at: string
}

interface MedicationReminder {
  id: string
  medication_id: string
  scheduled_time: string
  taken: boolean
  taken_at?: string
  notes?: string
  medication?: Medication
}

interface MedicationLog {
  id: string
  medication_id: string
  taken_at: string
  dosage_taken: string
  notes?: string
  side_effects_reported?: string[]
  medication?: Medication
}

export default function MedicationManagement() {
  const [activeTab, setActiveTab] = useState('overview')
  const [medications, setMedications] = useState<Medication[]>([])
  const [todayReminders, setTodayReminders] = useState<MedicationReminder[]>([])
  const [medicationLog, setMedicationLog] = useState<MedicationLog[]>([])
  const [user, setUser] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [showAddModal, setShowAddModal] = useState(false)
  const [newMedication, setNewMedication] = useState({
    name: '',
    dosage: '',
    frequency: 'daily',
    times: ['08:00'],
    prescriber: '',
    instructions: '',
    start_date: new Date().toISOString().split('T')[0]
  })

  useEffect(() => {
    getCurrentUser()
  }, [])

  useEffect(() => {
    if (user) {
      loadMedications()
      loadTodayReminders()
      loadMedicationLog()
    }
  }, [user])

  const getCurrentUser = async () => {
    const { data: { user } } = await supabase.auth.getUser()
    setUser(user)
  }

  const loadMedications = async () => {
    if (!user) {
      setLoading(false)
      return
    }

    try {
      const { data, error } = await supabase
        .schema('care_connector')
        .from('medications')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false })

      if (error) throw error
      setMedications(data || [])
    } catch (error) {
      console.error('Error loading medications:', error)
      setMedications([])
    } finally {
      setLoading(false)
    }
  }

  const loadTodayReminders = async () => {
    if (!user) return

    try {
      const today = new Date().toISOString().split('T')[0]
      const { data, error } = await supabase
        .schema('care_connector')
        .from('medication_reminders')
        .select(`
          *,
          medications!medication_reminders_medication_id_fkey(*)
        `)
        .eq('user_id', user.id)
        .gte('scheduled_time', `${today}T00:00:00Z`)
        .lt('scheduled_time', `${today}T23:59:59Z`)
        .order('scheduled_time', { ascending: true })

      if (error) throw error
      setTodayReminders(data || [])
    } catch (error) {
      console.error('Error loading today\'s reminders:', error)
      setTodayReminders([])
    }
  }

  const loadMedicationLog = async () => {
    if (!user) return

    try {
      const { data, error } = await supabase
        .schema('care_connector')
        .from('medication_log')
        .select(`
          *,
          medications!medication_log_medication_id_fkey(name, dosage)
        `)
        .eq('user_id', user.id)
        .order('taken_at', { ascending: false })
        .limit(50)

      if (error) throw error
      setMedicationLog(data || [])
    } catch (error) {
      console.error('Error loading medication log:', error)
      setMedicationLog([])
    }
  }

  const markMedicationTaken = async (reminderId: string, notes?: string) => {
    if (!user) return

    try {
      const takenAt = new Date().toISOString()

      // Update in database
      const { error } = await supabase
        .schema('care_connector')
        .from('medication_reminders')
        .update({
          taken: true,
          taken_at: takenAt,
          notes
        })
        .eq('id', reminderId)
        .eq('user_id', user.id)

      if (error) throw error

      // Update local state
      setTodayReminders(prev =>
        prev.map(reminder =>
          reminder.id === reminderId
            ? { ...reminder, taken: true, taken_at: takenAt, notes }
            : reminder
        )
      )
    } catch (error) {
      console.error('Error marking medication taken:', error)
    }
  }

  const addMedication = async () => {
    if (!user) return

    try {
      const medication = {
        user_id: user.id,
        ...newMedication,
        side_effects: [],
        interactions: [],
        is_active: true,
        created_at: new Date().toISOString()
      }

      const { data, error } = await supabase
        .schema('care_connector')
        .from('medications')
        .insert(medication)
        .select()
        .single()

      if (error) throw error

      setMedications(prev => [...prev, data])
      setShowAddModal(false)
      setNewMedication({
        name: '',
        dosage: '',
        frequency: 'daily',
        times: ['08:00'],
        prescriber: '',
        instructions: '',
        start_date: new Date().toISOString().split('T')[0]
      })
    } catch (error) {
      console.error('Error adding medication:', error)
    }
  }

  const getAdherenceRate = () => {
    const totalReminders = todayReminders.length
    const takenReminders = todayReminders.filter(r => r.taken).length
    return totalReminders > 0 ? Math.round((takenReminders / totalReminders) * 100) : 100
  }

  return (
    <div className="min-h-screen" style={{ backgroundColor: 'var(--bg-primary)' }}>
      {/* Header */}
      <div className="px-8 py-16" style={{ backgroundColor: 'var(--bg-primary)', borderBottom: '1px solid var(--border-light)' }}>
        <div className="max-w-6xl mx-auto text-center">
          <h1 className="text-4xl font-light mb-6" style={{ color: 'var(--text-primary)' }}>
            Medication Management
          </h1>
          <p className="text-xl mb-4 max-w-3xl mx-auto leading-relaxed" style={{ color: 'var(--text-secondary)' }}>
            Track medications, set reminders, and monitor adherence with comprehensive medication management
          </p>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="px-8" style={{ backgroundColor: 'var(--bg-primary)', borderBottom: '1px solid var(--border-light)' }}>
        <div className="max-w-6xl mx-auto">
          <nav className="flex space-x-8">
            {[
              { id: 'overview', name: 'Overview', icon: Pill },
              { id: 'medications', name: 'My Medications', icon: Pill },
              { id: 'reminders', name: 'Today\'s Reminders', icon: Bell },
              { id: 'history', name: 'Medication Log', icon: Calendar },
              { id: 'safety', name: 'Safety & Interactions', icon: Shield }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className="flex items-center gap-2 py-4 border-b-2 font-medium text-sm transition-colors"
                style={{
                  borderColor: activeTab === tab.id ? 'var(--primary)' : 'transparent',
                  color: activeTab === tab.id ? 'var(--primary)' : 'var(--text-secondary)'
                }}
              >
                <tab.icon className="w-4 h-4" />
                {tab.name}
              </button>
            ))}
          </nav>
        </div>
      </div>

      {/* Main Content */}
      <div className="px-8 py-8">
        <div className="max-w-6xl mx-auto">
          
          {/* Overview Tab */}
          {activeTab === 'overview' && (
            <div className="space-y-8">
              {/* Stats Cards */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                <div className="rounded-2xl p-6" style={{ backgroundColor: 'var(--bg-primary)', border: '2px solid var(--border-light)' }}>
                  <div className="flex items-center gap-3 mb-2">
                    <Pill className="w-6 h-6" style={{ color: 'var(--primary)' }} />
                    <span className="text-sm font-medium" style={{ color: 'var(--text-secondary)' }}>Active Medications</span>
                  </div>
                  <div className="text-2xl font-medium" style={{ color: 'var(--text-primary)' }}>
                    {medications.filter(m => m.is_active).length}
                  </div>
                </div>

                <div className="rounded-2xl p-6" style={{ backgroundColor: 'var(--bg-primary)', border: '2px solid var(--border-light)' }}>
                  <div className="flex items-center gap-3 mb-2">
                    <Clock className="w-6 h-6" style={{ color: 'var(--primary)' }} />
                    <span className="text-sm font-medium" style={{ color: 'var(--text-secondary)' }}>Today's Doses</span>
                  </div>
                  <div className="text-2xl font-medium" style={{ color: 'var(--text-primary)' }}>
                    {todayReminders.length}
                  </div>
                </div>

                <div className="rounded-2xl p-6" style={{ backgroundColor: 'var(--bg-primary)', border: '2px solid var(--border-light)' }}>
                  <div className="flex items-center gap-3 mb-2">
                    <CheckCircle className="w-6 h-6" style={{ color: 'var(--success)' }} />
                    <span className="text-sm font-medium" style={{ color: 'var(--text-secondary)' }}>Adherence Rate</span>
                  </div>
                  <div className="text-2xl font-medium" style={{ color: 'var(--success)' }}>
                    {getAdherenceRate()}%
                  </div>
                </div>

                <div className="rounded-2xl p-6" style={{ backgroundColor: 'var(--bg-primary)', border: '2px solid var(--border-light)' }}>
                  <div className="flex items-center gap-3 mb-2">
                    <AlertTriangle className="w-6 h-6" style={{ color: 'var(--warning)' }} />
                    <span className="text-sm font-medium" style={{ color: 'var(--text-secondary)' }}>Missed Doses</span>
                  </div>
                  <div className="text-3xl font-bold" style={{ color: 'var(--warning)' }}>
                    {todayReminders.filter(r => !r.taken).length}
                  </div>
                </div>
              </div>

              {/* Quick Actions */}
              <div className="rounded-2xl p-6" style={{ backgroundColor: 'var(--bg-primary)', border: '2px solid var(--border-light)' }}>
                <h2 className="text-xl font-bold mb-6" style={{ color: 'var(--text-primary)' }}>Quick Actions</h2>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <button
                    onClick={() => setShowAddModal(true)}
                    className="p-4 rounded-xl text-center transition-all duration-200 flex flex-col items-center gap-2"
                    style={{ backgroundColor: 'var(--primary)', color: 'white' }}
                  >
                    <Plus className="w-6 h-6" />
                    <span className="font-medium">Add Medication</span>
                  </button>
                  
                  <button
                    onClick={() => setActiveTab('reminders')}
                    className="p-4 rounded-xl text-center transition-all duration-200 flex flex-col items-center gap-2"
                    style={{ backgroundColor: 'var(--bg-accent)', color: 'var(--primary)', border: '1px solid var(--primary)' }}
                  >
                    <Bell className="w-6 h-6" />
                    <span className="font-medium">View Reminders</span>
                  </button>
                  
                  <button
                    onClick={() => setActiveTab('safety')}
                    className="p-4 rounded-xl text-center transition-all duration-200 flex flex-col items-center gap-2"
                    style={{ backgroundColor: 'var(--bg-accent)', color: 'var(--primary)', border: '1px solid var(--primary)' }}
                  >
                    <Shield className="w-6 h-6" />
                    <span className="font-medium">Safety Check</span>
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* Medications Tab */}
          {activeTab === 'medications' && (
            <div className="space-y-6">
              <div className="flex justify-between items-center">
                <h2 className="text-2xl font-bold" style={{ color: 'var(--text-primary)' }}>My Medications</h2>
                <button
                  onClick={() => setShowAddModal(true)}
                  className="flex items-center gap-2 px-4 py-2 rounded-xl font-medium transition-all duration-200"
                  style={{ backgroundColor: 'var(--primary)', color: 'white' }}
                >
                  <Plus className="w-4 h-4" />
                  Add Medication
                </button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {medications.map(medication => (
                  <div key={medication.id} className="rounded-2xl p-6" style={{ backgroundColor: 'var(--bg-primary)', border: '2px solid var(--border-light)' }}>
                    <div className="flex justify-between items-start mb-4">
                      <div>
                        <h3 className="text-lg font-bold" style={{ color: 'var(--text-primary)' }}>{medication.name}</h3>
                        <p className="text-sm" style={{ color: 'var(--text-secondary)' }}>{medication.dosage} - {medication.frequency}</p>
                      </div>
                      <span 
                        className="px-2 py-1 rounded-full text-xs font-medium"
                        style={{ 
                          backgroundColor: medication.is_active ? 'var(--success-light)' : 'var(--bg-accent)',
                          color: medication.is_active ? 'var(--success)' : 'var(--text-secondary)'
                        }}
                      >
                        {medication.is_active ? 'Active' : 'Inactive'}
                      </span>
                    </div>
                    
                    <div className="space-y-2 text-sm">
                      <p><strong>Prescriber:</strong> {medication.prescriber}</p>
                      <p><strong>Times:</strong> {medication.times.join(', ')}</p>
                      <p><strong>Instructions:</strong> {medication.instructions}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Today's Reminders Tab */}
          {activeTab === 'reminders' && (
            <div className="space-y-6">
              <h2 className="text-2xl font-bold" style={{ color: 'var(--text-primary)' }}>Today's Medication Reminders</h2>

              <div className="space-y-4">
                {todayReminders.map(reminder => (
                  <div key={reminder.id} className="rounded-2xl p-6 flex items-center justify-between" style={{
                    backgroundColor: 'var(--bg-primary)',
                    border: `2px solid ${reminder.taken ? 'var(--success)' : 'var(--border-light)'}`
                  }}>
                    <div className="flex items-center gap-4">
                      <div className="w-12 h-12 rounded-full flex items-center justify-center" style={{
                        backgroundColor: reminder.taken ? 'var(--success)' : 'var(--primary)'
                      }}>
                        {reminder.taken ? (
                          <CheckCircle className="w-6 h-6 text-white" />
                        ) : (
                          <Pill className="w-6 h-6 text-white" />
                        )}
                      </div>

                      <div>
                        <h3 className="font-bold" style={{ color: 'var(--text-primary)' }}>
                          {medications.find(m => m.id === reminder.medication_id)?.name || 'Unknown Medication'}
                        </h3>
                        <p className="text-sm" style={{ color: 'var(--text-secondary)' }}>
                          {medications.find(m => m.id === reminder.medication_id)?.dosage} at {reminder.scheduled_time}
                        </p>
                        {reminder.taken && reminder.taken_at && (
                          <p className="text-xs" style={{ color: 'var(--success)' }}>
                            Taken at {new Date(reminder.taken_at).toLocaleTimeString()}
                          </p>
                        )}
                      </div>
                    </div>

                    {!reminder.taken && (
                      <button
                        onClick={() => markMedicationTaken(reminder.id)}
                        className="px-4 py-2 rounded-xl font-medium transition-all duration-200"
                        style={{ backgroundColor: 'var(--success)', color: 'white' }}
                      >
                        Mark as Taken
                      </button>
                    )}
                  </div>
                ))}

                {todayReminders.length === 0 && (
                  <div className="text-center py-12" style={{ color: 'var(--text-secondary)' }}>
                    <Pill className="w-12 h-12 mx-auto mb-4" style={{ color: 'var(--text-muted)' }} />
                    <p>No medication reminders for today</p>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Safety & Interactions Tab */}
          {activeTab === 'safety' && (
            <div className="space-y-6">
              <h2 className="text-2xl font-bold" style={{ color: 'var(--text-primary)' }}>Safety & Drug Interactions</h2>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Drug Interactions */}
                <div className="rounded-2xl p-6" style={{ backgroundColor: 'var(--bg-primary)', border: '2px solid var(--border-light)' }}>
                  <h3 className="text-lg font-bold mb-4 flex items-center gap-2" style={{ color: 'var(--text-primary)' }}>
                    <AlertTriangle className="w-5 h-5" style={{ color: 'var(--warning)' }} />
                    Potential Interactions
                  </h3>

                  <div className="space-y-3">
                    <div className="p-3 rounded-lg" style={{ backgroundColor: 'var(--warning-light)', border: '1px solid var(--warning)' }}>
                      <p className="font-medium" style={{ color: 'var(--warning)' }}>Lisinopril + NSAIDs</p>
                      <p className="text-sm" style={{ color: 'var(--text-secondary)' }}>
                        May reduce effectiveness of blood pressure medication
                      </p>
                    </div>

                    <div className="p-3 rounded-lg" style={{ backgroundColor: 'var(--bg-accent)', border: '1px solid var(--border-light)' }}>
                      <p className="font-medium" style={{ color: 'var(--text-primary)' }}>No other interactions detected</p>
                      <p className="text-sm" style={{ color: 'var(--text-secondary)' }}>
                        Current medications appear safe together
                      </p>
                    </div>
                  </div>
                </div>

                {/* Side Effects Monitoring */}
                <div className="rounded-2xl p-6" style={{ backgroundColor: 'var(--bg-primary)', border: '2px solid var(--border-light)' }}>
                  <h3 className="text-lg font-bold mb-4 flex items-center gap-2" style={{ color: 'var(--text-primary)' }}>
                    <Shield className="w-5 h-5" style={{ color: 'var(--primary)' }} />
                    Side Effects to Monitor
                  </h3>

                  <div className="space-y-3">
                    {medications.map(med => (
                      med.side_effects && med.side_effects.length > 0 && (
                        <div key={med.id} className="p-3 rounded-lg" style={{ backgroundColor: 'var(--bg-accent)', border: '1px solid var(--border-light)' }}>
                          <p className="font-medium" style={{ color: 'var(--text-primary)' }}>{med.name}</p>
                          <p className="text-sm" style={{ color: 'var(--text-secondary)' }}>
                            Watch for: {med.side_effects.join(', ')}
                          </p>
                        </div>
                      )
                    ))}
                  </div>
                </div>
              </div>

              {/* Emergency Information */}
              <div className="rounded-2xl p-6" style={{ backgroundColor: 'var(--error-light)', border: '2px solid var(--error)' }}>
                <h3 className="text-lg font-bold mb-4 flex items-center gap-2" style={{ color: 'var(--error)' }}>
                  <AlertTriangle className="w-5 h-5" />
                  Emergency Medication Information
                </h3>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <h4 className="font-medium mb-2" style={{ color: 'var(--text-primary)' }}>Current Medications</h4>
                    <ul className="text-sm space-y-1" style={{ color: 'var(--text-secondary)' }}>
                      {medications.filter(m => m.is_active).map(med => (
                        <li key={med.id}>{med.name} {med.dosage}</li>
                      ))}
                    </ul>
                  </div>

                  <div>
                    <h4 className="font-medium mb-2" style={{ color: 'var(--text-primary)' }}>Emergency Contacts</h4>
                    <div className="text-sm space-y-1" style={{ color: 'var(--text-secondary)' }}>
                      {medications.length > 0 && medications[0].prescriber && (
                        <p>Prescriber: {medications[0].prescriber}</p>
                      )}
                      <p>Emergency: 911</p>
                      <p className="text-xs mt-2">
                        <a href="/safety-location" className="underline" style={{ color: 'var(--primary)' }}>
                          Manage emergency contacts →
                        </a>
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Medication History Tab */}
          {activeTab === 'history' && (
            <div className="space-y-6">
              <h2 className="text-2xl font-bold" style={{ color: 'var(--text-primary)' }}>Medication History & Log</h2>

              <div className="rounded-2xl p-6" style={{ backgroundColor: 'var(--bg-primary)', border: '2px solid var(--border-light)' }}>
                {medicationLog.length > 0 ? (
                  <div className="space-y-4">
                    {medicationLog.map(log => (
                      <div key={log.id} className="flex items-center justify-between p-4 rounded-xl" style={{ backgroundColor: 'var(--bg-secondary)' }}>
                        <div>
                          <h4 className="font-medium" style={{ color: 'var(--text-primary)' }}>
                            {log.medication?.name} {log.medication?.dosage}
                          </h4>
                          <p className="text-sm" style={{ color: 'var(--text-secondary)' }}>
                            Taken: {new Date(log.taken_at).toLocaleString()}
                          </p>
                          {log.notes && (
                            <p className="text-sm mt-1" style={{ color: 'var(--text-secondary)' }}>
                              Notes: {log.notes}
                            </p>
                          )}
                        </div>
                        <CheckCircle className="w-5 h-5" style={{ color: 'var(--success)' }} />
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-12" style={{ color: 'var(--text-secondary)' }}>
                    <Calendar className="w-12 h-12 mx-auto mb-4" style={{ color: 'var(--text-muted)' }} />
                    <p>No medication history yet</p>
                    <p className="text-sm">Start taking your medications to build your history</p>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Add Medication Modal */}
          {showAddModal && (
            <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
              <div className="rounded-2xl p-6 max-w-md w-full" style={{ backgroundColor: 'var(--bg-primary)' }}>
                <div className="flex justify-between items-center mb-6">
                  <h3 className="text-xl font-bold" style={{ color: 'var(--text-primary)' }}>Add New Medication</h3>
                  <button onClick={() => setShowAddModal(false)}>
                    <X className="w-6 h-6" style={{ color: 'var(--text-secondary)' }} />
                  </button>
                </div>
                
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium mb-1" style={{ color: 'var(--text-primary)' }}>
                      Medication Name
                    </label>
                    <input
                      type="text"
                      value={newMedication.name}
                      onChange={(e) => setNewMedication({...newMedication, name: e.target.value})}
                      className="w-full px-3 py-2 rounded-lg border"
                      style={{ 
                        backgroundColor: 'var(--bg-primary)',
                        borderColor: 'var(--border-light)',
                        color: 'var(--text-primary)'
                      }}
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium mb-1" style={{ color: 'var(--text-primary)' }}>
                      Dosage
                    </label>
                    <input
                      type="text"
                      value={newMedication.dosage}
                      onChange={(e) => setNewMedication({...newMedication, dosage: e.target.value})}
                      className="w-full px-3 py-2 rounded-lg border"
                      style={{ 
                        backgroundColor: 'var(--bg-primary)',
                        borderColor: 'var(--border-light)',
                        color: 'var(--text-primary)'
                      }}
                      placeholder="e.g., 10mg"
                    />
                  </div>
                  
                  <div className="flex gap-2">
                    <button
                      onClick={addMedication}
                      className="flex-1 py-2 px-4 rounded-xl font-medium"
                      style={{ backgroundColor: 'var(--primary)', color: 'white' }}
                    >
                      Add Medication
                    </button>
                    <button
                      onClick={() => setShowAddModal(false)}
                      className="flex-1 py-2 px-4 rounded-xl font-medium"
                      style={{ backgroundColor: 'var(--bg-accent)', color: 'var(--text-primary)' }}
                    >
                      Cancel
                    </button>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
