import React, { useState, useEffect } from 'react';
import { Navigate, Link } from 'react-router-dom';
import { supabase } from '../lib/supabase';

interface UserProfile {
  id: string;
  is_admin?: boolean;
}

const AdminDashboardPage: React.FC = () => {
  const [user, setUser] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [profileLoading, setProfileLoading] = useState(true);
  const [profileError, setProfileError] = useState<string | null>(null);
  const [stats, setStats] = useState<any>(null);
  const [statsLoading, setStatsLoading] = useState(true);

  // Check authentication and fetch user
  useEffect(() => {
    const checkAuth = async () => {
      const { data: { session } } = await supabase.auth.getSession();
      setUser(session?.user || null);
      setLoading(false);
    };
    checkAuth();
  }, []);

  // Fetch user profile to check admin status
  useEffect(() => {
    const fetchProfile = async () => {
      if (!user) {
        setProfileLoading(false);
        return;
      }
      setProfileLoading(true);
      setProfileError(null);
      
      const { data, error } = await supabase
        .schema('care_connector')
        .from('profiles')
        .select('*')
        .eq('id', user.id)
        .single();

      if (error) {
        setProfileError('Failed to fetch your admin profile. Please try again.');
      } else {
        setProfile(data as UserProfile);
      }
      setProfileLoading(false);
    };

    if (!loading) {
      fetchProfile();
    }
  }, [user, loading]);

  // Fetch basic stats for dashboard overview
  useEffect(() => {
    const fetchStats = async () => {
      if (!user || !profile?.is_admin) return;
      
      setStatsLoading(true);
      
      try {
        // Fetch basic counts from care_connector schema
        const [usersResult, providersResult, bookingsResult, careGroupsResult] = await Promise.all([
          supabase.schema('care_connector').from('profiles').select('id', { count: 'exact', head: true }),
          supabase.schema('care_connector').from('service_providers').select('id', { count: 'exact', head: true }),
          supabase.schema('care_connector').from('service_provider_bookings').select('id', { count: 'exact', head: true }),
          supabase.schema('care_connector').from('care_groups').select('id', { count: 'exact', head: true })
        ]);
        
        setStats({
          total_users: usersResult.count || 0,
          total_providers: providersResult.count || 0,
          total_bookings: bookingsResult.count || 0,
          total_care_groups: careGroupsResult.count || 0
        });
      } catch (err) {
        console.error('Error fetching admin stats:', err);
      } finally {
        setStatsLoading(false);
      }
    };
    
    if (!loading && !profileLoading && user && profile?.is_admin) {
      fetchStats();
    }
  }, [user, loading, profileLoading, profile]);

  if (loading || profileLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-lg" style={{ color: 'var(--text-secondary)' }}>Loading admin dashboard...</div>
      </div>
    );
  }

  if (profileError) {
    return (
      <div className="container mx-auto px-4 py-12">
        <div className="text-base text-red-600 leading-relaxed font-medium">{profileError}</div>
      </div>
    );
  }
  
  if (!user || !profile?.is_admin) {
    return (
      <div className="container mx-auto px-4 py-12 md:py-16">
        <div className="flex items-center justify-between mb-8">
          <div>
                    <h1 className="text-3xl font-bold mb-2 tracking-tight" style={{color: 'var(--text-primary)'}}>Admin Dashboard</h1>
        <p style={{color: 'var(--text-secondary)'}}>Administrative tools and platform management.</p>
          </div>
          <Link
            to="/dashboard"
            className="text-sm flex items-center transition-colors duration-200" style={{color: 'var(--text-secondary)'}} onMouseEnter={(e) => e.currentTarget.style.color = 'var(--text-primary)'} onMouseLeave={(e) => e.currentTarget.style.color = 'var(--text-secondary)'}
          >
            ← Back to Dashboard
          </Link>
        </div>

        <div className="flex flex-col items-center justify-center py-16">
          <div className="w-16 h-16 mb-6" style={{color: 'var(--text-muted)'}}>
            <svg fill="none" stroke="currentColor" viewBox="0 0 24 24" className="w-full h-full">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
            </svg>
          </div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Admin Access Unavailable</h2>
          <p className="text-gray-600 text-center mb-8 max-w-md">
            Please sign in with admin credentials to access administrative tools and platform management features.
          </p>
          <Link
            to="/dashboard"
            className="px-6 py-2 rounded-lg font-medium transition-colors duration-200" style={{backgroundColor: 'var(--bg-accent)', color: 'var(--text-primary)'}} onMouseEnter={(e) => e.currentTarget.style.backgroundColor = 'var(--bg-hover)'} onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'var(--bg-accent)'}
          >
            Back to Dashboard
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-12 md:py-16">
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="text-3xl font-bold mb-2 tracking-tight" style={{ color: 'var(--text-primary)' }}>Admin Dashboard</h1>
          <p style={{ color: 'var(--text-secondary)' }}>Manage your platform and monitor key metrics.</p>
        </div>
        <Link
          to="/dashboard"
          className="text-sm flex items-center transition-colors duration-200"
          style={{ color: 'var(--text-secondary)' }}
          onMouseEnter={(e) => e.currentTarget.style.color = 'var(--text-primary)'}
          onMouseLeave={(e) => e.currentTarget.style.color = 'var(--text-secondary)'}
        >
          ← Back to Dashboard
        </Link>
      </div>

      {/* Quick Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
        <div className="rounded-lg p-6" style={{ backgroundColor: 'var(--bg-primary)', border: '1px solid var(--border-light)', boxShadow: 'var(--shadow-card)' }}>
          <h3 className="text-sm font-medium mb-2" style={{ color: 'var(--text-secondary)' }}>Total Users</h3>
          <p className="text-3xl font-bold" style={{ color: 'var(--text-primary)' }}>
            {statsLoading ? '...' : (stats?.total_users || 0)}
          </p>
        </div>
        <div className="rounded-lg p-6" style={{ backgroundColor: 'var(--bg-primary)', border: '1px solid var(--border-light)', boxShadow: 'var(--shadow-card)' }}>
          <h3 className="text-sm font-medium mb-2" style={{ color: 'var(--text-secondary)' }}>Service Providers</h3>
          <p className="text-3xl font-bold" style={{ color: 'var(--text-primary)' }}>
            {statsLoading ? '...' : (stats?.total_providers || 0)}
          </p>
        </div>
        <div className="rounded-lg p-6" style={{ backgroundColor: 'var(--bg-primary)', border: '1px solid var(--border-light)', boxShadow: 'var(--shadow-card)' }}>
          <h3 className="text-sm font-medium mb-2" style={{ color: 'var(--text-secondary)' }}>Total Bookings</h3>
          <p className="text-3xl font-bold" style={{ color: 'var(--text-primary)' }}>
            {statsLoading ? '...' : (stats?.total_bookings || 0)}
          </p>
        </div>
        <div className="rounded-lg p-6" style={{ backgroundColor: 'var(--bg-primary)', border: '1px solid var(--border-light)', boxShadow: 'var(--shadow-card)' }}>
          <h3 className="text-sm font-medium mb-2" style={{ color: 'var(--text-secondary)' }}>Care Groups</h3>
          <p className="text-3xl font-bold" style={{ color: 'var(--text-primary)' }}>
            {statsLoading ? '...' : (stats?.total_care_groups || 0)}
          </p>
        </div>
      </div>

      {/* Admin Tools */}
      <div className="mb-12">
        <h2 className="text-xl font-semibold mb-6 tracking-tight" style={{ color: 'var(--text-primary)' }}>
          Admin Tools
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <Link
            to="/admin-analytics"
            className="rounded-lg p-6 transition-all duration-200"
            style={{
              backgroundColor: 'var(--bg-primary)',
              border: '1px solid var(--border-light)',
              boxShadow: 'var(--shadow-card)'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.boxShadow = 'var(--shadow-hover)'
              e.currentTarget.style.transform = 'translateY(-2px)'
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.boxShadow = 'var(--shadow-card)'
              e.currentTarget.style.transform = 'translateY(0)'
            }}
          >
            <div className="flex items-center mb-4">
              <div className="w-10 h-10 rounded-lg flex items-center justify-center mr-4" style={{ backgroundColor: 'var(--bg-accent)' }}>
                <svg className="w-5 h-5" style={{ color: 'var(--primary)' }} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold" style={{ color: 'var(--text-primary)' }}>Analytics</h3>
            </div>
            <p className="text-sm" style={{ color: 'var(--text-secondary)' }}>
              View detailed analytics, charts, and performance metrics for the platform.
            </p>
          </Link>

          <Link
            to="/admin-content-moderation"
            className="bg-white rounded-lg border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200"
          >
            <div className="flex items-center mb-4">
              <div className="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center mr-4">
                <svg className="w-5 h-5 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-gray-900">Content Moderation</h3>
            </div>
            <p className="text-gray-600 text-sm">
              Review and moderate user content, reports, and manage platform safety.
            </p>
          </Link>

          <Link
            to="/admin-settings"
            className="bg-white rounded-lg border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200"
          >
            <div className="flex items-center mb-4">
              <div className="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center mr-4">
                <svg className="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-gray-900">Settings</h3>
            </div>
            <p className="text-gray-600 text-sm">
              Configure platform settings, manage system preferences and features.
            </p>
          </Link>
        </div>
      </div>

      {/* Recent Activity */}
      <div className="mb-8">
        <h2 className="text-xl font-semibold mb-6 tracking-tight text-gray-900">
          Quick Actions
        </h2>
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Link
              to="/caregivers"
              className="flex items-center p-3 text-sm text-gray-700 hover:bg-gray-50 rounded-lg transition-colors duration-200"
            >
              <svg className="w-4 h-4 mr-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
              </svg>
              View All Users
            </Link>
            <Link
              to="/care-groups"
              className="flex items-center p-3 text-sm text-gray-700 hover:bg-gray-50 rounded-lg transition-colors duration-200"
            >
              <svg className="w-4 h-4 mr-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
              </svg>
              Manage Care Groups
            </Link>
            <Link
              to="/companions"
              className="flex items-center p-3 text-sm text-gray-700 hover:bg-gray-50 rounded-lg transition-colors duration-200"
            >
              <svg className="w-4 h-4 mr-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
              </svg>
              View Companions
            </Link>
            <Link
              to="/professionals"
              className="flex items-center p-3 text-sm text-gray-700 hover:bg-gray-50 rounded-lg transition-colors duration-200"
            >
              <svg className="w-4 h-4 mr-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0V4a2 2 0 00-2-2H8a2 2 0 00-2 2v2m8 0H8m8 0l-.5 8a2 2 0 01-2 1.8H10.5a2 2 0 01-2-1.8L8 6" />
              </svg>
              View Professionals
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminDashboardPage;
