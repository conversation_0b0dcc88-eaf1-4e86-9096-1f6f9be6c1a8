import { useState, useEffect, useMemo } from 'react'
import { Link } from 'react-router-dom'
import { dataService } from '../lib/dataService'
import { 
  Shield, 
  Clock, 
  Users, 
  Calendar, 
  MessageSquare, 
  CheckSquare,
  Smartphone,
  Lock,
  Star,
  Bell,
  FileText,
  Activity
} from 'lucide-react'

export default function Features() {
  console.log('Features component loading...')
  // Dynamic features loaded from database - NO HARDCODED DATA
  const [coreFeatures, setCoreFeatures] = useState([])
  const [additionalFeatures, setAdditionalFeatures] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  // Load features from database
  useEffect(() => {
    const fetchFeatures = async () => {
      try {
        setLoading(true)
        // Query features from database instead of hardcoded data
        const [coreData, additionalData] = await Promise.all([
          dataService.getCoreFeatures(),
          dataService.getAdditionalFeatures()
        ])
        
        // Set data from database - NO HARDCODED FALLBACK DATA (Holy Rule #1)
        setCoreFeatures(coreData || [])
        
        // Set data from database - NO HARDCODED FALLBACK DATA (Holy Rule #1)
        setAdditionalFeatures(additionalData || [])
      } catch (err) {
        console.error('Error fetching features:', err)
        setError('Failed to load features. Please refresh the page.')
        setCoreFeatures([])
        setAdditionalFeatures([])
      } finally {
        setLoading(false)
      }
    }
    
    fetchFeatures()
  }, [])

  // Icon mapping for dynamic icon rendering (consistent with HowItWorks) - Memoized for performance
  const iconMap = useMemo(() => ({
    'Shield': Shield,
    'Clock': Clock,
    'Users': Users,
    'Calendar': Calendar,
    'MessageSquare': MessageSquare,
    'CheckSquare': CheckSquare,
    'Smartphone': Smartphone,
    'Lock': Lock,
    'Star': Star,
    'Bell': Bell,
    'FileText': FileText,
    'Activity': Activity
  }), [])

  return (
    <div className="min-h-screen py-8 sm:py-12" style={{ backgroundColor: 'var(--bg-primary)' }}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Enhanced Apple Mac Desktop Header */}
        <div className="text-center mb-12 sm:mb-16">
          <div className="mb-6 sm:mb-8">
            <h1 className="text-3xl sm:text-4xl lg:text-5xl font-semibold tracking-tight macos-title"
                style={{
                  color: 'var(--text-primary)',
                  letterSpacing: '-0.02em',
                  fontWeight: '600'
                }}>
              Comprehensive Care Features
            </h1>
          </div>
          <p className="text-lg sm:text-xl lg:text-2xl macos-body max-w-4xl mx-auto leading-relaxed"
             style={{
               color: 'var(--text-secondary)',
               fontWeight: '400',
               lineHeight: '1.6'
             }}>
            Everything you need to coordinate, manage, and deliver exceptional care for your loved ones.
          </p>
        </div>

        {/* Error State */}
        {error && (
          <div className="mb-8 bg-red-50 border border-red-200 rounded-xl p-6 text-center">
            <p className="text-red-600 font-medium">{error}</p>
            <button
              onClick={() => window.location.reload()}
              className="mt-3 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
            >
              Refresh Page
            </button>
          </div>
        )}

        {/* Loading State */}
        {loading && (
          <div className="text-center mb-16">
            <div className="inline-block animate-spin rounded-full h-8 w-8 border-b-2" style={{borderColor: 'var(--primary)'}}></div>
            <p className="mt-2 text-gray-600">Loading features...</p>
          </div>
        )}

        {/* Enhanced Core Features */}
        {!loading && !error && (
        <>
        <div className="mb-16 sm:mb-20">
          <h2 className="text-2xl sm:text-3xl lg:text-4xl font-semibold text-center mb-8 sm:mb-12 macos-title"
              style={{
                color: 'var(--text-primary)',
                letterSpacing: '-0.01em',
                fontWeight: '600'
              }}>
            Core Platform Features
          </h2>

          {coreFeatures.length === 0 ? (
            <div className="text-center py-12">
              <p className="text-gray-500 text-lg">No core features available at this time.</p>
            </div>
          ) : (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 sm:gap-8">
            {coreFeatures.map((feature, index) => {
              const Icon = iconMap[feature.icon_name] || Users // Dynamic icon mapping with fallback
              return (
                <div key={index}
                     className="rounded-2xl sm:rounded-3xl p-6 sm:p-8 transition-all duration-300 hover:scale-105 group"
                     style={{
                       backgroundColor: 'var(--bg-secondary)',
                       boxShadow: 'var(--shadow-large)',
                       backdropFilter: 'blur(10px)',
                       border: '1px solid rgba(255,255,255,0.1)'
                     }}
                     role="article"
                     aria-label={`Core feature: ${feature.title}`}>
                  <div className="flex items-start">
                    <div className="w-12 h-12 sm:w-14 sm:h-14 rounded-xl sm:rounded-2xl flex items-center justify-center flex-shrink-0 transition-all duration-300 group-hover:scale-110"
                         style={{
                           backgroundColor: 'var(--primary)',
                           boxShadow: 'var(--shadow-medium)'
                         }}>
                      <Icon className="w-6 h-6 sm:w-7 sm:h-7" style={{ color: 'var(--bg-primary)' }} />
                    </div>
                    <div className="ml-4 sm:ml-6">
                      <h3 className="text-lg sm:text-xl lg:text-2xl font-semibold mb-2 sm:mb-3 macos-title"
                          style={{
                            color: 'var(--text-primary)',
                            fontWeight: '600'
                          }}>
                        {feature.title}
                      </h3>
                      <p className="mb-4 sm:mb-6 text-sm sm:text-base lg:text-lg macos-body leading-relaxed"
                         style={{
                           color: 'var(--text-secondary)',
                           lineHeight: '1.6'
                         }}>
                        {feature.description}
                      </p>
                      <ul className="space-y-2 sm:space-y-3">
                        {feature.features.map((item, idx) => (
                          <li key={idx} className="flex items-center text-sm sm:text-base macos-body"
                              style={{ color: 'var(--text-secondary)' }}>
                            <CheckSquare className="w-4 h-4 sm:w-5 sm:h-5 mr-2 sm:mr-3 flex-shrink-0"
                                        style={{ color: 'var(--primary)' }} />
                            {item}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>
              )
            })}
          </div>
          )}
        </div>

        {/* Enhanced Additional Features Grid */}
        <div className="mb-12 sm:mb-16">
          <h2 className="text-2xl sm:text-3xl lg:text-4xl font-semibold text-center mb-8 sm:mb-12 macos-title"
              style={{
                color: 'var(--text-primary)',
                letterSpacing: '-0.01em',
                fontWeight: '600'
              }}>
            Additional Platform Benefits
          </h2>

          {additionalFeatures.length === 0 ? (
            <div className="text-center py-12">
              <p className="text-gray-500 text-lg">No additional features available at this time.</p>
            </div>
          ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
            {additionalFeatures.map((feature, index) => {
              const Icon = iconMap[feature.icon_name] || Bell // Dynamic icon mapping with fallback
              return (
                <div key={index}
                     className="rounded-2xl sm:rounded-3xl p-6 sm:p-8 text-center transition-all duration-300 hover:scale-105 group"
                     style={{
                       backgroundColor: 'var(--bg-secondary)',
                       boxShadow: 'var(--shadow-large)',
                       backdropFilter: 'blur(10px)',
                       border: '1px solid rgba(255,255,255,0.1)'
                     }}
                     role="article"
                     aria-label={`Additional feature: ${feature.title}`}>
                  <div className="w-12 h-12 sm:w-14 sm:h-14 rounded-xl sm:rounded-2xl flex items-center justify-center mx-auto mb-4 sm:mb-6 transition-all duration-300 group-hover:scale-110"
                       style={{
                         backgroundColor: 'var(--primary)',
                         boxShadow: 'var(--shadow-medium)'
                       }}>
                    <Icon className="w-6 h-6 sm:w-7 sm:h-7" style={{ color: 'var(--bg-primary)' }} />
                  </div>
                  <h3 className="text-lg sm:text-xl font-semibold mb-2 sm:mb-3 macos-title"
                      style={{
                        color: 'var(--text-primary)',
                        fontWeight: '600'
                      }}>
                    {feature.title}
                  </h3>
                  <p className="text-sm sm:text-base macos-body leading-relaxed"
                     style={{
                       color: 'var(--text-secondary)',
                       lineHeight: '1.6'
                     }}>
                    {feature.description}
                  </p>
                </div>
              )
            })}
          </div>
          )}
        </div>

        {/* Enhanced Security Section */}
        <div className="rounded-3xl sm:rounded-[2rem] p-8 sm:p-12 mb-12 sm:mb-16"
             style={{
               backgroundColor: 'var(--bg-secondary)',
               boxShadow: 'var(--shadow-large)',
               backdropFilter: 'blur(10px)',
               border: '1px solid rgba(255,255,255,0.1)'
             }}>
          <div className="text-center">
            <div className="w-16 h-16 sm:w-20 sm:h-20 rounded-2xl sm:rounded-3xl flex items-center justify-center mx-auto mb-6 sm:mb-8"
                 style={{
                   backgroundColor: 'var(--primary)',
                   boxShadow: 'var(--shadow-large)'
                 }}>
              <Lock className="w-8 h-8 sm:w-10 sm:h-10" style={{ color: 'var(--bg-primary)' }} />
            </div>
            <h2 className="text-2xl sm:text-3xl lg:text-4xl font-semibold mb-4 sm:mb-6 macos-title"
                style={{
                  color: 'var(--text-primary)',
                  letterSpacing: '-0.01em',
                  fontWeight: '600'
                }}>
              Enterprise-Grade Security
            </h2>
            <p className="text-lg text-gray-600 mb-8 max-w-3xl mx-auto">
              Your care data is protected with the same security standards used by banks and healthcare institutions.
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center">
                <h3 className="font-semibold text-gray-900 mb-2">HIPAA Compliant</h3>
                <p className="text-sm text-gray-600">Full compliance with healthcare privacy regulations</p>
              </div>
              <div className="text-center">
                <h3 className="font-semibold text-gray-900 mb-2">End-to-End Encryption</h3>
                <p className="text-sm text-gray-600">All data encrypted in transit and at rest</p>
              </div>
              <div className="text-center">
                <h3 className="font-semibold text-gray-900 mb-2">SOC 2 Certified</h3>
                <p className="text-sm text-gray-600">Audited security controls and processes</p>
              </div>
            </div>
          </div>
        </div>
        </>
        )}

        {/* CTA Section */}
        <div className="text-center">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            Experience the Difference
          </h2>
          <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
            Join thousands of families who have transformed their care coordination with Care Connector.
          </p>
          <div className="space-x-4">
            <Link
              to="/get-started"
              className="inline-flex items-center px-8 py-3 border border-transparent text-base font-medium rounded-md transition-all duration-300 hover:scale-105"
              style={{
                backgroundColor: 'var(--primary)',
                color: 'var(--bg-primary)',
                boxShadow: 'var(--shadow-medium)'
              }}
            >
              Start Free Trial
            </Link>
            <Link
              to="/how-it-works"
              className="inline-flex items-center px-8 py-3 border text-base font-medium rounded-md transition-all duration-300 hover:scale-105"
              style={{
                backgroundColor: 'var(--bg-primary)',
                color: 'var(--text-primary)',
                borderColor: 'var(--text-secondary)',
                boxShadow: 'var(--shadow-small)'
              }}
            >
              Learn More
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}
