import React, { useState, useCallback } from 'react'
import { Link, useNavigate } from 'react-router-dom'
import { Eye, EyeOff, Loader2 } from 'lucide-react'
import { supabase } from '../lib/supabase'

export default function SignIn() {
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [showPassword, setShowPassword] = useState(false)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [validationErrors, setValidationErrors] = useState<{[key: string]: string}>({})
  const navigate = useNavigate()

  const validateForm = useCallback(() => {
    const errors: {[key: string]: string} = {}

    if (!email.trim()) {
      errors.email = 'Email is required'
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
      errors.email = 'Please enter a valid email address'
    }

    if (!password.trim()) {
      errors.password = 'Password is required'
    } else if (password.length < 8) {
      errors.password = 'Password must be at least 8 characters'
    }

    setValidationErrors(errors)
    return Object.keys(errors).length === 0
  }, [email, password])

  const handleSignIn = useCallback(async (e: React.FormEvent) => {
    e.preventDefault()
    console.log('SignIn form submitted with:', { email, password: '***' })

    if (!validateForm()) {
      return
    }

    setLoading(true)
    setError(null)
    setValidationErrors({})

    try {
      console.log('🔐 Attempting Supabase auth with email:', email)
      console.log('🔐 Supabase client initialized:', !!supabase)

      const { data, error } = await supabase.auth.signInWithPassword({
        email: email.trim(),
        password: password.trim(),
      })

      console.log('🔐 Supabase auth response:', {
        user: data?.user?.email,
        session: data?.session ? 'Session exists' : 'No session',
        error: error?.message,
        errorCode: error?.status
      })

      if (error) {
        console.error('🔐 Authentication failed:', error)
        throw error
      }

      if (!data?.user) {
        console.error('🔐 No user data returned')
        throw new Error('Authentication failed - no user data returned')
      }

      if (!data?.session) {
        console.error('🔐 No session data returned')
        throw new Error('Authentication failed - no session created')
      }

      console.log('🔐 Auth successful! User:', data.user.email)
      console.log('🔐 Session created, navigating to dashboard...')

      // Small delay to ensure session is fully established
      setTimeout(() => {
        navigate('/dashboard')
      }, 100)

    } catch (error) {
      console.error('🔐 Auth error details:', error)
      const errorMessage = error instanceof Error ? error.message : 'An error occurred during sign in'
      console.error('🔐 Setting error message:', errorMessage)
      setError(errorMessage)
    } finally {
      setLoading(false)
    }
  }, [email, password, navigate, validateForm])

  return (
    <main className="min-h-screen flex flex-col justify-center py-6 sm:py-8 px-4 sm:px-6 lg:px-8" style={{ backgroundColor: 'var(--bg-secondary)' }}>
      <div className="sm:mx-auto sm:w-full sm:max-w-md lg:max-w-lg">
        {/* Apple Mac Desktop Style Header */}
        <div className="flex justify-center mb-8 sm:mb-10">
          <div
            className="rounded-2xl sm:rounded-3xl px-8 sm:px-10 py-4 sm:py-5 text-2xl sm:text-3xl font-semibold tracking-wide macos-title"
            style={{
              backgroundColor: 'var(--primary)',
              color: 'var(--bg-primary)',
              boxShadow: 'var(--shadow-large)',
              backdropFilter: 'blur(10px)',
              border: '1px solid rgba(255,255,255,0.1)',
              letterSpacing: '-0.01em',
              fontWeight: '600'
            }}
          >
            2CareConnector
          </div>
        </div>

        <div className="text-center mb-8 sm:mb-10">
          <h2 className="text-2xl sm:text-3xl lg:text-4xl font-semibold mb-3 sm:mb-4 macos-title" style={{
            color: 'var(--text-primary)',
            fontWeight: '600',
            letterSpacing: '-0.01em'
          }}>
            Welcome Back
          </h2>
          <p className="text-base sm:text-lg macos-body" style={{
            color: 'var(--text-secondary)',
            fontWeight: '400',
            letterSpacing: '-0.005em'
          }}>
            Sign in to access your care network
          </p>
          <p className="mt-3 sm:mt-4 text-sm sm:text-base macos-body" style={{ color: 'var(--text-secondary)' }}>
            Don't have an account?{' '}
            <Link
              to="/auth"
              className="font-medium transition-all duration-200 hover:underline"
              style={{
                color: 'var(--primary)',
                textDecoration: 'none'
              }}
              onMouseEnter={(e) => e.currentTarget.style.opacity = '0.8'}
              onMouseLeave={(e) => e.currentTarget.style.opacity = '1'}
            >
              Create one here
            </Link>
          </p>
        </div>
      </div>

      <div className="sm:mx-auto sm:w-full sm:max-w-md lg:max-w-lg">
        <div
          className="py-8 sm:py-10 lg:py-12 px-6 sm:px-8 lg:px-10 rounded-2xl sm:rounded-3xl border"
          style={{
            backgroundColor: 'var(--bg-primary)',
            borderColor: 'var(--border-light)',
            boxShadow: 'var(--shadow-large)',
            backdropFilter: 'blur(10px)',
            border: '1px solid var(--border-light)'
          }}
        >
          <form className="space-y-6" onSubmit={handleSignIn} noValidate>
            {error && (
              <div
                className="rounded-xl p-4 border"
                style={{
                  backgroundColor: 'var(--bg-error)',
                  borderColor: 'var(--border-error)'
                }}
              >
                <p className="text-sm" style={{ color: 'var(--text-error)' }}>{error}</p>
              </div>
            )}
            
            <div>
              <label htmlFor="email" className="block text-base sm:text-lg font-medium mb-3 macos-body" style={{
                color: 'var(--text-primary)',
                fontWeight: '500',
                letterSpacing: '-0.005em'
              }}>
                Email Address
              </label>
              <div>
                <input
                  id="email"
                  name="email"
                  type="email"
                  autoComplete="email"
                  required
                  value={email}
                  onChange={(e) => {
                    setEmail(e.target.value)
                    if (validationErrors.email) {
                      setValidationErrors(prev => ({...prev, email: ''}))
                    }
                  }}
                  className="appearance-none block w-full px-4 sm:px-5 py-3 sm:py-4 border rounded-xl sm:rounded-2xl focus:outline-none transition-all duration-200 text-base sm:text-lg macos-body"
                  style={{
                    backgroundColor: 'var(--bg-secondary)',
                    borderColor: 'var(--border-light)',
                    color: 'var(--text-primary)',
                    fontWeight: '400',
                    letterSpacing: '-0.005em'
                  }}
                  onFocus={(e) => {
                    e.currentTarget.style.borderColor = 'var(--primary)'
                    e.currentTarget.style.boxShadow = 'var(--focus-shadow)'
                  }}
                  onBlur={(e) => {
                    e.currentTarget.style.borderColor = 'var(--border-light)'
                    e.currentTarget.style.boxShadow = 'none'
                  }}
                  placeholder="Enter your email"
                  aria-describedby={validationErrors.email ? "email-error" : undefined}
                  aria-invalid={!!validationErrors.email}
                />
              </div>
              {validationErrors.email && (
                <p id="email-error" className="mt-1 text-sm" style={{ color: 'var(--text-error)' }} role="alert">
                  {validationErrors.email}
                </p>
              )}
            </div>

            <div>
              <label htmlFor="password" className="block text-base sm:text-lg font-medium mb-3 macos-body" style={{
                color: 'var(--text-primary)',
                fontWeight: '500',
                letterSpacing: '-0.005em'
              }}>
                Password
              </label>
              <div className="relative">
                <input
                  id="password"
                  name="password"
                  type={showPassword ? 'text' : 'password'}
                  autoComplete="current-password"
                  required
                  value={password}
                  onChange={(e) => {
                    setPassword(e.target.value)
                    if (validationErrors.password) {
                      setValidationErrors(prev => ({...prev, password: ''}))
                    }
                  }}
                  className="appearance-none block w-full px-4 sm:px-5 py-3 sm:py-4 pr-12 border rounded-xl sm:rounded-2xl focus:outline-none transition-all duration-300 text-base sm:text-lg macos-body"
                  style={{
                    backgroundColor: 'var(--bg-secondary)',
                    borderColor: 'var(--border-light)',
                    color: 'var(--text-primary)',
                    fontWeight: '400',
                    letterSpacing: '-0.005em'
                  }}
                  onFocus={(e) => {
                    e.currentTarget.style.borderColor = 'var(--primary)'
                    e.currentTarget.style.boxShadow = 'var(--focus-shadow)'
                  }}
                  onBlur={(e) => {
                    e.currentTarget.style.borderColor = 'var(--border-light)'
                    e.currentTarget.style.boxShadow = 'none'
                  }}
                  placeholder="Enter your password"
                  aria-describedby={validationErrors.password ? "password-error" : undefined}
                  aria-invalid={!!validationErrors.password}
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-4 top-1/2 transform -translate-y-1/2 transition-all duration-200 hover:bg-opacity-10"
                  style={{ 
                    color: 'var(--text-muted)',
                    borderRadius: '4px',
                    padding: '4px'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.color = 'var(--primary)'
                    e.currentTarget.style.backgroundColor = 'var(--bg-accent)'
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.color = 'var(--text-muted)'
                    e.currentTarget.style.backgroundColor = 'transparent'
                  }}
                  aria-label={showPassword ? 'Hide password' : 'Show password'}
                >
                  {showPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                </button>
              </div>
              {validationErrors.password && (
                <p id="password-error" className="mt-1 text-sm" style={{ color: 'var(--text-error)' }} role="alert">
                  {validationErrors.password}
                </p>
              )}
            </div>

            <div className="flex items-center justify-between">
              <div className="text-sm">
                <Link
                  to="/forgot-password"
                  className="font-medium transition-all duration-200"
                  style={{ 
                    color: 'var(--primary)',
                    textDecoration: 'none'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.color = 'var(--primary)'
                    e.currentTarget.style.textDecoration = 'underline'
                    e.currentTarget.style.opacity = '0.8'
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.color = 'var(--primary)'
                    e.currentTarget.style.textDecoration = 'none'
                    e.currentTarget.style.opacity = '1'
                  }}
                  onFocus={(e) => {
                    e.currentTarget.style.boxShadow = 'var(--focus-shadow)'
                  }}
                  onBlur={(e) => {
                    e.currentTarget.style.boxShadow = 'none'
                  }}
                >
                  Forgot your password?
                </Link>
              </div>
            </div>

            <div>
              <button
                type="submit"
                disabled={loading}
                className="w-full flex justify-center py-4 px-6 rounded-xl text-base font-medium transition-all duration-200 disabled:opacity-50"
                style={{
                  backgroundColor: 'var(--primary)',
                  color: 'var(--bg-primary)',
                  boxShadow: 'var(--shadow-card)'
                }}
                onFocus={(e) => {
                  e.currentTarget.style.boxShadow = 'var(--focus-shadow)'
                }}
                onBlur={(e) => {
                  e.currentTarget.style.boxShadow = 'var(--shadow-card)'
                }}
              >
                {loading ? (
                  <div className="flex items-center justify-center gap-2">
                    <Loader2 className="w-4 h-4 animate-spin" />
                    Signing in...
                  </div>
                ) : (
                  'Sign in'
                )}
              </button>
            </div>
          </form>

          <div className="mt-8">
            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <div className="w-full border-t" style={{ borderColor: 'var(--border-light)' }} />
              </div>
              <div className="relative flex justify-center text-sm">
                <span
                  className="px-4 py-1 rounded-md"
                  style={{
                    backgroundColor: 'var(--bg-primary)',
                    color: 'var(--text-secondary)',
                    fontSize: '14px',
                    fontWeight: '500'
                  }}
                >
                  Or continue with
                </span>
              </div>
            </div>

            <div className="mt-8">
              <button
                type="button"
                disabled={loading}
                className="button-secondary w-full flex justify-center py-4 px-6 border rounded-xl text-base font-semibold transition-all duration-200 disabled:opacity-50"
                style={{
                  borderColor: 'var(--border-medium)',
                  backgroundColor: 'var(--bg-primary)',
                  color: 'var(--text-primary)'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.backgroundColor = 'var(--bg-secondary)'
                  e.currentTarget.style.borderColor = 'var(--primary)'
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = 'var(--bg-primary)'
                  e.currentTarget.style.borderColor = 'var(--border-medium)'
                }}
                onFocus={(e) => {
                  e.currentTarget.style.borderColor = 'var(--primary)'
                  e.currentTarget.style.boxShadow = 'var(--focus-shadow)'
                }}
                onBlur={(e) => {
                  e.currentTarget.style.borderColor = 'var(--border-medium)'
                  e.currentTarget.style.boxShadow = 'none'
                }}
                onClick={async () => {
                  try {
                    setLoading(true)
                    setError(null)
                    const { error } = await supabase.auth.signInWithOAuth({
                      provider: 'google',
                      options: {
                        redirectTo: `${window.location.origin}/dashboard`
                      }
                    })
                    if (error) throw error
                  } catch (error) {
                    console.error('Google OAuth error:', error)
                    setError('Failed to sign in with Google. Please try again.')
                    setLoading(false)
                  }
                }}
              >
                {loading ? (
                  <div className="flex items-center justify-center gap-2">
                    <Loader2 className="w-4 h-4 animate-spin" />
                    Connecting...
                  </div>
                ) : (
                  'Continue with Google'
                )}
              </button>
            </div>
          </div>
        </div>
      </div>
    </main>
  )
}
