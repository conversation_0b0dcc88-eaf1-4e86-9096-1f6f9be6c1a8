import React, { useState, useEffect } from 'react';
import { Navigate } from 'react-router-dom';
import { supabase } from '../lib/supabase';
import { BarC<PERSON>, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';

interface UserProfile {
  id: string;
  is_admin?: boolean;
}

const AdminAnalytics: React.FC = () => {
  const [user, setUser] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState<any>(null);
  const [dataLoading, setDataLoading] = useState(true);
  const [error, setError] = useState<any>(null);
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [profileLoading, setProfileLoading] = useState(true);
  const [profileError, setProfileError] = useState<string | null>(null);

  // Check authentication and fetch user
  useEffect(() => {
    const checkAuth = async () => {
      const { data: { session } } = await supabase.auth.getSession();
      setUser(session?.user || null);
      setLoading(false);
    };
    checkAuth();
  }, []);

  // Fetch user profile to check admin status
  useEffect(() => {
    const fetchProfile = async () => {
      if (!user) {
        setProfileLoading(false);
        return;
      }
      setProfileLoading(true);
      setProfileError(null);
      
      const { data, error } = await supabase
        .schema('care_connector')
        .from('profiles')
        .select('*')
        .eq('id', user.id)
        .single();

      if (error) {
        setProfileError('Failed to fetch your admin profile. Please try again.');
      } else {
        setProfile(data as UserProfile);
      }
      setProfileLoading(false);
    };

    if (!loading) {
      fetchProfile();
    }
  }, [user, loading]);

  // Fetch admin stats
  useEffect(() => {
    const fetchAdminStats = async () => {
      if (!user) return;
      
      setDataLoading(true);
      setError(null);
      
      try {
        // Fetch total counts from care_connector schema
        const [usersResult, providersResult, bookingsResult, careGroupsResult] = await Promise.all([
          supabase.schema('care_connector').from('profiles').select('id', { count: 'exact', head: true }),
          supabase.schema('care_connector').from('service_providers').select('id', { count: 'exact', head: true }),
          supabase.schema('care_connector').from('service_provider_bookings').select('id', { count: 'exact', head: true }),
          supabase.schema('care_connector').from('care_groups').select('id', { count: 'exact', head: true })
        ]);
        
        // Fetch monthly data for charts
        const userSignupsData = await supabase
          .schema('care_connector')
          .from('profiles')
          .select('created_at')
          .gte('created_at', new Date(Date.now() - 365 * 24 * 60 * 60 * 1000).toISOString());
          
        const careGroupsData = await supabase
          .schema('care_connector')
          .from('care_groups')
          .select('created_at')
          .gte('created_at', new Date(Date.now() - 365 * 24 * 60 * 60 * 1000).toISOString());
          
        const bookingsData = await supabase
          .schema('care_connector')
          .from('service_provider_bookings')
          .select('created_at')
          .gte('created_at', new Date(Date.now() - 365 * 24 * 60 * 60 * 1000).toISOString());
        
        // Process monthly data
        const processMonthlyData = (data: any[], label: string) => {
          const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
          const monthlyCount: { [key: string]: number } = {};
          
          data?.forEach(item => {
            const date = new Date(item.created_at);
            const monthName = months[date.getMonth()];
            monthlyCount[monthName] = (monthlyCount[monthName] || 0) + 1;
          });
          
          return months.map(month => ({ 
            month, 
            [label]: monthlyCount[month] || 0 
          }));
        };
        
        setStats({
          total_users: usersResult.count || 0,
          total_providers: providersResult.count || 0,
          total_bookings: bookingsResult.count || 0,
          total_care_groups: careGroupsResult.count || 0,
          userSignups: processMonthlyData(userSignupsData.data || [], 'signups'),
          careGroupCreations: processMonthlyData(careGroupsData.data || [], 'creations'),
          bookingCreations: processMonthlyData(bookingsData.data || [], 'bookings')
        });
      } catch (err: any) {
        console.error('Error fetching admin stats:', err);
        setError(err);
      } finally {
        setDataLoading(false);
      }
    };
    
    if (!loading && user && profile?.is_admin) {
      fetchAdminStats();
    }
  }, [user, loading, profile]);

  if (loading || profileLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-lg" style={{color: 'var(--text-secondary)'}}>Loading analytics...</div>
      </div>
    );
  }

  if (profileError) {
    return (
      <div className="container mx-auto px-4 py-12">
        <div className="text-base text-red-600 leading-relaxed font-medium">{profileError}</div>
      </div>
    );
  }
  
  if (!user || !profile?.is_admin) {
    return <Navigate to="/" />;
  }
  
  if (error) {
    return (
      <div className="container mx-auto px-4 py-12">
        <div className="text-base leading-relaxed font-medium" style={{ color: 'var(--text-error)' }}>
          Error loading analytics data: {error.message}
        </div>
      </div>
    );
  }

  if (dataLoading) {
    return (
      <div className="container mx-auto px-4 py-12">
        <h1 className="text-3xl font-bold mb-4 tracking-tight">Admin Analytics</h1>
        <div style={{ color: 'var(--text-secondary)' }}>Loading analytics data...</div>
      </div>
    );
  }

  const combinedData = stats?.userSignups?.map((signup: any, index: number) => ({
    ...signup,
    creations: stats.careGroupCreations[index]?.creations ?? 0,
    bookings: stats.bookingCreations[index]?.bookings ?? 0,
  })) || [];

  return (
    <div className="container mx-auto px-4 py-12 md:py-16">
      <h1 className="text-3xl font-bold mb-4 tracking-tight" style={{ color: 'var(--text-primary)' }}>Admin Analytics</h1>
      <p className="mb-8" style={{ color: 'var(--text-secondary)' }}>Overview of key platform metrics and performance data.</p>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
        <div className="rounded-lg p-6" style={{ backgroundColor: 'var(--bg-primary)', border: '1px solid var(--border-light)', boxShadow: 'var(--shadow-card)' }}>
          <h3 className="text-sm font-medium mb-2" style={{ color: 'var(--text-secondary)' }}>Total Users</h3>
          <p className="text-3xl font-bold" style={{ color: 'var(--text-primary)' }}>{stats?.total_users || 0}</p>
        </div>
        <div className="rounded-lg p-6" style={{ backgroundColor: 'var(--bg-primary)', border: '1px solid var(--border-light)', boxShadow: 'var(--shadow-card)' }}>
          <h3 className="text-sm font-medium mb-2" style={{ color: 'var(--text-secondary)' }}>Total Providers</h3>
          <p className="text-3xl font-bold" style={{ color: 'var(--text-primary)' }}>{stats?.total_providers || 0}</p>
        </div>
        <div className="rounded-lg p-6" style={{ backgroundColor: 'var(--bg-primary)', border: '1px solid var(--border-light)', boxShadow: 'var(--shadow-card)' }}>
          <h3 className="text-sm font-medium mb-2" style={{ color: 'var(--text-secondary)' }}>Total Bookings</h3>
          <p className="text-3xl font-bold" style={{ color: 'var(--text-primary)' }}>{stats?.total_bookings || 0}</p>
        </div>
        <div className="rounded-lg p-6" style={{ backgroundColor: 'var(--bg-primary)', border: '1px solid var(--border-light)', boxShadow: 'var(--shadow-card)' }}>
          <h3 className="text-sm font-medium mb-2" style={{ color: 'var(--text-secondary)' }}>Care Groups</h3>
          <p className="text-3xl font-bold" style={{ color: 'var(--text-primary)' }}>{stats?.total_care_groups || 0}</p>
        </div>
      </div>

      {/* Chart */}
      <div className="mb-8">
        <h2 className="text-xl font-semibold mb-4 tracking-tight" style={{ color: 'var(--text-primary)' }}>
          Monthly Activity Overview
        </h2>
        <div className="rounded-lg p-6" style={{ backgroundColor: 'var(--bg-primary)', border: '1px solid var(--border-light)', boxShadow: 'var(--shadow-card)' }}>
          <ResponsiveContainer width="100%" height={400}>
            <BarChart data={combinedData}>
              <CartesianGrid strokeDasharray="3 3" stroke="var(--border-light)" />
              <XAxis dataKey="month" stroke="var(--text-secondary)" />
              <YAxis stroke="var(--text-secondary)" />
              <Tooltip 
                contentStyle={{
                  backgroundColor: 'white',
                  border: '1px solid #e5e7eb',
                  borderRadius: '8px'
                }}
              />
              <Legend />
              <Bar dataKey="signups" fill="var(--primary)" name="User Signups" />
              <Bar dataKey="creations" fill="var(--success)" name="Care Group Creations" />
              <Bar dataKey="bookings" fill="var(--warning)" name="Bookings" />
            </BarChart>
          </ResponsiveContainer>
        </div>
      </div>
    </div>
  );
};

export default AdminAnalytics;
