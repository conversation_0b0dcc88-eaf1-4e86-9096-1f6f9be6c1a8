import { useState, useEffect, useRef, useCallback } from 'react'
import { Link, useNavigate } from 'react-router-dom'
import { Search, Users, Sparkles, Calendar, MessageSquare, CheckSquare, Shield, UserCheck, ChevronDown, CheckCircle, Award } from 'lucide-react'
import { dataService } from '../lib/dataService'

export default function Home() {
  const [showFindCareDropdown, setShowFindCareDropdown] = useState(false)
  const [caregivers, setCaregivers] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [homepageStats, setHomepageStats] = useState({
    verifiedProfessionals: 0,
    averageRating: null,
    successfulBookings: 0,
    supportStatus: null
  })
  const [heroContent, setHeroContent] = useState(null)
  const [securityBadges, setSecurityBadges] = useState([])
  const [searchContent, setSearchContent] = useState({
    title: 'Find Your Perfect Care Match',
    subtitle: 'Search by location, specialty, and availability',
    locationHelp: 'Enter your city, state, or ZIP code to find nearby care providers'
  })
  const [featuresContent, setFeaturesContent] = useState([])
  
  // Essential Care Tools section state
  const [essentialToolsContent, setEssentialToolsContent] = useState([])
  
  // Featured Care Providers section state
  const [featuredProvidersContent, setFeaturedProvidersContent] = useState({
    title: 'Featured Care Providers',
    description: 'Meet our top-rated, verified healthcare professionals ready to provide exceptional care.'
  })
  
  // Provider Network section state
  const [providerNetworkContent, setProviderNetworkContent] = useState({
    title: 'Building Our Provider Network',
    description: 'We\'re carefully vetting and onboarding qualified care providers to ensure the highest quality of service.',
    buttonText: 'Join Our Provider Network'
  })
  const [takeControlContent, setTakeControlContent] = useState({
    title: 'Take Control of Your Care Journey',
    description: 'Access powerful tools to manage appointments, track health records, and coordinate with your care team all in one secure platform.'
  })
  const [footerContent, setFooterContent] = useState({
    description: 'Connecting families with trusted care providers through innovative technology and compassionate service.',
    servicesTitle: 'Services',
    learnMoreTitle: 'Learn More'
  })
  const [searchFilters, setSearchFilters] = useState({
    careType: 'all',
    location: '',
    availability: 'any',
    insurance: 'any',
    language: 'any',
    certification: 'any'
  })
  const [searchFilterOptions, setSearchFilterOptions] = useState({
    careTypes: [],
    availability: [],
    insurance: [],
    languages: [],
    certifications: []
  })
  const [navigationOptions, setNavigationOptions] = useState<any[]>([])
  const dropdownRef = useRef<HTMLDivElement>(null)
  const featuredProvidersRef = useRef<HTMLDivElement>(null)
  const [featuredProvidersVisible, setFeaturedProvidersVisible] = useState(false)
  const [formValidation, setFormValidation] = useState({
    location: { isValid: true, message: '' },
    careType: { isValid: true, message: '' },
    availability: { isValid: true, message: '' }
  })
  const navigate = useNavigate()

  useEffect(() => {
    const fetchData = async () => {
      try {
        setError(null) // Clear any previous errors
        console.log('Fetching homepage data...')

        // Load real data from database with proper error handling - NO HARDCODED DATA
        const [caregiversData, statsData, searchOptionsData, heroData, securityData, featuresData, toolsData] = await Promise.allSettled([
          dataService.getCaregivers(),
          dataService.getHomepageStats(),
          dataService.getSearchFilterOptions(),
          dataService.getHeroContent(),
          dataService.getSecurityBadges(),
          dataService.getFeaturesContent(),
          dataService.getEssentialToolsContent()
        ]).then(results => results.map(result => result.status === 'fulfilled' ? result.value : null))

        console.log('Caregivers data received:', caregiversData)
        console.log('Stats data received:', statsData)
        console.log('Search options received:', searchOptionsData)
        console.log('Hero content received:', heroData)
        console.log('Security badges received:', securityData)
        console.log('Features content received:', featuresData)
        console.log('Essential tools received:', toolsData)

        setCaregivers((caregiversData || []).slice(0, 3)) // Show only first 3 for homepage
        setHomepageStats(statsData || {
          verifiedProfessionals: 0,
          averageRating: null,
          successfulBookings: 0,
          supportStatus: null
        })
        setSearchFilterOptions(searchOptionsData || {
          careTypes: [],
          availability: [],
          insurance: [],
          languages: [],
          certifications: []
        })
        setNavigationOptions([])

        // Load real content from database with null safety - NO HARDCODED FALLBACKS
        setSecurityBadges(securityData || [])
        setFeaturesContent(featuresData || [])
        setEssentialToolsContent(toolsData || [])
        setHeroContent(heroData || null)

        // Load real content from database - NO HARDCODED FALLBACKS
        const [searchData, featuredData, networkData, controlData, footerData] = await Promise.allSettled([
          dataService.getSearchContent(),
          dataService.getFeaturedProvidersContent(),
          dataService.getProviderNetworkContent(),
          dataService.getTakeControlContent(),
          dataService.getFooterContent()
        ]).then(results => results.map(result => result.status === 'fulfilled' ? result.value : null))

        setSearchContent(searchData || { 
          title: 'Find Your Perfect Care Match', 
          subtitle: 'Search by location, specialty, and availability',
          locationHelp: 'Enter your city, state, or ZIP code to find nearby care providers'
        })
        setFeaturedProvidersContent(featuredData || { 
          title: 'Featured Care Providers', 
          description: 'Meet our top-rated, verified healthcare professionals ready to provide exceptional care.'
        })
        setProviderNetworkContent(networkData || { 
          title: 'Building Our Provider Network', 
          description: 'We\'re carefully vetting and onboarding qualified care providers to ensure the highest quality of service.',
          buttonText: 'Join Our Provider Network'
        })
        setTakeControlContent(controlData || { 
          title: 'Take Control of Your Care Journey', 
          description: 'Access powerful tools to manage appointments, track health records, and coordinate with your care team all in one secure platform.'
        })
        setFooterContent(footerData || { 
          description: 'Connecting families with trusted care providers through innovative technology and compassionate service.',
          servicesTitle: 'Services',
          learnMoreTitle: 'Learn More'
        })
      } catch (error) {
        console.error('Error fetching homepage data:', error)
        setError('Failed to load care providers. Please try again later.')
        setCaregivers([])
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [])

  // Handle outside click and keyboard navigation for dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setShowFindCareDropdown(false)
      }
    }

    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        setShowFindCareDropdown(false)
      }
    }

    if (showFindCareDropdown) {
      document.addEventListener('mousedown', handleClickOutside)
      document.addEventListener('keydown', handleKeyDown)
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
      document.removeEventListener('keydown', handleKeyDown)
    }
  }, [showFindCareDropdown])

  // Intersection Observer for featured providers lazy loading
  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setFeaturedProvidersVisible(true)
            observer.unobserve(entry.target)
          }
        })
      },
      {
        rootMargin: '100px 0px',
        threshold: 0.1
      }
    )

    if (featuredProvidersRef.current) {
      observer.observe(featuredProvidersRef.current)
    }

    return () => {
      if (featuredProvidersRef.current) {
        observer.unobserve(featuredProvidersRef.current)
      }
    }
  }, [])

  const handleGetStarted = useCallback(() => {
    navigate('/get-started')
  }, [navigate])

  const handleBrowseProviders = useCallback(() => {
    navigate('/caregivers')
  }, [navigate])

  const handleSearchSubmit = useCallback(() => {
    // Reset validation state
    const newValidation = {
      location: { isValid: true, message: '' },
      careType: { isValid: true, message: '' },
      availability: { isValid: true, message: '' }
    }

    let hasErrors = false

    // Validate required location field
    if (!searchFilters.location.trim()) {
      newValidation.location = { isValid: false, message: 'Location is required' }
      hasErrors = true
    } else if (searchFilters.location.trim().length < 2) {
      newValidation.location = { isValid: false, message: 'Location must be at least 2 characters' }
      hasErrors = true
    }

    // Validate care type selection
    if (!searchFilters.careType || searchFilters.careType === '') {
      newValidation.careType = { isValid: false, message: 'Please select a care type' }
      hasErrors = true
    }

    // Update validation state
    setFormValidation(newValidation)

    if (hasErrors) {
      setError('Please correct the errors below and try again.')
      // Focus on first invalid field
      const firstInvalidField = document.querySelector('[aria-invalid="true"]') as HTMLElement
      if (firstInvalidField) {
        firstInvalidField.focus()
      }
      return
    }

    // Clear any existing errors
    setError('')

    // Construct search parameters
    const searchParams = new URLSearchParams()
    searchParams.set('location', searchFilters.location)
    if (searchFilters.careType && searchFilters.careType !== 'all') {
      searchParams.set('careType', searchFilters.careType)
    }
    if (searchFilters.availability && searchFilters.availability !== 'all') {
      searchParams.set('availability', searchFilters.availability)
    }
    if (searchFilters.insurance && searchFilters.insurance !== 'all') {
      searchParams.set('insurance', searchFilters.insurance)
    }
    if (searchFilters.language && searchFilters.language !== 'all') {
      searchParams.set('language', searchFilters.language)
    }
    if (searchFilters.certification && searchFilters.certification !== 'all') {
      searchParams.set('certification', searchFilters.certification)
    }

    // Navigate to caregivers page with search parameters
    const searchQuery = searchParams.toString()
    navigate(`/caregivers${searchQuery ? `?${searchQuery}` : ''}`)
  }, [navigate, searchFilters])

  const handleFilterChange = (filterType: string, value: string) => {
    setSearchFilters(prev => ({
      ...prev,
      [filterType]: value
    }))
  }

  const handleAIHelper = () => {
    // Navigate to AI assistance page or open AI chat
    navigate('/ai-assistant')
  }

  return (
    <main style={{ minHeight: '100vh', backgroundColor: 'var(--bg-primary)' }}>
      {/* Skip Link for Accessibility */}
      <a href="#main-content" className="skip-link">
        Skip to main content
      </a>

      {/* Hero Section - Apple Mac Desktop Style with Enhanced Visual Hierarchy */}
      <section id="main-content" className="py-24 md:py-32 lg:py-40 px-4 sm:px-6 md:px-8 text-center" style={{
        backgroundColor: 'var(--bg-primary)'
      }} role="banner" aria-label="Hero section">

        <div className="max-w-5xl mx-auto">
          {/* Main Title - Apple Mac Desktop Typography with Perfect Spacing */}
          <div className="space-y-8">
            {heroContent?.title ? (
              <h1 className="text-5xl sm:text-6xl md:text-7xl lg:text-8xl font-light mb-6 tracking-tight macos-title" style={{
                color: 'var(--text-primary)',
                lineHeight: '0.95',
                fontWeight: '300',
                letterSpacing: '-0.025em',
                textRendering: 'optimizeLegibility'
              }}>
                {heroContent.title}
              </h1>
            ) : (
              <div className="h-20 sm:h-24 md:h-28 lg:h-32 rounded-lg animate-pulse mb-6" style={{backgroundColor: 'var(--bg-secondary)'}}></div>
            )}
            {heroContent?.subtitle ? (
              <p className="text-xl sm:text-2xl md:text-3xl max-w-4xl mx-auto leading-relaxed mb-16 macos-subtitle" style={{
                color: 'var(--text-secondary)',
                lineHeight: '1.3',
                fontWeight: '400',
                letterSpacing: '-0.01em',
                textRendering: 'optimizeLegibility'
              }}>
                {heroContent.subtitle}
              </p>
            ) : (
              <div className="h-8 sm:h-10 md:h-12 rounded-lg animate-pulse mb-16 max-w-4xl mx-auto" style={{backgroundColor: 'var(--bg-secondary)'}}></div>
            )}

            {/* Call-to-Action Buttons - Apple Mac Desktop Style */}
            <div className="flex flex-col sm:flex-row gap-6 justify-center items-center mt-16">
              <button
                onClick={() => navigate('/caregivers')}
                className="button-primary px-10 py-4 rounded-xl text-lg font-medium flex items-center gap-3"
                style={{
                  minWidth: '220px'
                } as React.CSSProperties}
                aria-label="Find care providers"
              >
                <Search className="w-5 h-5" />
                Find Care Providers
              </button>
              <button
                onClick={() => navigate('/how-it-works')}
                className="button-secondary px-10 py-4 rounded-xl text-lg font-medium flex items-center gap-3"
                style={{
                  minWidth: '220px'
                } as React.CSSProperties}
                aria-label="Learn how Care Connector works"
              >
                <Sparkles className="w-5 h-5" />
                How It Works
              </button>
            </div>
          </div>
        </div>


      </section>

      {/* Trust Indicators Section - Clean Apple Mac Desktop Style */}
      <section className="py-16 px-4 sm:px-8" style={{
        backgroundColor: 'var(--bg-secondary)',
        borderBottom: '1px solid var(--border-light)'
      }}>


        <div className="max-w-5xl mx-auto">
          {/* Section Header - Clean Apple Mac Desktop Typography */}
          <div className="text-center mb-16">
            <h2 className="text-3xl sm:text-4xl lg:text-5xl font-light mb-6" style={{
              color: 'var(--text-primary)',
              lineHeight: '1.15',
              fontWeight: '200',
              letterSpacing: '-0.01em'
            }}>
              {homepageStats.verifiedProfessionals > 0 ? (
                <>Trusted by <span className="font-semibold" style={{
                  fontWeight: '700',
                  color: 'var(--primary)'
                }}>{homepageStats.verifiedProfessionals.toLocaleString()}</span> Care Providers</>
              ) : (
                <>Building Our <span className="font-semibold" style={{
                  fontWeight: '700',
                  color: 'var(--primary)'
                }}>Premium</span> Provider Network</>
              )}
            </h2>
            <p className="text-lg sm:text-xl max-w-3xl mx-auto" style={{
              color: 'var(--text-secondary)',
              lineHeight: '1.4',
              fontWeight: '300'
            }}>
              {homepageStats.verifiedProfessionals > 0 ? (
                'Join a growing community of healthcare professionals and families who trust our platform for comprehensive care coordination'
              ) : (
                'We are carefully curating and vetting qualified healthcare professionals to ensure the highest quality of care for our community'
              )}
            </p>
          </div>

          {/* Clean Stats Grid - Apple Mac Desktop Style */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
            {homepageStats.verifiedProfessionals !== undefined ? (
              <div className="text-center p-6 rounded-xl transition-all duration-200 hover:shadow-md" style={{
                backgroundColor: 'var(--bg-primary)',
                border: '1px solid var(--border-light)',
                boxShadow: 'var(--shadow-card)'
              }}>
                <div className="text-3xl font-light mb-2" style={{color: 'var(--text-primary)'}}>
                  {homepageStats.verifiedProfessionals.toLocaleString()}
                </div>
                <div className="text-sm font-medium" style={{color: 'var(--text-secondary)'}}>Verified Professionals</div>
              </div>
            ) : (
              <div className="text-center p-6 rounded-xl animate-pulse" style={{
                backgroundColor: 'var(--bg-secondary)',
                border: '1px solid var(--border-light)'
              }}>
                <div className="h-8 rounded mb-2" style={{backgroundColor: 'var(--bg-muted)'}}></div>
                <div className="h-4 rounded w-3/4 mx-auto" style={{backgroundColor: 'var(--bg-muted)'}}></div>
              </div>
            )}
            {homepageStats.averageRating ? (
              <div className="text-center p-6 rounded-xl transition-all duration-200 hover:shadow-md" style={{
                backgroundColor: 'var(--bg-primary)',
                border: '1px solid var(--border-light)',
                boxShadow: 'var(--shadow-card)'
              }}>
                <div className="text-3xl font-light mb-2 flex items-center justify-center gap-1" style={{color: 'var(--text-primary)'}}>
                  {homepageStats.averageRating}
                  <Award className="w-6 h-6" style={{color: 'var(--accent-warning)'}} />
                </div>
                <div className="text-sm font-medium" style={{color: 'var(--text-secondary)'}}>Average Rating</div>
              </div>
            ) : (
              <div className="text-center p-6 rounded-xl animate-pulse" style={{
                backgroundColor: 'var(--bg-secondary)',
                border: '1px solid var(--border-light)'
              }}>
                <div className="h-8 rounded mb-2" style={{backgroundColor: 'var(--bg-muted)'}}></div>
                <div className="h-4 rounded w-3/4 mx-auto" style={{backgroundColor: 'var(--bg-muted)'}}></div>
              </div>
            )}
            {homepageStats.successfulBookings !== undefined ? (
              <div className="text-center p-6 rounded-xl transition-all duration-200 hover:shadow-md" style={{
                backgroundColor: 'var(--bg-primary)',
                border: '1px solid var(--border-light)',
                boxShadow: 'var(--shadow-card)'
              }}>
                <div className="text-3xl font-light mb-2" style={{color: 'var(--text-primary)'}}>
                  {homepageStats.successfulBookings.toLocaleString()}
                </div>
                <div className="text-sm font-medium" style={{color: 'var(--text-secondary)'}}>Successful Bookings</div>
              </div>
            ) : (
              <div className="text-center p-6 rounded-xl animate-pulse" style={{
                backgroundColor: 'var(--bg-secondary)',
                border: '1px solid var(--border-light)'
              }}>
                <div className="h-8 rounded mb-2" style={{backgroundColor: 'var(--bg-muted)'}}></div>
                <div className="h-4 rounded w-3/4 mx-auto" style={{backgroundColor: 'var(--bg-muted)'}}></div>
              </div>
            )}
            {homepageStats.supportStatus ? (
              <div className="text-center p-6 rounded-xl transition-all duration-200 hover:shadow-md" style={{
                backgroundColor: 'var(--bg-primary)',
                border: '1px solid var(--border-light)',
                boxShadow: 'var(--shadow-card)'
              }}>
                <div className="text-3xl font-light mb-2" style={{color: 'var(--text-primary)'}}>
                  {homepageStats.supportStatus}
                </div>
                <div className="text-sm font-medium" style={{color: 'var(--text-secondary)'}}>Support Available</div>
              </div>
            ) : (
              <div className="text-center p-6 rounded-xl animate-pulse" style={{
                backgroundColor: 'var(--bg-secondary)',
                border: '1px solid var(--border-light)'
              }}>
                <div className="h-8 rounded mb-2" style={{backgroundColor: 'var(--bg-muted)'}}></div>
                <div className="h-4 rounded w-3/4 mx-auto" style={{backgroundColor: 'var(--bg-muted)'}}></div>
              </div>
            )}
          </div>

          {/* Clean Security Badges - Apple Mac Desktop Style */}
          <div className="flex flex-wrap items-center justify-center gap-3 sm:gap-4">
            {securityBadges.length > 0 ? (
              securityBadges
                .filter((badge) => !badge.text.toLowerCase().includes('hipaa'))
                .map((badge) => {
                // Dynamic icon mapping based on icon string from database
                const iconMap = {
                  'CheckCircle': CheckCircle,
                  'Shield': Shield,
                  'Award': Award
                }
                const IconComponent = iconMap[badge.icon] || CheckCircle

                return (
                  <div
                    key={badge.id}
                    className="flex items-center gap-2 px-4 py-3 rounded-xl transition-all duration-200 hover:shadow-md cursor-default"
                    style={{
                      backgroundColor: 'var(--bg-primary)',
                      border: '1px solid var(--border-light)',
                      boxShadow: 'var(--shadow-card)'
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.transform = 'translateY(-2px)'
                      e.currentTarget.style.boxShadow = 'var(--shadow-hover)'
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.transform = 'translateY(0)'
                      e.currentTarget.style.boxShadow = 'var(--shadow-card)'
                    }}
                    role="img"
                    aria-label={`Security badge: ${badge.text}`}
                  >
                    <IconComponent className="w-5 h-5" style={{color: 'var(--primary)'}} aria-hidden="true" />
                    <span className="text-sm font-medium whitespace-nowrap" style={{color: 'var(--text-primary)'}}>{badge.text}</span>
                  </div>
                )
              })
            ) : (
              // Loading state for security badges
              Array.from({ length: 3 }).map((_, index) => (
                <div
                  key={`loading-badge-${index}`}
                  className="flex items-center gap-2 px-4 py-3 rounded-xl animate-pulse"
                  style={{
                    backgroundColor: 'var(--bg-secondary)',
                    border: '1px solid var(--border-light)',
                    minWidth: '120px',
                    height: '44px'
                  }}
                >
                  <div className="w-5 h-5 rounded-full" style={{backgroundColor: 'var(--bg-tertiary)'}}></div>
                  <div className="h-4 rounded flex-1" style={{backgroundColor: 'var(--bg-tertiary)'}}></div>
                </div>
              ))
            )}
          </div>
        </div>
      </section>

      {/* Search Section - Apple Mac Desktop Style with Enhanced Card Design */}
      <section className="py-24 px-4 sm:px-8" style={{
        backgroundColor: 'var(--bg-secondary)',
        borderTop: '1px solid var(--border-light)'
      }}>
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-20">
            {searchContent.title ? (
              <h2 className="text-3xl sm:text-4xl lg:text-5xl font-light mb-8 macos-title" style={{
                color: 'var(--text-primary)',
                lineHeight: '1.1',
                letterSpacing: '-0.02em',
                fontWeight: '300'
              }}>
                {searchContent.title}
              </h2>
            ) : (
              <div className="h-12 sm:h-14 lg:h-16 rounded-lg animate-pulse mb-8 max-w-2xl mx-auto" style={{backgroundColor: 'var(--bg-secondary)'}}></div>
            )}
            {searchContent.subtitle ? (
              <p className="text-lg sm:text-xl max-w-3xl mx-auto macos-subtitle" style={{
                color: 'var(--text-secondary)',
                lineHeight: '1.5',
                fontWeight: '400',
                letterSpacing: '-0.005em'
              }}>
                {searchContent.subtitle}
              </p>
            ) : (
              <div className="h-6 sm:h-7 rounded-lg animate-pulse max-w-3xl mx-auto" style={{backgroundColor: 'var(--bg-secondary)'}}></div>
            )}
          </div>

          {/* Display search form error if exists */}
          {error && (
            <div className="mb-6 p-4 rounded-lg" style={{backgroundColor: 'var(--bg-error)', border: '1px solid var(--border-error)'}} role="alert" aria-live="polite">
              <div className="flex items-center gap-2">
                <svg className="w-5 h-5 flex-shrink-0" style={{color: 'var(--text-error)'}} fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
                <span className="text-sm font-medium" style={{color: 'var(--text-error)'}}>{error}</span>
              </div>
            </div>
          )}

          <form onSubmit={(e) => { e.preventDefault(); handleSearchSubmit(); }} className="rounded-3xl p-12 sm:p-14" style={{
            backgroundColor: 'var(--bg-primary)',
            border: '1px solid var(--border-light)',
            boxShadow: 'var(--shadow-large)',
            backdropFilter: 'blur(10px)'
          }} role="search" aria-label="Find care providers" noValidate>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8 sm:gap-10 mb-10">
              <div>
                <label htmlFor="care-type-select" className="block text-sm font-semibold mb-3" style={{color: 'var(--text-primary)', letterSpacing: '0.025em'}}>
                  Care Type
                </label>
                <select
                  value={searchFilters.careType}
                  onChange={(e) => {
                    handleFilterChange('careType', e.target.value)
                    // Clear validation error when user makes selection
                    if (!formValidation.careType.isValid && e.target.value) {
                      setFormValidation(prev => ({
                        ...prev,
                        careType: { isValid: true, message: '' }
                      }))
                    }
                  }}
                  className="w-full px-5 py-4 rounded-xl border focus:outline-none focus:ring-2 focus:ring-opacity-20 transition-all duration-300"
                  style={{
                    backgroundColor: 'var(--bg-primary)',
                    borderColor: !formValidation.careType.isValid ? 'var(--border-error)' : 'var(--border-light)',
                    color: 'var(--text-primary)'
                  } as React.CSSProperties}
                  onFocus={(e) => {
                    e.currentTarget.style.borderColor = 'var(--primary)'
                    e.currentTarget.style.boxShadow = 'var(--focus-shadow)'
                  }}
                  onBlur={(e) => {
                    e.currentTarget.style.borderColor = !formValidation.careType.isValid ? 'var(--border-error)' : 'var(--border-light)'
                    e.currentTarget.style.boxShadow = 'none'
                  }}
                  onMouseEnter={(e) => {
                    if (formValidation.careType.isValid) {
                      e.currentTarget.style.borderColor = 'var(--border-medium)'
                    }
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.borderColor = !formValidation.careType.isValid ? 'var(--border-error)' : 'var(--border-light)'
                  }}
                  id="care-type-select"
                  aria-invalid={!formValidation.careType.isValid}
                  aria-describedby={!formValidation.careType.isValid ? 'care-type-error' : undefined}
                >
                  <option value="all">All Types</option>
                  {searchFilterOptions.careTypes.map(careType => (
                    <option key={typeof careType === 'string' ? careType : careType.value} value={typeof careType === 'string' ? careType : careType.value}>
                      {typeof careType === 'string' ? careType.charAt(0).toUpperCase() + careType.slice(1).replace('_', ' ') : careType.label}
                    </option>
                  ))}
                </select>
                {!formValidation.careType.isValid && (
                  <div id="care-type-error" className="text-xs mt-1 flex items-center gap-1" style={{color: 'var(--text-error)'}} role="alert" aria-live="polite">
                    <svg className="w-3 h-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z" />
                    </svg>
                    {formValidation.careType.message}
                  </div>
                )}
              </div>

              <div>
                <label htmlFor="location-input" className="block text-sm font-semibold mb-3" style={{color: 'var(--text-primary)', letterSpacing: '0.025em'}}>
                  Location <span style={{color: 'var(--text-error)', fontSize: '0.9em'}} aria-label="required">*</span>
                </label>
                <input
                  id="location-input"
                  type="text"
                  placeholder="San Francisco, CA or 94102"
                  value={searchFilters.location}
                  onChange={(e) => {
                    handleFilterChange('location', e.target.value)
                    // Clear validation error when user starts typing
                    if (!formValidation.location.isValid && e.target.value.trim()) {
                      setFormValidation(prev => ({
                        ...prev,
                        location: { isValid: true, message: '' }
                      }))
                    }
                  }}
                  required
                  aria-required="true"
                  aria-invalid={!formValidation.location.isValid}
                  aria-describedby={`location-help ${!formValidation.location.isValid ? 'location-error' : ''}`}
                  className="w-full px-5 py-4 rounded-xl border transition-all duration-300 placeholder-styled focus:outline-none focus:ring-2 focus:ring-opacity-20"
                  style={{
                    backgroundColor: 'var(--bg-primary)',
                    borderColor: 'var(--border-light)',
                    color: 'var(--text-primary)'
                  } as React.CSSProperties}
                  onFocus={(e) => {
                    e.currentTarget.style.borderColor = 'var(--primary)'
                    e.currentTarget.style.boxShadow = 'var(--focus-shadow)'
                  }}
                  onBlur={(e) => {
                    if (!error) {
                      e.currentTarget.style.borderColor = 'var(--border-light)'
                      e.currentTarget.style.boxShadow = 'none'
                    }
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.borderColor = 'var(--border-medium)'
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.borderColor = 'var(--border-light)'
                  }}
                />
                <div id="location-help" className="text-xs mt-1" style={{color: 'var(--text-muted)'}}>
                  {searchContent.locationHelp}
                </div>
                {!formValidation.location.isValid && (
                  <div id="location-error" className="text-xs mt-1 flex items-center gap-1" style={{color: 'var(--text-error)'}} role="alert" aria-live="polite">
                    <svg className="w-3 h-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z" />
                    </svg>
                    {formValidation.location.message}
                  </div>
                )}
              </div>

              <div>
                <label htmlFor="availability-select" className="block text-sm font-semibold mb-3" style={{color: 'var(--text-primary)', letterSpacing: '0.025em'}}>
                  Availability
                </label>
                <select
                  id="availability-select"
                  value={searchFilters.availability}
                  onChange={(e) => handleFilterChange('availability', e.target.value)}
                  className="w-full px-5 py-4 rounded-xl border focus:outline-none focus:ring-2 focus:ring-opacity-20 transition-all duration-300"
                  style={{
                    backgroundColor: 'var(--bg-primary)',
                    borderColor: 'var(--border-light)',
                    color: 'var(--text-primary)'
                  } as React.CSSProperties}
                  onFocus={(e) => {
                    e.currentTarget.style.borderColor = 'var(--primary)'
                    e.currentTarget.style.boxShadow = 'var(--focus-shadow)'
                  }}
                  onBlur={(e) => {
                    e.currentTarget.style.borderColor = 'var(--border-light)'
                    e.currentTarget.style.boxShadow = 'none'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.borderColor = 'var(--border-medium)'
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.borderColor = 'var(--border-light)'
                  }}
                >
                  <option value="any">Select your preferred timing</option>
                  {searchFilterOptions.availability.map(avail => (
                    <option key={typeof avail === 'string' ? avail : avail.value} value={typeof avail === 'string' ? avail : avail.value}>
                      {typeof avail === 'string' ? avail.charAt(0).toUpperCase() + avail.slice(1) : avail.label}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label htmlFor="insurance-select" className="block text-sm font-semibold mb-3" style={{color: 'var(--text-primary)', letterSpacing: '0.025em'}}>
                  Insurance Accepted
                </label>
                <select
                  id="insurance-select"
                  value={searchFilters.insurance}
                  onChange={(e) => handleFilterChange('insurance', e.target.value)}
                  className="w-full px-5 py-4 rounded-xl border focus:outline-none focus:ring-2 focus:ring-opacity-20 transition-all duration-300"
                  style={{
                    backgroundColor: 'var(--bg-primary)',
                    borderColor: 'var(--border-light)',
                    color: 'var(--text-primary)'
                  } as React.CSSProperties}
                  onFocus={(e) => {
                    e.currentTarget.style.borderColor = 'var(--primary)'
                    e.currentTarget.style.boxShadow = 'var(--focus-shadow)'
                  }}
                  onBlur={(e) => {
                    e.currentTarget.style.borderColor = 'var(--border-light)'
                    e.currentTarget.style.boxShadow = 'none'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.borderColor = 'var(--border-medium)'
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.borderColor = 'var(--border-light)'
                  }}
                >
                  <option value="any">Choose your insurance plan</option>
                  {searchFilterOptions.insurance.map(ins => (
                    <option key={typeof ins === 'string' ? ins : ins.value} value={typeof ins === 'string' ? ins : ins.value}>
                      {typeof ins === 'string' ? ins.charAt(0).toUpperCase() + ins.slice(1) : ins.label}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label htmlFor="language-select" className="block text-sm font-semibold mb-3" style={{color: 'var(--text-primary)', letterSpacing: '0.025em'}}>
                  Languages Spoken
                </label>
                <select
                  id="language-select"
                  value={searchFilters.language}
                  onChange={(e) => handleFilterChange('language', e.target.value)}
                  className="w-full px-5 py-4 rounded-xl border focus:outline-none focus:ring-2 focus:ring-opacity-20 transition-all duration-300"
                  style={{
                    backgroundColor: 'var(--bg-primary)',
                    borderColor: 'var(--border-light)',
                    color: 'var(--text-primary)'
                  } as React.CSSProperties}
                  onFocus={(e) => {
                    e.currentTarget.style.borderColor = 'var(--primary)'
                    e.currentTarget.style.boxShadow = 'var(--focus-shadow)'
                  }}
                  onBlur={(e) => {
                    e.currentTarget.style.borderColor = 'var(--border-light)'
                    e.currentTarget.style.boxShadow = 'none'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.borderColor = 'var(--border-medium)'
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.borderColor = 'var(--border-light)'
                  }}
                >
                  <option value="any">Preferred language</option>
                  {searchFilterOptions.languages.map(lang => (
                    <option key={typeof lang === 'string' ? lang : lang.value} value={typeof lang === 'string' ? lang : lang.value}>
                      {typeof lang === 'string' ? lang.charAt(0).toUpperCase() + lang.slice(1) : lang.label}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label htmlFor="certification-select" className="block text-sm font-semibold mb-3" style={{color: 'var(--text-primary)', letterSpacing: '0.025em'}}>
                  Certifications
                </label>
                <select
                  id="certification-select"
                  value={searchFilters.certification}
                  onChange={(e) => handleFilterChange('certification', e.target.value)}
                  className="w-full px-5 py-4 rounded-xl border focus:outline-none focus:ring-2 focus:ring-opacity-20 transition-all duration-300"
                  style={{
                    backgroundColor: 'var(--bg-primary)',
                    borderColor: 'var(--border-light)',
                    color: 'var(--text-primary)'
                  } as React.CSSProperties}
                  onFocus={(e) => {
                    e.currentTarget.style.borderColor = 'var(--primary)'
                    e.currentTarget.style.boxShadow = 'var(--focus-shadow)'
                  }}
                  onBlur={(e) => {
                    e.currentTarget.style.borderColor = 'var(--border-light)'
                    e.currentTarget.style.boxShadow = 'none'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.borderColor = 'var(--border-medium)'
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.borderColor = 'var(--border-light)'
                  }}
                >
                  <option value="any">Required certifications</option>
                  {searchFilterOptions.certifications.map(cert => (
                    <option key={typeof cert === 'string' ? cert : cert.value} value={typeof cert === 'string' ? cert : cert.value}>
                      {typeof cert === 'string' ? cert.toUpperCase() : cert.label}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            <div className="text-center">
              <button
                onClick={(e) => { e.preventDefault(); handleSearchSubmit(); }}
                disabled={loading}
                className="button-primary w-full px-8 py-4 rounded-xl text-lg font-medium focus:outline-none focus:ring-2 focus:ring-opacity-50 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
                style={{
                  fontSize: '18px',
                  fontWeight: '500',
                  letterSpacing: '-0.005em',
                  boxShadow: 'var(--shadow-large)'
                } as React.CSSProperties}
                onMouseEnter={(e) => {
                  if (!loading) {
                    e.currentTarget.style.transform = 'translateY(-2px) scale(1.01)'
                    e.currentTarget.style.boxShadow = 'var(--shadow-large)'
                  }
                }}
                onMouseLeave={(e) => {
                  if (!loading) {
                    e.currentTarget.style.transform = 'translateY(0) scale(1)'
                    e.currentTarget.style.boxShadow = 'var(--shadow-medium)'
                  }
                }}
                onFocus={(e) => {
                  e.currentTarget.style.boxShadow = 'var(--focus-shadow)'
                }}
                onBlur={(e) => {
                  e.currentTarget.style.boxShadow = 'var(--shadow-medium)'
                }}
                aria-label={loading ? "Searching for care providers..." : "Search for care providers"}
                aria-describedby="search-help"
                type="submit"
              >
                {loading ? (
                  <>
                    <div className="w-6 h-6 inline mr-3 border-2 border-white border-t-transparent rounded-full animate-spin" aria-hidden="true"></div>
                    Searching...
                  </>
                ) : (
                  <>
                    <Search className="w-6 h-6 inline mr-3" aria-hidden="true" />
                    Search Care Providers
                  </>
                )}
              </button>
              <div id="search-help" className="text-sm mt-4" style={{color: 'var(--text-muted)'}}>
                Find verified healthcare professionals in your area
              </div>
            </div>
          </form>
        </div>
      </section>

      {/* Features Section - Apple Mac Desktop Style with Enhanced Typography */}
      <section
        className="py-24 px-4 sm:px-8 text-center"
        style={{ backgroundColor: 'var(--bg-secondary)' }}
      >
        <div className="max-w-6xl mx-auto">
          <div className="mb-20">
            <h2 className="text-3xl sm:text-4xl lg:text-5xl font-medium mb-10 macos-title" style={{
              color: 'var(--text-primary)',
              lineHeight: '1.1',
              letterSpacing: '-0.025em',
              fontWeight: '500'
            }}>
              Your Complete Care Network
            </h2>
            <p className="text-lg sm:text-xl mb-12 max-w-3xl mx-auto macos-subtitle" style={{
              color: 'var(--text-secondary)',
              lineHeight: '1.5',
              fontWeight: '400',
              letterSpacing: '-0.01em'
            }}>
              Healthcare coordination for modern families with comprehensive tools and verified professionals.
            </p>
          </div>
        </div>

        <div className="max-w-6xl mx-auto grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
          {/* Find Verified Care - Enhanced Apple Mac Desktop Card */}
          <div
            className="rounded-3xl p-8 border transition-all duration-500 group"
            style={{
              backgroundColor: 'var(--bg-primary)',
              borderColor: 'var(--border-light)',
              color: 'var(--text-primary)',
              boxShadow: 'var(--shadow-card)',
              backdropFilter: 'blur(10px)'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.boxShadow = 'var(--shadow-large)'
              e.currentTarget.style.transform = 'translateY(-4px)'
              e.currentTarget.style.borderColor = 'var(--primary)'
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.boxShadow = 'var(--shadow-card)'
              e.currentTarget.style.transform = 'translateY(0)'
              e.currentTarget.style.borderColor = 'var(--border-light)'
            }}
          >
            <div className="text-center mb-6">
              <div
                className="w-16 h-16 rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-all duration-300"
                style={{
                  backgroundColor: 'var(--primary)',
                  boxShadow: 'var(--shadow-large)'
                }}
              >
                <Search className="w-8 h-8" style={{ color: 'var(--bg-primary)' }} />
              </div>
              <h3 className="text-xl font-semibold mb-4 macos-title" style={{
                color: 'var(--text-primary)',
                fontWeight: '600',
                letterSpacing: '-0.01em'
              }}>
                Find Verified Care
              </h3>
              <p className="mb-6 text-base leading-relaxed macos-body" style={{ color: 'var(--text-secondary)' }}>
                Connect with verified healthcare professionals who deliver exceptional, compassionate care.
              </p>
            </div>
            <button
              onClick={handleBrowseProviders}
              className="button-primary w-full px-6 py-4 rounded-2xl font-medium flex items-center justify-center gap-3 transition-all duration-300"
              style={{
                fontSize: '16px',
                fontWeight: '500',
                letterSpacing: '-0.005em',
                boxShadow: 'var(--shadow-large)'
              } as React.CSSProperties}
              onMouseEnter={(e) => {
                e.currentTarget.style.transform = 'translateY(-2px)'
                e.currentTarget.style.boxShadow = 'var(--shadow-large)'
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.transform = 'translateY(0)'
                e.currentTarget.style.boxShadow = 'var(--shadow-medium)'
              }}
            >
              Browse Providers
              <Search className="w-5 h-5" />
            </button>
          </div>

          {featuresContent.length > 0 ? (
            featuresContent.map((feature) => {
              // Dynamic icon mapping based on icon string from database
              const iconMap = {
                'Users': Users,
                'Sparkles': Sparkles
              }
              const IconComponent = iconMap[feature.icon] || Users

              // Dynamic action handlers
              const handleAction = () => {
                if (feature.action === 'create-group') {
                  navigate('/create-group')
                } else if (feature.action === 'ai-helper') {
                  handleAIHelper()
                }
              }

              return (
                <div
                  key={feature.id}
                  className="rounded-xl p-8 border transition-all duration-300"
                  style={{
                    backgroundColor: 'var(--bg-primary)',
                    borderColor: 'var(--border-light)',
                    color: 'var(--text-primary)',
                    boxShadow: 'var(--shadow-card)'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.boxShadow = 'var(--shadow-card-hover)'
                    e.currentTarget.style.transform = 'translateY(-4px)'
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.boxShadow = 'var(--shadow-card)'
                    e.currentTarget.style.transform = 'translateY(0)'
                  }}
                >
                  <div
                    className="w-16 h-16 rounded-2xl flex items-center justify-center mx-auto mb-6 transition-all duration-300"
                    style={{ backgroundColor: 'var(--primary)' }}
                  >
                    <IconComponent className="w-8 h-8" style={{ color: 'var(--bg-primary)' }} />
                  </div>
                  <h3 className="text-xl font-semibold mb-4" style={{
                    color: 'var(--text-primary)',
                    fontWeight: '600',
                    letterSpacing: '-0.01em'
                  }}>
                    {feature.title}
                  </h3>
                  <p className="mb-6 text-base leading-relaxed" style={{
                    color: 'var(--text-secondary)',
                    lineHeight: '1.6'
                  }}>
                    {feature.description}
                  </p>
                  <button
                    onClick={handleAction}
                    className="w-full px-6 py-3 rounded-lg font-medium flex items-center justify-center gap-3 transition-all duration-300"
                    style={{
                      backgroundColor: 'var(--primary)',
                      color: 'var(--bg-primary)',
                      fontWeight: '500',
                      letterSpacing: '-0.005em',
                      boxShadow: 'var(--shadow-medium)'
                    } as React.CSSProperties}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.opacity = '0.95'
                      e.currentTarget.style.transform = 'translateY(-2px)'
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.opacity = '1'
                      e.currentTarget.style.transform = 'translateY(0)'
                    }}
                  >
                    {feature.buttonText}
                    {feature.action === 'create-group' && <Users className="w-5 h-5" />}
                  </button>
                </div>
              )
            })
          ) : (
            // Loading state for features content
            Array.from({ length: 2 }).map((_, index) => (
              <div
                key={`loading-feature-${index}`}
                className="rounded-xl p-8 border animate-pulse"
                style={{
                  backgroundColor: 'var(--bg-primary)',
                  borderColor: 'var(--border-light)',
                  boxShadow: 'var(--shadow-card)'
                }}
              >
                <div
                  className="w-16 h-16 rounded-2xl mx-auto mb-6"
                  style={{ backgroundColor: 'var(--bg-secondary)' }}
                ></div>
                <div className="h-6 rounded mb-4 mx-auto max-w-32" style={{ backgroundColor: 'var(--bg-secondary)' }}></div>
                <div className="space-y-2 mb-6">
                  <div className="h-4 rounded" style={{ backgroundColor: 'var(--bg-secondary)' }}></div>
                  <div className="h-4 rounded w-3/4 mx-auto" style={{ backgroundColor: 'var(--bg-secondary)' }}></div>
                </div>
                <div className="h-12 rounded-lg" style={{ backgroundColor: 'var(--bg-secondary)' }}></div>
              </div>
            ))
          )}
        </div>
      </section>

      {/* Essential Care Tools */}
      <section className="py-20 px-8 text-center macos-section-elegant" style={{backgroundColor: 'var(--bg-secondary)'}}>
        <div className="max-w-4xl mx-auto mb-16">
          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-light mb-6" style={{
            color: 'var(--text-primary)',
            fontWeight: '200',
            letterSpacing: '-0.01em',
            lineHeight: '1.15'
          }}>
            Essential Care Tools
          </h2>
          <p className="text-lg sm:text-xl leading-relaxed" style={{
            color: 'var(--text-secondary)',
            fontWeight: '300',
            lineHeight: '1.4'
          }}>
            Stay connected and organized with integrated care management tools.
          </p>
        </div>

        <div className="max-w-6xl mx-auto grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8">
          {essentialToolsContent.length > 0 ? (
            essentialToolsContent.map((tool) => {
              // Dynamic icon mapping based on icon string from database
              const iconMap = {
                'Calendar': Calendar,
                'MessageSquare': MessageSquare,
                'CheckSquare': CheckSquare,
                'Search': Search,
                'Users': Users,
                'FileText': Search, // Using Search as FileText is not imported
                'Shield': Shield
              }
              const IconComponent = iconMap[tool.icon] || Calendar

              return (
                <Link
                  key={tool.id}
                  to={tool.link}
                  className="rounded-xl p-8 block macos-card-elegant border transition-all duration-300"
                  style={{
                    backgroundColor: 'var(--bg-primary)',
                    borderColor: 'var(--border-light)',
                    boxShadow: 'var(--shadow-card)'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.boxShadow = 'var(--shadow-card-hover)'
                    e.currentTarget.style.transform = 'translateY(-4px)'
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.boxShadow = 'var(--shadow-card)'
                    e.currentTarget.style.transform = 'translateY(0)'
                  }}
                >
                  <div
                    className="w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-6"
                    style={{backgroundColor: 'var(--bg-accent)'}}
                  >
                    <IconComponent className="w-8 h-8" style={{color: 'var(--primary)'}} />
                  </div>
                  <h3 className="text-xl font-semibold mb-4" style={{color: 'var(--text-primary)'}}>
                    {tool.title}
                  </h3>
                  <p className="text-sm" style={{color: 'var(--text-secondary)'}}>
                    {tool.description}
                  </p>
                </Link>
              )
            })
          ) : (
            // Loading state for essential tools content
            Array.from({ length: 6 }).map((_, index) => (
              <div
                key={`loading-tool-${index}`}
                className="rounded-xl p-8 border animate-pulse"
                style={{
                  backgroundColor: 'var(--bg-primary)',
                  borderColor: 'var(--border-light)',
                  boxShadow: 'var(--shadow-card)'
                }}
              >
                <div
                  className="w-16 h-16 rounded-full mx-auto mb-6"
                  style={{backgroundColor: 'var(--bg-secondary)'}}
                ></div>
                <div className="h-6 rounded mb-4 max-w-32 mx-auto" style={{backgroundColor: 'var(--bg-secondary)'}}></div>
                <div className="space-y-2">
                  <div className="h-4 rounded" style={{backgroundColor: 'var(--bg-secondary)'}}></div>
                  <div className="h-4 rounded w-3/4 mx-auto" style={{backgroundColor: 'var(--bg-secondary)'}}></div>
                </div>
              </div>
            ))
          )}
        </div>
      </section>

      {/* Provider Cards Section - Apple Mac Desktop Style with Enhanced Visual Design */}
      <section
        ref={featuredProvidersRef}
        className="py-24"
        style={{
          backgroundColor: 'var(--bg-primary)',
          borderTop: '1px solid var(--border-light)'
        }}
      >
        <div className="max-w-6xl mx-auto px-6">
          <div className="text-center mb-20">
            <h2 className="text-3xl sm:text-4xl lg:text-5xl font-light mb-8 macos-title" style={{
              color: 'var(--text-primary)',
              lineHeight: '1.1',
              letterSpacing: '-0.02em',
              fontWeight: '300'
            }}>
              {featuredProvidersContent.title}
            </h2>
            <p className="text-lg sm:text-xl max-w-3xl mx-auto macos-subtitle" style={{
              color: 'var(--text-secondary)',
              lineHeight: '1.4',
              fontWeight: '400',
              letterSpacing: '-0.005em'
            }}>
              {featuredProvidersContent.description}
            </p>
          </div>

          {loading ? (
            <div className="text-center py-16" role="status" aria-live="polite" aria-label="Loading care providers">
              <div className="max-w-sm mx-auto">
                <div className="flex justify-center mb-6">
                  <div className="relative">
                    <div className="w-12 h-12 rounded-full border-2 border-transparent animate-spin" style={{borderTopColor: 'var(--primary)', borderRightColor: 'var(--primary)'}} aria-hidden="true"></div>
    
                  </div>
                </div>
                <h3 className="text-lg font-medium mb-2" style={{color: 'var(--text-primary)'}}>
                  Connecting to Provider Network
                </h3>
                <p className="text-sm mb-3" style={{color: 'var(--text-secondary)'}}>
                  Searching verified healthcare professionals in your area...
                </p>
                <div className="flex items-center justify-center gap-2 text-xs animate-pulse-subtle" style={{color: 'var(--text-muted)'}}>
                  <span className="w-1 h-1 rounded-full" style={{backgroundColor: 'var(--primary)'}}></span>
                  <span>Verifying credentials</span>
                  <span className="w-1 h-1 rounded-full" style={{backgroundColor: 'var(--primary)'}}></span>
                  <span>Checking availability</span>
                  <span className="w-1 h-1 rounded-full" style={{backgroundColor: 'var(--primary)'}}></span>
                  <span>Matching preferences</span>
                </div>
                <div className="mt-4 text-xs" style={{color: 'var(--text-muted)'}}>
                  This may take a few moments...
                </div>
              </div>
            </div>
          ) : error ? (
            <div className="text-center py-16" role="alert" aria-live="assertive">
              <div className="max-w-md mx-auto">
                <div className="w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-6" style={{backgroundColor: 'var(--bg-error-light)'}}>
                  <svg className="w-8 h-8" style={{color: 'var(--text-error)'}} fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z" />
                  </svg>
                </div>
                <h3 className="text-xl font-semibold mb-3" style={{color: 'var(--text-primary)'}}>
                  Unable to Load Providers
                </h3>
                <p className="text-base mb-6" style={{color: 'var(--text-secondary)'}}>
                  {error}
                </p>
                <div className="flex flex-col sm:flex-row gap-3 justify-center">
                  <button
                    onClick={() => {
                      setError(null)
                      setLoading(true)
                      // Retry data fetching
                      const fetchData = async () => {
                        try {
                          const [caregiversData, statsData, searchOptionsData, navigationOptionsData] = await Promise.all([
                            dataService.getCaregivers(),
                            dataService.getHomepageStats(),
                            dataService.getSearchFilterOptions(),
                            dataService.getNavigationOptions()
                          ])
                          setCaregivers(caregiversData.slice(0, 3))
                          setHomepageStats(statsData)
                          setSearchFilterOptions(searchOptionsData)
                          setNavigationOptions(navigationOptionsData)
                        } catch (error) {
                          console.error('Retry failed:', error)
                          setError('Failed to load care providers. Please check your connection and try again.')
                        } finally {
                          setLoading(false)
                        }
                      }
                      fetchData()
                    }}
                    className="button-primary px-6 py-3 rounded-lg font-medium transition-all duration-200"
                    style={{
                      backgroundColor: 'var(--primary)',
                      color: 'var(--bg-primary)'
                    }}
                    aria-label="Retry loading care providers"
                  >
                    Try Again
                  </button>
                  <button
                    onClick={() => navigate('/caregivers')}
                    className="button-secondary px-6 py-3 rounded-lg font-medium transition-all duration-200"
                    style={{
                      backgroundColor: 'var(--bg-secondary)',
                      color: 'var(--text-primary)',
                      border: '1px solid var(--border-medium)'
                    }}
                    aria-label="Browse all care providers"
                  >
                    Browse All Providers
                  </button>
                </div>
              </div>
            </div>
          ) : caregivers.length > 0 ? (
            <>
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
                {caregivers.map((caregiver) => (
                  <div
                    key={caregiver.id}
                    className="rounded-3xl p-8 transition-all duration-500 border cursor-pointer group"
                    style={{
                      backgroundColor: 'var(--bg-secondary)',
                      borderColor: 'var(--border-light)',
                      boxShadow: 'var(--shadow-card)',
                      backdropFilter: 'blur(10px)'
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.backgroundColor = 'var(--bg-primary)'
                      e.currentTarget.style.boxShadow = 'var(--shadow-large)'
                      e.currentTarget.style.transform = 'translateY(-4px)'
                      e.currentTarget.style.borderColor = 'var(--primary)'
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.backgroundColor = 'var(--bg-secondary)'
                      e.currentTarget.style.boxShadow = 'var(--shadow-card)'
                      e.currentTarget.style.transform = 'translateY(0)'
                      e.currentTarget.style.borderColor = 'var(--border-light)'
                    }}
                  >
                    {/* Header with Avatar and Verification - Apple Mac Desktop Style */}
                    <div className="flex flex-col items-center text-center mb-6">
                      <div className="relative mb-4">
                        <div
                          className="w-20 h-20 rounded-full flex items-center justify-center"
                          style={{
                            backgroundColor: 'var(--bg-primary)',
                            border: '3px solid var(--border-light)',
                            boxShadow: 'var(--shadow-large)'
                          }}
                        >
                          {caregiver.avatar_url ? (
                            <img
                              src={caregiver.avatar_url}
                              alt={caregiver.full_name || 'Provider'}
                              className="w-20 h-20 rounded-full object-cover"
                              style={{border: '3px solid var(--border-light)'}}
                              loading="lazy"
                              decoding="async"
                              onError={(e) => {
                                e.currentTarget.style.display = 'none'
                                e.currentTarget.nextElementSibling?.classList.remove('hidden')
                              }}
                            />
                          ) : null}
                          <div className={`${caregiver.avatar_url ? 'hidden' : 'flex'} w-10 h-10 items-center justify-center`}>
                            <Users className="w-10 h-10" style={{color: 'var(--text-secondary)'}} />
                          </div>
                        </div>
                        {/* Verification Badge - Enhanced */}
                        <div className="absolute -top-1 -right-1 w-7 h-7 rounded-full flex items-center justify-center" style={{
                          backgroundColor: 'var(--primary)',
                          border: '2px solid var(--bg-primary)',
                          boxShadow: 'var(--shadow-large)'
                        }}>
                          <Shield className="w-4 h-4" style={{color: 'var(--bg-primary)'}} />
                        </div>
                      </div>

                      <div className="text-center">
                        <h3 className="font-semibold text-xl mb-2 macos-title" style={{
                          color: 'var(--text-primary)',
                          fontWeight: '600',
                          letterSpacing: '-0.01em'
                        }}>
                          {caregiver.full_name || 'Professional Caregiver'}
                        </h3>

                        {/* Rating Display - Enhanced */}
                        <div className="flex items-center justify-center gap-3 mb-3">
                          <div className="flex items-center gap-1">
                            {caregiver.average_rating ? [1, 2, 3, 4, 5].map((star) => (
                              <span key={star} className="text-lg" style={{color: star <= caregiver.average_rating ? 'var(--accent-warning)' : 'var(--text-muted)'}}>
                                ★
                              </span>
                            )) : (
                              <span className="text-sm" style={{color: 'var(--text-muted)'}}>Not rated</span>
                            )}
                          </div>
                          <span className="text-sm font-medium px-3 py-1 rounded-full" style={{
                            color: 'var(--text-secondary)',
                            backgroundColor: 'var(--bg-accent)'
                          }}>
                            {caregiver.average_rating ? `${caregiver.average_rating.toFixed(1)}★` : 'New provider'} {caregiver.reviews_count > 0 && `(${caregiver.reviews_count})`}
                          </span>
                        </div>

                        {/* Availability Indicator - Enhanced */}
                        {caregiver.availability_status && (
                          <div className="flex items-center justify-center gap-2 mb-4">
                            <div className="w-3 h-3 rounded-full" style={{
                              backgroundColor: caregiver.availability_status === 'available' ? 'var(--success)' : 'var(--text-muted)',
                              boxShadow: 'var(--shadow-small)'
                            }}></div>
                            <span className="text-sm font-medium px-3 py-1 rounded-full" style={{
                              color: caregiver.availability_status === 'available' ? 'var(--success)' : 'var(--text-muted)',
                              backgroundColor: caregiver.availability_status === 'available' ? 'var(--bg-success)' : 'var(--bg-secondary)'
                            }}>
                              {caregiver.availability_status === 'available' ? 'Available Now' : 'Currently Busy'}
                            </span>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Bio */}
                    {caregiver.bio && (
                      <p className="text-sm mb-4 leading-relaxed" style={{color: 'var(--text-secondary)'}}>
                        {caregiver.bio.length > 120 ? `${caregiver.bio.substring(0, 120)}...` : caregiver.bio}
                      </p>
                    )}
                    <div className="text-center mb-4">
                      {caregiver.location && (
                        <div className="flex items-center justify-center gap-1 mb-2" style={{color: 'var(--text-secondary)'}}>
                          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
                            <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                          </svg>
                          <span>{caregiver.location}</span>
                        </div>
                      )}
                      {caregiver.specialties && (
                        <div className="flex gap-2 justify-center flex-wrap">
                          {caregiver.specialties.slice(0, 2).map((specialty: string, index: number) => (
                            <span
                              key={index}
                              className="px-2 py-1 rounded text-sm"
                              style={{
                                backgroundColor: 'var(--bg-accent)',
                                color: 'var(--text-primary)'
                              }}
                            >
                              {specialty}
                            </span>
                          ))}
                        </div>
                      )}
                    </div>
                    <Link
                      to={`/provider/caregiver/${caregiver.id}`}
                      className="button-primary w-full py-2 px-4 rounded-lg font-medium transition-colors block text-center hover:opacity-90"
                    >
                      View Profile
                    </Link>
                  </div>
                ))}
              </div>

              <div className="text-center mt-8">
                <Link
                  to="/caregivers"
                  className="button-primary px-6 py-3 rounded-lg font-medium transition-colors inline-block hover:opacity-90"
                >
                  View All Caregivers →
                </Link>
              </div>
            </>
          ) : (
            <div className="text-center py-16">
              <div className="max-w-md mx-auto">
                <div className="w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-6" style={{backgroundColor: 'var(--bg-accent)'}}>
                  <Search className="w-8 h-8" style={{color: 'var(--primary)'}} />
                </div>
                <h3 className="text-xl font-semibold mb-3" style={{color: 'var(--text-primary)'}}>
                  {providerNetworkContent.title}
                </h3>
                <p className="text-base mb-6" style={{color: 'var(--text-secondary)'}}>
                  {providerNetworkContent.description}
                </p>
                <button
                  onClick={handleBrowseProviders}
                  className="button-primary px-6 py-3 rounded-lg font-medium macos-button-elegant"
                >
                  {providerNetworkContent.buttonText}
                </button>
              </div>
            </div>
          )}
        </div>
      </section>

      {/* Take Control Section */}
      <section className="py-16 sm:py-20 px-4 sm:px-8 text-center" style={{ backgroundColor: 'var(--bg-primary)' }}>
        <h2 className="text-3xl sm:text-4xl lg:text-5xl font-light mb-6" style={{
          color: 'var(--text-primary)',
          lineHeight: '1.2'
        }}>
          {takeControlContent.title}
        </h2>
        <p className="mb-8 text-base sm:text-lg max-w-2xl mx-auto" style={{
          color: 'var(--text-secondary)',
          lineHeight: '1.5'
        }}>
          {takeControlContent.description}
        </p>

        <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
          <button
            onClick={handleGetStarted}
            className="button-primary px-6 py-3 rounded-lg font-medium transition-colors hover:opacity-90"
          >
            Get Started →
          </button>

          <button
            onClick={handleBrowseProviders}
            className="button-secondary px-6 py-3 rounded-lg font-medium flex items-center gap-2 macos-button-elegant"
          >
            <Search className="w-4 h-4" />
            Browse Providers
          </button>
        </div>
      </section>

      {/* Footer - Apple Mac Desktop Style */}
      <footer
        className="py-20 sm:py-24 lg:py-28 px-4 sm:px-8 border-t"
        style={{
          backgroundColor: 'var(--bg-secondary)',
          borderColor: 'var(--border-light)'
        }}
      >
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-12 sm:gap-16 mb-12 sm:mb-16">
            {/* Logo and Description - Apple Mac Desktop Style */}
            <div className="lg:col-span-1 text-center sm:text-left">
              <div className="flex items-center justify-center sm:justify-start gap-4 mb-8">
                <div
                  className="w-12 h-12 rounded-2xl flex items-center justify-center"
                  style={{
                    backgroundColor: 'var(--primary)',
                    boxShadow: 'var(--shadow-large)'
                  }}
                >
                  <svg className="w-7 h-7" style={{ color: 'var(--bg-primary)' }} fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/>
                  </svg>
                </div>
                <span className="font-semibold text-2xl sm:text-3xl macos-title" style={{
                  color: 'var(--text-primary)',
                  fontWeight: '600',
                  letterSpacing: '-0.015em'
                }}>Care Connector</span>
              </div>
              <p className="mb-8 text-base sm:text-lg leading-relaxed max-w-md mx-auto sm:mx-0 macos-body" style={{color: 'var(--text-secondary)'}}>
                {footerContent.description}
              </p>
              <div className="flex flex-col sm:flex-row flex-wrap gap-6 sm:gap-8 justify-center sm:justify-start">

                <div className="flex items-center gap-3 text-base px-4 py-2 rounded-full" style={{
                  color: 'var(--text-secondary)',
                  backgroundColor: 'var(--bg-primary)',
                  border: '1px solid var(--border-light)'
                }}>
                  <UserCheck className="w-5 h-5" style={{color: 'var(--primary)'}} />
                  <span className="font-medium">Verified Providers</span>
                </div>
              </div>
            </div>

            {/* Services - Enhanced */}
            <div className="text-center sm:text-left">
              <h3 className="font-semibold mb-4 sm:mb-6 text-base sm:text-lg" style={{ color: 'var(--text-primary)' }}>{footerContent.servicesTitle}</h3>
              <ul className="space-y-4">
                <li>
                  <Link
                    to="/caregivers"
                    className="transition-all duration-200 hover:translate-x-1 focus:outline-none focus:ring-2 focus:ring-opacity-50 rounded-sm px-1 py-0.5"
                    style={{ color: 'var(--text-secondary)' }}
                    onMouseEnter={(e) => e.currentTarget.style.color = 'var(--primary)'}
                    onMouseLeave={(e) => e.currentTarget.style.color = 'var(--text-secondary)'}
                    onFocus={(e) => {
                      e.currentTarget.style.color = 'var(--primary)'
                      e.currentTarget.style.boxShadow = 'var(--focus-shadow)'
                    }}
                    onBlur={(e) => {
                      e.currentTarget.style.color = 'var(--text-secondary)'
                      e.currentTarget.style.boxShadow = 'none'
                    }}
                  >
                    Find Care
                  </Link>
                </li>
                <li>
                  <Link
                    to="/care-groups"
                    className="transition-all duration-200 hover:translate-x-1"
                    style={{ color: 'var(--text-secondary)' }}
                    onMouseEnter={(e) => e.currentTarget.style.color = 'var(--primary)'}
                    onMouseLeave={(e) => e.currentTarget.style.color = 'var(--text-secondary)'}
                  >
                    Care Groups
                  </Link>
                </li>
              </ul>
            </div>

            {/* Learn More - Enhanced */}
            <div className="text-center sm:text-left">
              <h3 className="font-semibold mb-4 sm:mb-6 text-base sm:text-lg" style={{ color: 'var(--text-primary)' }}>{footerContent.learnMoreTitle}</h3>
              <ul className="space-y-4">
                <li>
                  <Link
                    to="/how-it-works"
                    className="transition-all duration-200 hover:translate-x-1"
                    style={{ color: 'var(--text-secondary)' }}
                    onMouseEnter={(e) => e.currentTarget.style.color = 'var(--primary)'}
                    onMouseLeave={(e) => e.currentTarget.style.color = 'var(--text-secondary)'}
                  >
                    How It Works
                  </Link>
                </li>
                <li>
                  <Link
                    to="/features"
                    className="transition-all duration-200 hover:translate-x-1"
                    style={{ color: 'var(--text-secondary)' }}
                    onMouseEnter={(e) => e.currentTarget.style.color = 'var(--primary)'}
                    onMouseLeave={(e) => e.currentTarget.style.color = 'var(--text-secondary)'}
                  >
                    Features
                  </Link>
                </li>
              </ul>
            </div>

            {/* Legal & Compliance - New Section */}
            <div className="text-center sm:text-left">
              <h3 className="font-semibold mb-4 sm:mb-6 text-base sm:text-lg" style={{ color: 'var(--text-primary)' }}>Legal & Compliance</h3>
              <ul className="space-y-4">
                <li>
                  <Link
                    to="/privacy-policy"
                    className="transition-all duration-200 hover:translate-x-1"
                    style={{ color: 'var(--text-secondary)' }}
                    onMouseEnter={(e) => e.currentTarget.style.color = 'var(--primary)'}
                    onMouseLeave={(e) => e.currentTarget.style.color = 'var(--text-secondary)'}
                  >
                    Privacy Policy
                  </Link>
                </li>
                <li>
                  <Link
                    to="/terms-of-service"
                    className="transition-all duration-200 hover:translate-x-1"
                    style={{ color: 'var(--text-secondary)' }}
                    onMouseEnter={(e) => e.currentTarget.style.color = 'var(--primary)'}
                    onMouseLeave={(e) => e.currentTarget.style.color = 'var(--text-secondary)'}
                  >
                    Terms of Service
                  </Link>
                </li>

                <li>
                  <Link
                    to="/provider-portal"
                    className="transition-all duration-200 hover:translate-x-1"
                    style={{ color: 'var(--text-secondary)' }}
                    onMouseEnter={(e) => e.currentTarget.style.color = 'var(--primary)'}
                    onMouseLeave={(e) => e.currentTarget.style.color = 'var(--text-secondary)'}
                  >
                    Provider Portal
                  </Link>
                </li>
              </ul>
            </div>
          </div>

          <div
            className="pt-6 sm:pt-8 text-center border-t"
            style={{
              borderColor: 'var(--border-light)',
              color: 'var(--text-secondary)'
            }}
          >
            <p className="text-xs sm:text-sm">© {new Date().getFullYear()} Care Connector. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </main>
  )
}
