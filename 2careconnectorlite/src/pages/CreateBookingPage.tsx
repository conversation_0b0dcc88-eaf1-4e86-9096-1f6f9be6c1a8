import React, { useState, useEffect } from 'react';
import { useNavigate, useParams, useSearchParams } from 'react-router-dom';
import { Loader2, User, Calendar as CalendarIcon, Clock, MapPin, DollarSign, FileText, ArrowLeft, CheckCircle2, Lock } from 'lucide-react';
import { format } from 'date-fns';
import { supabase } from '../lib/supabase';

interface Provider {
  id: string;
  provider_type: string;
  first_name: string;
  last_name: string;
  email: string;
  hourly_rate?: number;
  location?: string;
  bio?: string;
  avatar_url?: string;
}

interface Service {
  id: string;
  provider_id: string;
  service_name: string;
  service_description?: string;
  custom_price_per_session?: number;
  duration_minutes?: number;
}

interface CareRecipient {
  id: string;
  name: string;
  relationship: string;
  age?: number;
  notes?: string;
}

interface BookingFormData {
  provider_id: string;
  provider_type: string;
  service_id?: string;
  care_recipient_id?: string;
  start_time: string;
  end_time: string;
  location?: string;
  special_requirements?: string;
  hourly_rate?: number;
}

const CreateBookingPage: React.FC = () => {
  const navigate = useNavigate();
  const { providerType, providerId } = useParams<{ providerType: string; providerId: string }>();
  const [searchParams] = useSearchParams();
  
  const [provider, setProvider] = useState<Provider | null>(null);
  const [services, setServices] = useState<Service[]>([]);
  const [careRecipients, setCareRecipients] = useState<CareRecipient[]>([]);
  const [loading, setLoading] = useState(true);
  const [creating, setCreating] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const [formData, setFormData] = useState<BookingFormData>({
    provider_id: providerId || '',
    provider_type: providerType || '',
    start_time: '',
    end_time: '',
    location: '',
    special_requirements: '',
  });

  useEffect(() => {
    const fetchPageData = async () => {
      if (!providerId || !providerType) {
        setError("Provider information is missing.");
        setLoading(false);
        return;
      }

      try {
        // Get current user
        const { data: { user } } = await supabase.auth.getUser();
        if (!user) {
          setError("Please sign in to create a booking.");
          setLoading(false);
          return;
        }

        // Normalize provider type (remove 's' if plural)
        const normalizedProviderType = providerType?.endsWith('s') ? providerType.slice(0, -1) : providerType;
        
        console.log('Fetching provider with ID:', providerId, 'Type:', normalizedProviderType);
        
        // Fetch provider details
        const { data: providerData, error: providerError } = await supabase
          .schema('care_connector')
          .from('profiles')
          .select('*')
          .eq('id', providerId)
          .eq('role', normalizedProviderType)
          .single();

        if (providerError) {
          console.error('Provider fetch error:', providerError);
          console.error('Searched for ID:', providerId, 'Role:', normalizedProviderType);
          
          // Try to get available providers for debugging
          const { data: allProviders } = await supabase
            .schema('care_connector')
            .from('profiles')
            .select('id, role, first_name, last_name')
            .eq('role', normalizedProviderType)
            .limit(5);
          
          console.error('Available providers with this role:', allProviders);
          throw new Error(`Provider with ID ${providerId} not found.`);
        }

        setProvider(providerData);

        // Fetch provider services (if not care_checker)
        if (providerType !== 'care_checker') {
          const { data: servicesData, error: servicesError } = await supabase
            .schema('care_connector')
            .from('service_provider_services')
            .select('*')
            .eq('provider_id', providerId);

          if (servicesError) {
            console.warn('Services fetch error (non-critical):', servicesError);
          } else {
            setServices(servicesData || []);
          }
        }

        // Fetch user's care recipients
        const { data: recipientsData, error: recipientsError } = await supabase
          .schema('care_connector')
          .from('care_recipients')
          .select('*')
          .eq('user_id', user.id);

        if (recipientsError) {
          console.warn('Care recipients fetch error (non-critical):', recipientsError);
        } else {
          setCareRecipients(recipientsData || []);
        }

        // Set initial form data
        setFormData(prev => ({
          ...prev,
          provider_id: providerId,
          provider_type: normalizedProviderType,
          hourly_rate: providerData.hourly_rate || 0,
          location: providerData.location || ''
        }));

      } catch (err: any) {
        console.error("Error fetching page data:", err.message);
        setError(err.message || "Failed to load booking information.");
      } finally {
        setLoading(false);
      }
    };

    fetchPageData();
  }, [providerId, providerType]);

  const handleInputChange = (field: keyof BookingFormData, value: string | number) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const calculateDuration = () => {
    if (formData.start_time && formData.end_time) {
      const start = new Date(formData.start_time);
      const end = new Date(formData.end_time);
      const diffMs = end.getTime() - start.getTime();
      const diffHours = diffMs / (1000 * 60 * 60);
      return Math.max(0, diffHours);
    }
    return 0;
  };

  const calculateTotalCost = () => {
    const duration = calculateDuration();
    const rate = formData.hourly_rate || provider?.hourly_rate || 0;
    return duration * rate;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.start_time || !formData.end_time) {
      alert('Please select start and end times.');
      return;
    }

    if (new Date(formData.start_time) >= new Date(formData.end_time)) {
      alert('End time must be after start time.');
      return;
    }

    if (calculateDuration() < 0.5) {
      alert('Booking must be at least 30 minutes.');
      return;
    }

    setCreating(true);
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        throw new Error('Please sign in to create a booking.');
      }

      const bookingData = {
        user_id: user.id,
        provider_id: formData.provider_id,
        provider_type: formData.provider_type,
        service_id: formData.service_id || null,
        care_recipient_id: formData.care_recipient_id || null,
        start_time: formData.start_time,
        end_time: formData.end_time,
        location: formData.location || provider?.location || 'TBD',
        special_requirements: formData.special_requirements || null,
        total_cost: calculateTotalCost(),
        status: 'pending',
        service_type: services.find(s => s.id === formData.service_id)?.service_name || 'Care Service'
      };

      const { data, error } = await supabase
        .schema('care_connector')
        .from('service_provider_bookings')
        .insert([bookingData])
        .select()
        .single();

      if (error) {
        throw error;
      }

      console.log('Booking created successfully:', data);
      
      // Navigate to booking detail page
      navigate(`/booking/${data.id}`, { 
        state: { message: 'Booking request sent successfully!' }
      });

    } catch (err: any) {
      console.error('Error creating booking:', err);
      alert(`Failed to create booking: ${err.message}`);
    } finally {
      setCreating(false);
    }
  };

  if (loading) {
    return (
              <div className="min-h-screen flex items-center justify-center" style={{backgroundColor: 'var(--bg-primary)'}}>
        <div className="flex items-center space-x-3">
                  <Loader2 className="h-6 w-6 animate-spin" style={{color: 'var(--primary)'}} />
        <span className="font-medium" style={{color: 'var(--text-secondary)'}}>Loading booking information...</span>
        </div>
      </div>
    );
  }

  if (error) {
    // Check if this is an authentication error
    const isAuthError = error.includes('sign in') || error.includes('Please sign in');
    
    if (isAuthError) {
      return (
        <div className="auth-error-container">
          <div className="auth-error-content">
            <div className="text-center">
              <Lock className="h-12 w-12 text-red-500 mx-auto mb-4" />
                      <h2 className="text-xl font-semibold mb-2" style={{color: 'var(--text-primary)'}}>Access Restricted</h2>
        <p className="mb-6" style={{color: 'var(--text-secondary)'}}>Please sign in to create bookings and access this feature.</p>
              <div className="space-y-3">
                <button
                  onClick={() => navigate('/auth')}
                  className="button-primary w-full py-3 px-4 rounded-lg font-medium transition-colors hover:opacity-90"
                >
                  Sign In
                </button>
                <button
                  onClick={() => navigate(-1)}
                  className="button-secondary w-full py-3 px-4 rounded-lg font-medium transition-colors hover:opacity-90"
                >
                  Go Back
                </button>
              </div>
            </div>
          </div>
        </div>
      );
    }
    
    // General error state for non-authentication errors
    return (
      <div className="min-h-screen flex items-center justify-center" style={{ backgroundColor: 'var(--bg-secondary)' }}>
        <div className="rounded-2xl shadow-sm border p-8 max-w-md w-full mx-4" style={{ backgroundColor: 'var(--bg-primary)', borderColor: 'var(--border-light)' }}>
          <div className="text-center">
            <CalendarIcon className="h-12 w-12 mx-auto mb-4" style={{ color: 'var(--text-error)' }} />
            <h2 className="text-xl font-semibold mb-2" style={{ color: 'var(--text-primary)' }}>Error Loading Page</h2>
            <p className="mb-6" style={{ color: 'var(--text-secondary)' }}>{error}</p>
            <button
              onClick={() => navigate(-1)}
              className="px-6 py-3 rounded-xl font-medium transition-colors"
              style={{
                backgroundColor: 'var(--primary)',
                color: 'var(--text-white)',
                boxShadow: 'var(--shadow-card)',
                transition: 'all 0.2s ease'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.opacity = '0.9'
                e.currentTarget.style.transform = 'translateY(-1px)'
                e.currentTarget.style.boxShadow = 'var(--shadow-hover)'
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.opacity = '1'
                e.currentTarget.style.transform = 'translateY(0)'
                e.currentTarget.style.boxShadow = 'var(--shadow-card)'
              }}
              onFocus={(e) => {
                e.currentTarget.style.boxShadow = 'var(--focus-shadow)'
              }}
              onBlur={(e) => {
                e.currentTarget.style.boxShadow = 'var(--shadow-card)'
              }}
            >
              Go Back
            </button>
          </div>
        </div>
      </div>
    );
  }

  if (!provider) {
    return (
      <div className="min-h-screen flex items-center justify-center" style={{ backgroundColor: 'var(--bg-secondary)' }}>
        <div className="rounded-2xl shadow-sm border p-8 max-w-md w-full mx-4" style={{ backgroundColor: 'var(--bg-primary)', borderColor: 'var(--border-light)' }}>
          <div className="text-center">
            <User className="h-12 w-12 mx-auto mb-4" style={{ color: 'var(--text-muted)' }} />
            <h2 className="text-xl font-semibold mb-2" style={{ color: 'var(--text-primary)' }}>Provider Not Found</h2>
            <p className="mb-6" style={{ color: 'var(--text-secondary)' }}>The care provider you are looking for could not be found.</p>
            <button
              onClick={() => navigate(-1)}
              className="px-6 py-3 rounded-xl font-medium transition-colors"
              style={{
                backgroundColor: 'var(--primary)',
                color: 'var(--text-white)',
                boxShadow: 'var(--shadow-card)',
                transition: 'all 0.2s ease'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.opacity = '0.9'
                e.currentTarget.style.transform = 'translateY(-1px)'
                e.currentTarget.style.boxShadow = 'var(--shadow-hover)'
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.opacity = '1'
                e.currentTarget.style.transform = 'translateY(0)'
                e.currentTarget.style.boxShadow = 'var(--shadow-card)'
              }}
              onFocus={(e) => {
                e.currentTarget.style.boxShadow = 'var(--focus-shadow)'
              }}
              onBlur={(e) => {
                e.currentTarget.style.boxShadow = 'var(--shadow-card)'
              }}
            >
              Go Back
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen" style={{ backgroundColor: 'var(--bg-secondary)' }}>
      {/* Apple Mac Desktop Style Header */}
      <div className="border-b" style={{ backgroundColor: 'var(--bg-primary)', borderColor: 'var(--border-light)', boxShadow: 'var(--shadow-medium)' }}>
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-6 sm:py-8">
          <div className="flex items-center">
            <button
              onClick={() => navigate(-1)}
              className="flex items-center mr-6 px-4 py-2 rounded-xl font-medium transition-all duration-200 hover:scale-105"
              style={{
                color: 'var(--text-secondary)',
                backgroundColor: 'var(--bg-secondary)',
                border: '1px solid var(--border-light)'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.color = 'var(--text-primary)'
                e.currentTarget.style.boxShadow = 'var(--shadow-medium)'
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.color = 'var(--text-secondary)'
                e.currentTarget.style.boxShadow = 'none'
              }}
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back
            </button>
            <div>
              <h1 className="text-2xl sm:text-3xl font-semibold macos-title" style={{
                color: 'var(--text-primary)',
                fontWeight: '600',
                letterSpacing: '-0.01em'
              }}>Create Booking</h1>
              <p className="text-base sm:text-lg macos-body mt-1" style={{
                color: 'var(--text-secondary)',
                fontWeight: '400'
              }}>Book an appointment with {provider.first_name} {provider.last_name}</p>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          
          {/* Main Form */}
          <div className="lg:col-span-2">
            <form
              onSubmit={handleSubmit}
              className="space-y-6"
              onKeyDown={(e) => {
                if (e.key === 'Enter' && (e.target as HTMLElement).tagName !== 'TEXTAREA') {
                  e.preventDefault()
                  const form = e.currentTarget
                  const inputs = Array.from(form.querySelectorAll('input, select, textarea, button'))
                  const currentIndex = inputs.indexOf(e.target as HTMLElement)
                  const nextInput = inputs[currentIndex + 1] as HTMLElement
                  if (nextInput) {
                    nextInput.focus()
                  }
                }
              }}
            >
              
              {/* Provider Information */}
              <div className="rounded-2xl shadow-sm border p-6" style={{ backgroundColor: 'var(--bg-primary)', borderColor: 'var(--border-light)' }}>
                <h2 className="text-lg font-medium mb-4" style={{color: 'var(--text-primary)'}}>Provider Information</h2>
                
                <div className="flex items-center space-x-4 mb-4">
                  <div className="h-16 w-16 rounded-full flex items-center justify-center" style={{ backgroundColor: 'var(--bg-accent)', opacity: 0.1 }}>
                    <User className="h-8 w-8" style={{ color: 'var(--primary)' }} />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold" style={{color: 'var(--text-primary)'}}>
                      {provider.first_name} {provider.last_name}
                    </h3>
                    <p className="capitalize" style={{ color: 'var(--text-secondary)' }}>{provider.provider_type}</p>
                    {provider.hourly_rate && (
                      <p className="font-medium" style={{ color: 'var(--primary)' }}>${provider.hourly_rate}/hour</p>
                    )}
                  </div>
                </div>
                
                {provider.bio && (
                  <p className="mb-4" style={{ color: 'var(--text-secondary)' }}>{provider.bio}</p>
                )}

                {provider.location && (
                  <div className="flex items-center" style={{ color: 'var(--text-secondary)' }}>
                    <MapPin className="mr-2 h-4 w-4" />
                    {provider.location}
                  </div>
                )}
              </div>

              {/* Service Selection */}
              {services.length > 0 && (
                <div className="rounded-2xl shadow-sm border p-6" style={{ backgroundColor: 'var(--bg-primary)', borderColor: 'var(--border-light)' }}>
                  <h2 className="text-xl font-semibold mb-4" style={{color: 'var(--text-primary)'}}>Select Service</h2>
                  
                  <div className="space-y-3">
                    {services.map((service) => (
                      <label key={service.id} className="flex items-center space-x-3 p-4 border rounded-xl cursor-pointer transition-all duration-200" style={{borderColor: 'var(--border-light)', backgroundColor: 'var(--bg-primary)'}} onMouseEnter={(e) => e.currentTarget.style.backgroundColor = 'var(--bg-accent)'} onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'var(--bg-primary)'}>
                        <input
                          type="radio"
                          name="service"
                          value={service.id}
                          checked={formData.service_id === service.id}
                          onChange={(e) => handleInputChange('service_id', e.target.value)}
                          style={{accentColor: 'var(--primary)'}} className="focus:ring-2"
                        />
                        <div className="flex-1">
                          <h4 className="font-medium" style={{color: 'var(--text-primary)'}}>{service.service_name}</h4>
                          {service.service_description && (
                            <p className="text-sm" style={{color: 'var(--text-secondary)'}}>{service.service_description}</p>
                          )}
                          {service.custom_price_per_session && (
                            <p className="text-sm font-medium" style={{color: 'var(--primary)'}}>
                              ${service.custom_price_per_session}/session
                            </p>
                          )}
                        </div>
                      </label>
                    ))}
                  </div>
                </div>
              )}

              {/* Care Recipient Selection */}
              {careRecipients.length > 0 && (
                <div className="rounded-2xl shadow-sm border p-6" style={{ backgroundColor: 'var(--bg-primary)', borderColor: 'var(--border-light)' }}>
                  <h2 className="text-xl font-semibold mb-4" style={{color: 'var(--text-primary)'}}>Care Recipient</h2>
                  
                  <div className="space-y-3">
                    {careRecipients.map((recipient) => (
                      <label key={recipient.id} className="flex items-center space-x-3 p-4 border rounded-xl cursor-pointer transition-all duration-200" style={{borderColor: 'var(--border-light)', backgroundColor: 'var(--bg-primary)'}} onMouseEnter={(e) => e.currentTarget.style.backgroundColor = 'var(--bg-accent)'} onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'var(--bg-primary)'}>
                        <input
                          type="radio"
                          name="care_recipient"
                          value={recipient.id}
                          checked={formData.care_recipient_id === recipient.id}
                          onChange={(e) => handleInputChange('care_recipient_id', e.target.value)}
                          className="text-var(--logo-green) focus:ring-var(--logo-green)"
                        />
                        <div className="flex-1">
                                                  <h4 className="font-medium" style={{color: 'var(--text-primary)'}}>{recipient.name}</h4>
                        <p className="text-sm" style={{color: 'var(--text-secondary)'}}>{recipient.relationship}</p>
                        {recipient.age && (
                          <p className="text-sm" style={{color: 'var(--text-secondary)'}}>Age: {recipient.age}</p>
                        )}
                        </div>
                      </label>
                    ))}
                  </div>
                </div>
              )}

              {/* Date and Time */}
              <div className="rounded-2xl shadow-sm border p-6" style={{ backgroundColor: 'var(--bg-primary)', borderColor: 'var(--border-light)' }}>
                <h2 className="text-xl font-semibold mb-4" style={{color: 'var(--text-primary)'}}>Date & Time</h2>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium mb-2" style={{color: 'var(--text-secondary)'}}>Start Time</label>
                    <input
                      type="datetime-local"
                      value={formData.start_time}
                      onChange={(e) => handleInputChange('start_time', e.target.value)}
                      className="w-full px-4 py-3 border rounded-xl focus:outline-none focus:ring-2 focus:border-transparent" style={{borderColor: 'var(--border-light)', accentColor: 'var(--primary)'}}
                      required
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium mb-2" style={{color: 'var(--text-secondary)'}}>End Time</label>
                    <input
                      type="datetime-local"
                                        value={formData.end_time}
                  onChange={(e) => handleInputChange('end_time', e.target.value)}
                  className="w-full px-4 py-3 border rounded-xl focus:outline-none focus:ring-2" style={{borderColor: 'var(--border-light)', backgroundColor: 'var(--bg-primary)', color: 'var(--text-primary)'}} onFocus={(e) => {e.currentTarget.style.borderColor = 'var(--primary)'; e.currentTarget.style.boxShadow = '0 0 0 3px var(--focus-shadow)'}} onBlur={(e) => {e.currentTarget.style.borderColor = 'var(--border-light)'; e.currentTarget.style.boxShadow = 'none'}}
                      required
                    />
                  </div>
                </div>
                
                {formData.start_time && formData.end_time && (
                  <div className="mt-4 p-4 rounded-xl" style={{backgroundColor: 'var(--bg-accent)'}}>
                    <div className="flex items-center justify-between text-sm">
                      <span style={{color: 'var(--text-secondary)'}}>Duration:</span>
                      <span className="font-medium" style={{color: 'var(--text-primary)'}}>{calculateDuration().toFixed(1)} hours</span>
                    </div>
                  </div>
                )}
              </div>

              {/* Location */}
              <div className="rounded-2xl shadow-sm border p-6" style={{ backgroundColor: 'var(--bg-primary)', borderColor: 'var(--border-light)' }}>
                <h2 className="text-xl font-semibold mb-4" style={{color: 'var(--text-primary)'}}>Location</h2>
                
                <input
                  type="text"
                  placeholder="Service location (optional)"
                  value={formData.location}
                  onChange={(e) => handleInputChange('location', e.target.value)}
                  className="w-full px-4 py-3 border rounded-xl focus:outline-none focus:ring-2 focus:border-transparent" style={{borderColor: 'var(--border-light)', accentColor: 'var(--primary)'}}
                />
                
                {provider.location && (
                  <p className="mt-2 text-sm text-secondary">
                    Provider's location: {provider.location}
                  </p>
                )}
              </div>

              {/* Special Requirements */}
              <div className="rounded-2xl shadow-sm border p-6" style={{ backgroundColor: 'var(--bg-primary)', borderColor: 'var(--border-light)' }}>
                <h2 className="text-xl font-semibold mb-4" style={{color: 'var(--text-primary)'}}>Special Requirements</h2>
                
                <textarea
                  placeholder="Any specific instructions, requirements, or notes for the provider..."
                  value={formData.special_requirements}
                  onChange={(e) => handleInputChange('special_requirements', e.target.value)}
                  rows={4}
                  className="w-full px-4 py-3 border border-light rounded-xl focus:outline-none focus:ring-2 focus:ring-var(--logo-green) focus:border-transparent resize-none"
                />
              </div>
            </form>
          </div>

          {/* Booking Summary Sidebar */}
          <div className="space-y-6">
            <div className="rounded-2xl shadow-sm border p-6 sticky top-6" style={{ backgroundColor: 'var(--bg-primary)', borderColor: 'var(--border-light)' }}>
                              <h3 className="text-lg font-semibold mb-4" style={{color: 'var(--text-primary)'}}>Booking Summary</h3>
              
              <div className="space-y-3 mb-6">
                <div className="flex justify-between text-sm">
                                  <span style={{color: 'var(--text-secondary)'}}>Provider:</span>
                <span className="font-medium" style={{color: 'var(--text-primary)'}}>
                  {provider.first_name} {provider.last_name}
                </span>
                </div>
                
                {formData.service_id && (
                  <div className="flex justify-between text-sm">
                    <span style={{color: 'var(--text-secondary)'}}>Service:</span>
                    <span className="font-medium" style={{color: 'var(--text-primary)'}}>
                      {services.find(s => s.id === formData.service_id)?.service_name || 'Selected Service'}
                    </span>
                  </div>
                )}
                
                {formData.care_recipient_id && (
                  <div className="flex justify-between text-sm">
                    <span style={{color: 'var(--text-secondary)'}}>For:</span>
                    <span className="font-medium" style={{color: 'var(--text-primary)'}}>
                      {careRecipients.find(r => r.id === formData.care_recipient_id)?.name || 'Selected Recipient'}
                    </span>
                  </div>
                )}
                
                {formData.start_time && (
                  <div className="flex justify-between text-sm">
                    <span style={{color: 'var(--text-secondary)'}}>Date:</span>
                    <span className="font-medium" style={{color: 'var(--text-primary)'}}>
                      {format(new Date(formData.start_time), 'MMM d, yyyy')}
                    </span>
                  </div>
                )}
                
                {formData.start_time && formData.end_time && (
                  <div className="flex justify-between text-sm">
                    <span style={{color: 'var(--text-secondary)'}}>Time:</span>
                    <span className="font-medium" style={{color: 'var(--text-primary)'}}>
                      {format(new Date(formData.start_time), 'h:mm a')} - {format(new Date(formData.end_time), 'h:mm a')}
                    </span>
                  </div>
                )}
                
                {calculateDuration() > 0 && (
                  <div className="flex justify-between text-sm">
                    <span style={{color: 'var(--text-secondary)'}}>Duration:</span>
                    <span className="font-medium" style={{color: 'var(--text-primary)'}}>{calculateDuration().toFixed(1)} hours</span>
                  </div>
                )}
                
                {provider.hourly_rate && calculateDuration() > 0 && (
                  <div className="flex justify-between text-sm">
                    <span style={{color: 'var(--text-secondary)'}}>Rate:</span>
                    <span className="font-medium" style={{color: 'var(--text-primary)'}}>${provider.hourly_rate}/hour</span>
                  </div>
                )}
              </div>
              
              {calculateTotalCost() > 0 && (
                <div className="border-t border-light pt-4 mb-6">
                  <div className="flex justify-between">
                    <span className="font-semibold" style={{color: 'var(--text-primary)'}}>Total Cost:</span>
                    <span className="font-semibold text-var(--logo-green) text-lg">
                      ${calculateTotalCost().toFixed(2)}
                    </span>
                  </div>
                </div>
              )}
              
              <button
                type="submit"
                onClick={handleSubmit}
                disabled={creating || !formData.start_time || !formData.end_time}
                className="w-full flex items-center justify-center px-6 py-4 rounded-xl font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                style={{ backgroundColor: 'var(--primary)', color: 'var(--bg-primary)' }}
                onMouseEnter={(e) => !e.currentTarget.disabled && (e.currentTarget.style.backgroundColor = 'var(--primary)')}
                onMouseLeave={(e) => !e.currentTarget.disabled && (e.currentTarget.style.backgroundColor = 'var(--primary)')}
              >
                {creating ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Creating Booking...
                  </>
                ) : (
                  <>
                    <CheckCircle2 className="mr-2 h-4 w-4" />
                    Send Booking Request
                  </>
                )}
              </button>
              
              <p className="text-xs mt-3 text-center" style={{color: 'var(--text-secondary)'}}>
                Your booking request will be sent to the provider for confirmation.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CreateBookingPage;
