import React, { useEffect, useState } from 'react'
import { useNavigate, useSearchParams } from 'react-router-dom'
import { CheckCircle, XCircle, Mail, ArrowRight } from 'lucide-react'
import { supabase } from '../lib/supabase'
import { authService } from '../services/authService'
import { securityAuditService } from '../services/securityAuditService'

export default function EmailVerified() {
  const navigate = useNavigate()
  const [searchParams] = useSearchParams()
  const [verificationStatus, setVerificationStatus] = useState<'loading' | 'success' | 'error'>('loading')
  const [errorMessage, setErrorMessage] = useState('')

  useEffect(() => {
    const handleEmailVerification = async () => {
      try {
        // Get tokens from URL parameters
        const accessToken = searchParams.get('access_token')
        const refreshToken = searchParams.get('refresh_token')
        const type = searchParams.get('type')

        if (type === 'signup' && accessToken && refreshToken) {
          // Set the session with the tokens
          const { data, error } = await supabase.auth.setSession({
            access_token: accessToken,
            refresh_token: refreshToken
          })

          if (error) {
            console.error('Error setting session:', error)
            setVerificationStatus('error')
            setErrorMessage('Failed to verify email. Please try again.')
            
            await securityAuditService.logSecurityEvent({
              event_type: 'suspicious_activity',
              severity: 'medium',
              details: {
                reason: 'Email verification failed',
                error: error.message,
                type
              }
            })
            return
          }

          if (data.user) {
            console.log('✅ Email verified successfully for user:', data.user.email)
            
            // Log successful email verification
            await securityAuditService.logSecurityEvent({
              user_id: data.user.id,
              event_type: 'login',
              severity: 'low',
              details: {
                email: data.user.email,
                emailVerification: true,
                timestamp: new Date().toISOString()
              }
            })

            setVerificationStatus('success')
            
            // Redirect to dashboard after 3 seconds
            setTimeout(() => {
              navigate('/dashboard')
            }, 3000)
          } else {
            setVerificationStatus('error')
            setErrorMessage('No user data found. Please try signing up again.')
          }
        } else {
          setVerificationStatus('error')
          setErrorMessage('Invalid verification link. Please check your email or try signing up again.')
          
          await securityAuditService.logSecurityEvent({
            event_type: 'suspicious_activity',
            severity: 'medium',
            details: {
              reason: 'Invalid email verification attempt',
              hasAccessToken: !!accessToken,
              hasRefreshToken: !!refreshToken,
              type
            }
          })
        }
      } catch (error) {
        console.error('Email verification error:', error)
        setVerificationStatus('error')
        setErrorMessage('An unexpected error occurred during verification.')
        
        await securityAuditService.logSecurityEvent({
          event_type: 'suspicious_activity',
          severity: 'high',
          details: {
            reason: 'Unexpected error during email verification',
            error: error instanceof Error ? error.message : String(error)
          }
        })
      }
    }

    handleEmailVerification()
  }, [searchParams, navigate])

  const handleContinue = () => {
    if (verificationStatus === 'success') {
      navigate('/dashboard')
    } else {
      navigate('/get-started')
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center p-4" style={{
      backgroundColor: 'var(--bg-primary)'
    }}>
      <div 
        className="max-w-md w-full rounded-2xl p-8 shadow-2xl border text-center"
        style={{
          backgroundColor: 'var(--bg-primary)',
          borderColor: 'var(--border-medium)',
          boxShadow: 'var(--shadow-large)'
        }}
      >
        {/* Status Icon */}
        <div className="flex justify-center mb-6">
          {verificationStatus === 'loading' && (
            <div 
              className="p-4 rounded-full animate-pulse"
              style={{
                backgroundColor: 'var(--bg-secondary)',
                color: 'var(--primary)'
              }}
            >
              <Mail className="w-8 h-8" />
            </div>
          )}
          
          {verificationStatus === 'success' && (
            <div 
              className="p-4 rounded-full"
              style={{
                backgroundColor: 'var(--bg-success)',
                color: 'var(--text-success)'
              }}
            >
              <CheckCircle className="w-8 h-8" />
            </div>
          )}
          
          {verificationStatus === 'error' && (
            <div 
              className="p-4 rounded-full"
              style={{
                backgroundColor: 'var(--bg-error)',
                color: 'var(--text-error)'
              }}
            >
              <XCircle className="w-8 h-8" />
            </div>
          )}
        </div>

        {/* Title */}
        <h1 className="text-2xl font-semibold mb-4 macos-title" style={{
          color: 'var(--text-primary)',
          fontWeight: '600',
          letterSpacing: '-0.01em'
        }}>
          {verificationStatus === 'loading' && 'Verifying Email...'}
          {verificationStatus === 'success' && 'Email Verified!'}
          {verificationStatus === 'error' && 'Verification Failed'}
        </h1>

        {/* Message */}
        <p className="mb-8 macos-body" style={{
          color: 'var(--text-secondary)',
          fontWeight: '400',
          letterSpacing: '-0.005em',
          lineHeight: '1.5'
        }}>
          {verificationStatus === 'loading' && 'Please wait while we verify your email address...'}
          {verificationStatus === 'success' && 'Your email has been successfully verified. You will be redirected to your dashboard shortly.'}
          {verificationStatus === 'error' && (errorMessage || 'There was a problem verifying your email. Please try again.')}
        </p>

        {/* Action Button */}
        {verificationStatus !== 'loading' && (
          <button
            onClick={handleContinue}
            className="w-full py-4 px-6 rounded-xl text-base font-medium text-white transition-all duration-200 macos-body flex items-center justify-center"
            style={{
              backgroundColor: verificationStatus === 'success' ? 'var(--primary)' : 'var(--text-error)',
              boxShadow: 'var(--shadow-medium)'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.opacity = '0.9'
              e.currentTarget.style.transform = 'translateY(-1px)'
              e.currentTarget.style.boxShadow = 'var(--shadow-large)'
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.opacity = '1'
              e.currentTarget.style.transform = 'translateY(0)'
              e.currentTarget.style.boxShadow = 'var(--shadow-medium)'
            }}
          >
            {verificationStatus === 'success' ? 'Continue to Dashboard' : 'Try Again'}
            <ArrowRight className="w-5 h-5 ml-2" />
          </button>
        )}

        {/* Loading indicator */}
        {verificationStatus === 'loading' && (
          <div className="flex justify-center">
            <div 
              className="w-8 h-8 border-4 border-t-transparent rounded-full animate-spin"
              style={{
                borderColor: 'var(--primary)',
                borderTopColor: 'transparent'
              }}
            />
          </div>
        )}

        {/* Security note */}
        <p className="text-center mt-6 text-sm macos-body" style={{
          color: 'var(--text-muted)',
          fontWeight: '400'
        }}>
          Email verification is required for account security
        </p>
      </div>
    </div>
  )
}
