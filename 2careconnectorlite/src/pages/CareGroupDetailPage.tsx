import React, { useState, useEffect } from 'react'
import { usePara<PERSON>, useNavigate } from 'react-router-dom'
import { Users, Calendar, MessageSquare, Settings, MapPin, UserPlus, CheckCircle, AlertCircle } from 'lucide-react'
import { supabase } from '../lib/supabase'

interface CareGroupDetail {
  id: string
  name: string
  description: string
  group_type: 'public' | 'private' | 'support'
  location: string
  member_count: number
  max_members: number
  created_at: string
  tags: string[]
  members: Array<{
    user_id: string
    full_name: string
    avatar_url?: string
    role: 'admin' | 'moderator' | 'member'
    joined_at: string
  }>
  recent_activity: Array<{
    id: string
    type: string
    content: string
    user_name: string
    created_at: string
  }>
}

const CareGroupDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>()
  const navigate = useNavigate()
  const [user, setUser] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [processing, setProcessing] = useState(false)
  const [careGroup, setCareGroup] = useState<CareGroupDetail | null>(null)
  const [activeTab, setActiveTab] = useState('overview')
  const [userMembership, setUserMembership] = useState<any>(null)
  const [debugInfo, setDebugInfo] = useState<string>('Initializing...')

  useEffect(() => {
    const getUser = async () => {
      console.log('🔍 CareGroupDetailPage: useEffect triggered - Starting getUser...')
      console.log('🔍 CareGroupDetailPage: URL id parameter:', id)
      
      const { data: { user } } = await supabase.auth.getUser()
      console.log('🔍 CareGroupDetailPage: Authentication result - User:', user ? 'authenticated' : 'not authenticated')
      console.log('🔍 CareGroupDetailPage: User ID:', user?.id || 'null')
      
      setUser(user)
      
      console.log('🔍 CareGroupDetailPage: Checking conditions - user:', !!user, 'id:', !!id)
      
      if (user && id) {
        console.log('✅ CareGroupDetailPage: Both user and id present - calling loadCareGroupDetail...')
        await loadCareGroupDetail()
        await checkUserMembership()
      } else {
        console.log('❌ CareGroupDetailPage: Missing required data - user:', !!user, 'id:', !!id)
        if (!user) console.log('❌ CareGroupDetailPage: AUTHENTICATION ISSUE - User not authenticated')
        if (!id) console.log('❌ CareGroupDetailPage: ROUTING ISSUE - ID parameter missing')
      }
      
      console.log('🔍 CareGroupDetailPage: Setting loading to false...')
      setLoading(false)
    }
    getUser()
  }, [id])

  const loadCareGroupDetail = async () => {
    if (!id) {
      setDebugInfo('❌ No ID parameter provided')
      return
    }

    setDebugInfo('🔍 Starting loadCareGroupDetail...')
    console.log('🔍 CareGroupDetailPage: Starting loadCareGroupDetail with ID:', id)

    try {
      setDebugInfo('🔍 STEP 1: Querying care_groups table...')
      console.log('🔍 CareGroupDetailPage: STEP 1 - Querying care_groups table with ID:', id)
      const { data: groupData, error: groupError } = await supabase
        .from('care_groups')
        .select('*')
        .eq('id', id)
        .single()

      console.log('🔍 CareGroupDetailPage: STEP 1 RESULT - Data:', groupData, 'Error:', groupError)

      if (groupError) {
        setDebugInfo(`❌ STEP 1 FAILED: ${groupError.message || 'care_groups query failed'}`)
        console.error('❌ CareGroupDetailPage: STEP 1 FAILED - care_groups query failed:', groupError)
        throw groupError
      }

      setDebugInfo('✅ STEP 1 SUCCESS: care_groups query completed')
      console.log('✅ CareGroupDetailPage: STEP 1 SUCCESS - care_groups query completed successfully')
      
      setDebugInfo('🔍 STEP 2: Querying care_group_members table...')
      console.log('🔍 CareGroupDetailPage: STEP 2 - Querying care_group_members table with group_id:', id)
      
      // First get members without foreign key reference
      const { data: membersData, error: membersError } = await supabase
        .from('care_group_members')
        .select('user_id, role, joined_at')
        .eq('group_id', id)

      console.log('🔍 CareGroupDetailPage: STEP 2A RESULT - Members Data:', membersData, 'Error:', membersError)

      if (membersError) {
        setDebugInfo(`❌ STEP 2A FAILED: ${membersError.message || 'care_group_members query failed'}`)
        console.error('❌ CareGroupDetailPage: STEP 2A FAILED - care_group_members query failed:', membersError)
        throw membersError
      }

      setDebugInfo('🔍 STEP 2B: Querying profiles for member details...')
      console.log('🔍 CareGroupDetailPage: STEP 2B - Querying profiles for member details')
      
      // Get user IDs for profile lookup
      const userIds = (membersData || []).map(member => member.user_id)
      
      // Get profiles for all member user IDs
      const { data: profilesData, error: profilesError } = await supabase
        .from('profiles')
        .select('id, full_name, avatar_url')
        .in('id', userIds)

      console.log('🔍 CareGroupDetailPage: STEP 2B RESULT - Profiles Data:', profilesData, 'Error:', profilesError)

      if (profilesError) {
        setDebugInfo(`❌ STEP 2B FAILED: ${profilesError.message || 'profiles query failed'}`)
        console.error('❌ CareGroupDetailPage: STEP 2B FAILED - profiles query failed:', profilesError)
        throw profilesError
      }

      setDebugInfo('✅ STEP 2 SUCCESS: care_group_members query completed')
      console.log('✅ CareGroupDetailPage: STEP 2 SUCCESS - care_group_members query completed successfully')
      
      setDebugInfo('🔍 STEP 3: Querying care_group_activity table (optional)...')
      console.log('🔍 CareGroupDetailPage: STEP 3 - Attempting to query care_group_activity table with group_id:', id)

      // Try to get activity data - make it optional if table doesn't exist
      let activityData = []
      let activityProfilesData = []
      
      try {
        const { data: activityResult, error: activityError } = await supabase
          .from('care_group_activity')
          .select('id, activity_type, content, created_at, user_id')
          .eq('group_id', id)
          .order('created_at', { ascending: false })
          .limit(10)

        console.log('🔍 CareGroupDetailPage: STEP 3A RESULT - Activity Data:', activityResult, 'Error:', activityError)

        if (activityError) {
          // If table doesn't exist, log but don't throw - continue without activity
          if (activityError.message?.includes('does not exist')) {
            setDebugInfo('⚠️ STEP 3A SKIPPED: care_group_activity table does not exist (continuing without activity)')
            console.warn('⚠️ CareGroupDetailPage: STEP 3A SKIPPED - care_group_activity table does not exist, continuing without activity')
          } else {
            setDebugInfo(`❌ STEP 3A FAILED: ${activityError.message || 'care_group_activity query failed'}`)
            console.error('❌ CareGroupDetailPage: STEP 3A FAILED - care_group_activity query failed:', activityError)
            throw activityError
          }
        } else {
          activityData = activityResult || []
          
          if (activityData.length > 0) {
            setDebugInfo('🔍 STEP 3B: Querying profiles for activity users...')
            console.log('🔍 CareGroupDetailPage: STEP 3B - Querying profiles for activity users')
            
            // Get activity user IDs for profile lookup
            const activityUserIds = activityData.map(activity => activity.user_id).filter(Boolean)
            
            // Get profiles for activity users
            if (activityUserIds.length > 0) {
              const { data: profilesResult, error: activityProfilesError } = await supabase
                .from('profiles')
                .select('id, full_name')
                .in('id', activityUserIds)

              console.log('🔍 CareGroupDetailPage: STEP 3B RESULT - Activity Profiles Data:', profilesResult, 'Error:', activityProfilesError)

              if (activityProfilesError) {
                setDebugInfo(`❌ STEP 3B FAILED: ${activityProfilesError.message || 'activity profiles query failed'}`)
                console.error('❌ CareGroupDetailPage: STEP 3B FAILED - activity profiles query failed:', activityProfilesError)
                throw activityProfilesError
              }
              
              activityProfilesData = profilesResult || []
            }
          }
        }
      } catch (error) {
        // If it's a table existence error, continue without activity, otherwise re-throw
        if (error.message?.includes('does not exist')) {
          setDebugInfo('⚠️ STEP 3 SKIPPED: care_group_activity table does not exist (continuing without activity)')
          console.warn('⚠️ CareGroupDetailPage: STEP 3 SKIPPED - care_group_activity table does not exist, continuing without activity')
        } else {
          throw error
        }
      }

      setDebugInfo('✅ STEP 3 SUCCESS: care_group_activity query completed')
      console.log('✅ CareGroupDetailPage: STEP 3 SUCCESS - care_group_activity query completed successfully')
      
      setDebugInfo('🔍 STEP 4: Setting careGroup state...')
      console.log('🔍 CareGroupDetailPage: STEP 4 - All queries successful, calling setCareGroup...')

      setCareGroup({
        id: groupData.id,
        name: groupData.name,
        description: groupData.description,
        group_type: groupData.group_type,
        location: groupData.location,
        member_count: groupData.member_count,
        max_members: groupData.max_members,
        created_at: groupData.created_at,
        tags: groupData.tags || [],
        members: (membersData || []).map(member => {
          // Find corresponding profile data
          const profile = (profilesData || []).find(p => p.id === member.user_id)
          return {
            user_id: member.user_id,
            full_name: profile?.full_name || 'Unknown User',
            avatar_url: profile?.avatar_url,
            role: member.role,
            joined_at: member.joined_at
          }
        }),
        recent_activity: (activityData || []).map(activity => {
          // Find corresponding profile data for activity user
          const activityProfile = (activityProfilesData || []).find(p => p.id === activity.user_id)
          return {
            id: activity.id,
            type: activity.activity_type,
            content: activity.content,
            user_name: activityProfile?.full_name || 'Unknown User',
            created_at: activity.created_at
          }
        })
      })
    } catch (error) {
      console.error('Error loading care group detail:', error)
    }
  }

  const checkUserMembership = async () => {
    if (!user || !id) return

    try {
      const { data, error } = await supabase
        .from('care_group_members')
        .select('role, joined_at')
        .eq('group_id', id)
        .eq('user_id', user.id)
        .single()

      if (error && error.code !== 'PGRST116') throw error
      setUserMembership(data)
    } catch (error) {
      console.error('Error checking membership:', error)
    }
  }

  const joinCareGroup = async () => {
    if (!user || !id || !careGroup) return

    setProcessing(true)
    try {
      const { error } = await supabase
        .from('care_group_members')
        .insert({
          group_id: id,
          user_id: user.id,
          role: 'member'
        })

      if (error) throw error

      await supabase
        .from('care_groups')
        .update({ member_count: careGroup.member_count + 1 })
        .eq('id', id)

      await loadCareGroupDetail()
      await checkUserMembership()
    } catch (error) {
      console.error('Error joining care group:', error)
    } finally {
      setProcessing(false)
    }
  }

  const getGroupTypeColor = (type: string) => {
    switch (type) {
      case 'public': return 'bg-green-100 text-green-800'
      case 'private': return 'bg-blue-100 text-blue-800'
      case 'support': return 'bg-purple-100 text-purple-800'
      default: return 'bg-green-100 text-green-800'
    }
  }

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'admin': return 'bg-red-100 text-red-800'
      case 'moderator': return 'bg-orange-100 text-orange-800'
      default: return 'bg-green-100 text-green-800'
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center" style={{ backgroundColor: 'var(--bg-secondary)' }}>
        <div className="text-center">
          <div className="inline-flex items-center gap-5 px-12 py-8 rounded-3xl" style={{
            backgroundColor: 'var(--bg-primary)',
            border: '1px solid var(--border-light)',
            boxShadow: 'var(--shadow-large)',
            backdropFilter: 'blur(10px)'
          }}>
            <div className="w-10 h-10 rounded-full animate-spin" style={{
              border: '3px solid var(--border-light)',
              borderTop: '3px solid var(--primary)'
            }}></div>
            <div>
              <div className="text-2xl font-semibold mb-2 macos-title" style={{
                color: 'var(--text-primary)',
                fontWeight: '600',
                letterSpacing: '-0.01em'
              }}>Loading Care Group</div>
              <div className="text-lg macos-body" style={{
                color: 'var(--text-secondary)',
                fontWeight: '400',
                letterSpacing: '-0.005em'
              }}>Getting group details...</div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (!careGroup) {
    return (
      <div className="min-h-screen flex items-center justify-center" style={{ backgroundColor: 'var(--bg-secondary)' }}>
        <div className="rounded-3xl p-12 max-w-md w-full mx-4" style={{
          backgroundColor: 'var(--bg-primary)',
          border: '1px solid var(--border-light)',
          boxShadow: 'var(--shadow-large)',
          backdropFilter: 'blur(10px)'
        }}>
          <div className="text-center">
            <div className="w-20 h-20 rounded-3xl mx-auto mb-6 flex items-center justify-center" style={{
              backgroundColor: 'var(--error)',
              boxShadow: 'var(--shadow-large)'
            }}>
              <AlertCircle className="h-10 w-10" style={{ color: 'var(--bg-primary)' }} />
            </div>
            <h1 className="text-2xl font-semibold mb-4 macos-title" style={{
              color: 'var(--text-primary)',
              fontWeight: '600',
              letterSpacing: '-0.01em'
            }}>Care Group Not Found</h1>
            <p className="mb-8 text-lg macos-body" style={{
              color: 'var(--text-secondary)',
              fontWeight: '400',
              letterSpacing: '-0.005em'
            }}>The care group you're looking for doesn't exist or you don't have permission to view it.</p>

            <button
              onClick={() => navigate('/care-groups')}
              className="px-8 py-4 rounded-2xl font-medium transition-all duration-300 hover:scale-105 macos-body"
              style={{
                backgroundColor: 'var(--primary)',
                color: 'var(--bg-primary)',
                fontSize: '1.1rem',
                fontWeight: '500',
                letterSpacing: '-0.005em',
                boxShadow: 'var(--shadow-large)'
              }}
            >
              Browse Care Groups
            </button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen" style={{ backgroundColor: 'var(--bg-secondary)' }}>
      <div className="max-w-7xl mx-auto px-8 py-12">
        {/* Header */}
        <div className="rounded-3xl p-12 mb-12" style={{
          backgroundColor: 'var(--bg-primary)',
          border: '1px solid var(--border-light)',
          boxShadow: 'var(--shadow-large)',
          backdropFilter: 'blur(10px)'
        }}>
          <div className="flex justify-between items-start mb-8">
            <div className="flex-1">
              <div className="flex items-center gap-6 mb-6">
                <div className="w-24 h-24 rounded-3xl flex items-center justify-center" style={{
                  backgroundColor: 'var(--primary)',
                  boxShadow: 'var(--shadow-large)'
                }}>
                  <Users className="w-12 h-12" style={{ color: 'var(--bg-primary)' }} />
                </div>
                <div>
                  <h1 className="text-5xl font-semibold mb-4 macos-title" style={{
                    color: 'var(--text-primary)',
                    fontWeight: '600',
                    letterSpacing: '-0.02em',
                    lineHeight: '1.1'
                  }}>{careGroup.name}</h1>
                  <div className="flex items-center gap-6">
                    <span className="inline-flex px-4 py-2 text-sm font-medium rounded-2xl macos-body" style={{
                      backgroundColor: 'var(--primary)',
                      color: 'var(--bg-primary)',
                      fontWeight: '500',
                      letterSpacing: '-0.005em'
                    }}>
                      {careGroup.group_type}
                    </span>
                    <span className="text-lg macos-body" style={{
                      color: 'var(--text-secondary)',
                      fontWeight: '400',
                      letterSpacing: '-0.005em'
                    }}>{careGroup.member_count} members</span>
                    {careGroup.location && (
                      <div className="flex items-center gap-2 text-lg macos-body" style={{
                        color: 'var(--text-secondary)',
                        fontWeight: '400',
                        letterSpacing: '-0.005em'
                      }}>
                        <MapPin className="w-5 h-5" />
                        {careGroup.location}
                      </div>
                    )}
                  </div>
                </div>
              </div>
              <p className="text-xl mb-6 macos-body" style={{
                color: 'var(--text-primary)',
                fontWeight: '400',
                letterSpacing: '-0.005em',
                lineHeight: '1.5'
              }}>{careGroup.description}</p>
              {careGroup.tags.length > 0 && (
                <div className="flex flex-wrap gap-3">
                  {careGroup.tags.map((tag, index) => (
                    <span key={index} className="px-4 py-2 text-sm rounded-2xl macos-body" style={{
                      backgroundColor: 'var(--bg-secondary)',
                      color: 'var(--text-secondary)',
                      fontWeight: '400',
                      letterSpacing: '-0.005em',
                      border: '1px solid var(--border-light)'
                    }}>
                      {tag}
                    </span>
                  ))}
                </div>
              )}
            </div>
            
            {user && !userMembership && (
              <button
                onClick={joinCareGroup}
                disabled={processing || careGroup.member_count >= careGroup.max_members}
                className="bg-logo-green text-white px-6 py-3 rounded-lg hover:bg-green-600 transition-colors flex items-center gap-2 disabled:opacity-50"
              >
                <UserPlus className="w-4 h-4" />
                {careGroup.member_count >= careGroup.max_members ? 'Group Full' : 'Join Group'}
              </button>
            )}
          </div>
        </div>

        {/* Tabs */}
        <div className="bg-white rounded-lg shadow-sm mb-6">
          <div className="border-b" style={{borderColor: 'var(--border-light)'}}>
            <nav className="flex space-x-8 px-6">
              {[
                { id: 'overview', name: 'Overview', icon: Users },
                { id: 'members', name: 'Members', icon: Users },
                { id: 'activity', name: 'Activity', icon: MessageSquare },
                { id: 'chat', name: 'Group Chat', icon: MessageSquare },
                { id: 'calendar', name: 'Calendar', icon: Calendar },
                { id: 'tasks', name: 'Care Tasks', icon: CheckCircle }
              ].map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className="flex items-center gap-2 py-4 border-b-2 font-medium text-sm transition-colors"
                  style={{
                    borderColor: activeTab === tab.id ? 'var(--primary)' : 'transparent',
                    color: activeTab === tab.id ? 'var(--primary)' : 'var(--text-secondary)'
                  }}
                >
                  <tab.icon className="w-4 h-4" />
                  {tab.name}
                </button>
              ))}
            </nav>
          </div>
        </div>

        {/* Tab Content */}
        {activeTab === 'overview' && (
          <div className="bg-white rounded-lg shadow-sm p-6">
            <h3 className="text-lg font-semibold mb-4" style={{color: 'var(--text-primary)'}}>Group Information</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div>
                <span className="text-sm font-medium" style={{color: 'var(--text-secondary)'}}>Type</span>
                <div className="mt-1">
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getGroupTypeColor(careGroup.group_type)}`}>
                    {careGroup.group_type}
                  </span>
                </div>
              </div>
              <div>
                          <span className="text-sm font-medium" style={{color: 'var(--text-secondary)'}}>Members</span>
          <div className="mt-1 text-lg font-semibold" style={{color: 'var(--text-primary)'}}>
                  {careGroup.member_count}/{careGroup.max_members}
                </div>
              </div>
              <div>
                          <span className="text-sm font-medium" style={{color: 'var(--text-secondary)'}}>Created</span>
          <div className="mt-1" style={{color: 'var(--text-primary)'}}>
                  {new Date(careGroup.created_at).toLocaleDateString()}
                </div>
              </div>
            </div>

            {/* Quick Actions */}
            <div className="mt-8 bg-white rounded-lg shadow-sm p-6">
              <h3 className="text-lg font-semibold mb-4" style={{color: 'var(--text-primary)'}}>Quick Actions</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <button
                  onClick={() => navigate('/care-groups')}
                  className="p-4 rounded-lg transition-colors text-left"
                  style={{backgroundColor: 'var(--bg-accent)'}}
                  onMouseEnter={(e) => e.currentTarget.style.backgroundColor = 'var(--bg-primary)'}
                  onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'var(--bg-accent)'}
                >
                  <Users className="w-6 h-6 text-logo-green mb-2" />
                  <div className="font-medium" style={{color: 'var(--text-primary)'}}>Browse Groups</div>
                  <div className="text-sm" style={{color: 'var(--text-secondary)'}}>Explore other care groups</div>
                </button>
                <button
                  onClick={() => navigate('/dashboard')}
                  className="p-4 rounded-lg transition-colors text-left"
                  style={{backgroundColor: 'var(--bg-accent)'}}
                  onMouseEnter={(e) => e.currentTarget.style.backgroundColor = 'var(--bg-primary)'}
                  onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'var(--bg-accent)'}
                >
                  <Settings className="w-6 h-6 text-logo-green mb-2" />
                  <div className="font-medium" style={{color: 'var(--text-primary)'}}>Dashboard</div>
                  <div className="text-sm" style={{color: 'var(--text-secondary)'}}>Return to main dashboard</div>
                </button>
                <button
                  onClick={() => navigate('/messages')}
                  className="p-4 rounded-lg transition-colors text-left"
                  style={{backgroundColor: 'var(--bg-accent)'}}
                  onMouseEnter={(e) => e.currentTarget.style.backgroundColor = 'var(--bg-primary)'}
                  onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'var(--bg-accent)'}
                >
                  <MessageSquare className="w-6 h-6 text-logo-green mb-2" />
                  <div className="font-medium" style={{color: 'var(--text-primary)'}}>Messaging</div>
                  <div className="text-sm" style={{color: 'var(--text-secondary)'}}>Send secure messages</div>
                </button>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'members' && (
          <div className="bg-white rounded-lg shadow-sm p-6">
            <h3 className="text-lg font-semibold mb-6" style={{color: 'var(--text-primary)'}}>
              Members ({careGroup.members.length})
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {careGroup.members.map((member) => (
                <div key={member.user_id} className="border rounded-lg p-4" style={{borderColor: 'var(--border-light)'}}>
                  <div className="flex items-center gap-3 mb-3">
                    {member.avatar_url ? (
                      <img 
                        src={member.avatar_url} 
                        alt={member.full_name}
                        className="w-12 h-12 rounded-full object-cover"
                      />
                    ) : (
                            <div className="w-12 h-12 rounded-full flex items-center justify-center" style={{backgroundColor: 'var(--bg-accent)'}}>
        <Users className="w-6 h-6" style={{color: 'var(--text-secondary)'}} />
                      </div>
                    )}
                    <div>
                      <div className="font-medium" style={{color: 'var(--text-primary)'}}>{member.full_name}</div>
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getRoleColor(member.role)}`}>
                        {member.role}
                      </span>
                    </div>
                  </div>
                  <div className="text-sm" style={{color: 'var(--text-secondary)'}}>
                    Joined {new Date(member.joined_at).toLocaleDateString()}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {activeTab === 'activity' && (
          <div className="bg-white rounded-lg shadow-sm p-6">
            <h3 className="text-lg font-semibold mb-6" style={{color: 'var(--text-primary)'}}>Recent Activity</h3>
            {careGroup.recent_activity.length > 0 ? (
              <div className="space-y-4">
                {careGroup.recent_activity.map((activity) => (
                  <div key={activity.id} className="flex items-start gap-3 p-4 border rounded-lg" style={{borderColor: 'var(--border-light)'}}>
                    <div className="p-2 rounded-full" style={{backgroundColor: 'var(--bg-accent)'}}>
                      <MessageSquare className="w-4 h-4" />
                    </div>
                    <div className="flex-1">
                      <div className="text-sm" style={{color: 'var(--text-primary)'}}>
                        <span className="font-medium">{activity.user_name}</span>
                        {activity.type === 'message' && ' posted a message'}
                        {activity.type === 'member_join' && ' joined the group'}
                      </div>
                      {activity.content && (
                        <div className="mt-1" style={{color: 'var(--text-secondary)'}}>{activity.content}</div>
                      )}
                      <div className="text-xs mt-1" style={{color: 'var(--text-secondary)'}}>
                        {new Date(activity.created_at).toLocaleDateString()}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-center py-8" style={{color: 'var(--text-secondary)'}}>No recent activity</p>
            )}
          </div>
        )}

        {/* Group Chat Tab */}
        {activeTab === 'chat' && (
          <div style={{ backgroundColor: 'var(--bg-primary)', borderRadius: '0.5rem', boxShadow: 'var(--shadow-card)', padding: '1.5rem' }}>
            <h3 style={{ fontSize: '1.125rem', fontWeight: '600', color: 'var(--text-primary)', marginBottom: '1rem' }}>Group Chat</h3>
            <div style={{
              backgroundColor: 'var(--bg-secondary)',
              borderRadius: '0.5rem',
              padding: '2rem',
              textAlign: 'center',
              border: '1px solid var(--border-light)'
            }}>
              <MessageSquare style={{ width: '3rem', height: '3rem', margin: '0 auto 1rem', color: 'var(--text-muted)' }} />
              <h4 style={{ fontSize: '1.125rem', fontWeight: '600', color: 'var(--text-primary)', marginBottom: '0.5rem' }}>
                Real-time Group Chat
              </h4>
              <p style={{ color: 'var(--text-secondary)', marginBottom: '1.5rem' }}>
                Communicate with all group members in real-time. Share updates, ask questions, and coordinate care.
              </p>
              <button style={{
                backgroundColor: 'var(--primary)',
                color: 'white',
                padding: '0.75rem 1.5rem',
                borderRadius: '0.5rem',
                border: 'none',
                fontWeight: '600',
                cursor: 'pointer'
              }}>
                Start Chatting
              </button>
            </div>
          </div>
        )}

        {/* Calendar Tab */}
        {activeTab === 'calendar' && (
          <div style={{ backgroundColor: 'var(--bg-primary)', borderRadius: '0.5rem', boxShadow: 'var(--shadow-card)', padding: '1.5rem' }}>
            <h3 style={{ fontSize: '1.125rem', fontWeight: '600', color: 'var(--text-primary)', marginBottom: '1rem' }}>Shared Calendar</h3>
            <div style={{
              backgroundColor: 'var(--bg-secondary)',
              borderRadius: '0.5rem',
              padding: '2rem',
              textAlign: 'center',
              border: '1px solid var(--border-light)'
            }}>
              <Calendar style={{ width: '3rem', height: '3rem', margin: '0 auto 1rem', color: 'var(--text-muted)' }} />
              <h4 style={{ fontSize: '1.125rem', fontWeight: '600', color: 'var(--text-primary)', marginBottom: '0.5rem' }}>
                Group Calendar & Events
              </h4>
              <p style={{ color: 'var(--text-secondary)', marginBottom: '1.5rem' }}>
                Coordinate appointments, medication schedules, and care activities. Everyone stays informed.
              </p>
              <div style={{ display: 'flex', gap: '0.5rem', justifyContent: 'center' }}>
                <button style={{
                  backgroundColor: 'var(--primary)',
                  color: 'white',
                  padding: '0.75rem 1.5rem',
                  borderRadius: '0.5rem',
                  border: 'none',
                  fontWeight: '600',
                  cursor: 'pointer'
                }}>
                  View Calendar
                </button>
                <button style={{
                  backgroundColor: 'var(--bg-accent)',
                  color: 'var(--primary)',
                  padding: '0.75rem 1.5rem',
                  borderRadius: '0.5rem',
                  border: '1px solid var(--primary)',
                  fontWeight: '600',
                  cursor: 'pointer'
                }}>
                  Add Event
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Care Tasks Tab */}
        {activeTab === 'tasks' && (
          <div style={{ backgroundColor: 'var(--bg-primary)', borderRadius: '0.5rem', boxShadow: 'var(--shadow-card)', padding: '1.5rem' }}>
            <h3 style={{ fontSize: '1.125rem', fontWeight: '600', color: 'var(--text-primary)', marginBottom: '1rem' }}>Care Tasks & Coordination</h3>
            <div style={{
              backgroundColor: 'var(--bg-secondary)',
              borderRadius: '0.5rem',
              padding: '2rem',
              textAlign: 'center',
              border: '1px solid var(--border-light)'
            }}>
              <CheckCircle style={{ width: '3rem', height: '3rem', margin: '0 auto 1rem', color: 'var(--text-muted)' }} />
              <h4 style={{ fontSize: '1.125rem', fontWeight: '600', color: 'var(--text-primary)', marginBottom: '0.5rem' }}>
                Collaborative Care Management
              </h4>
              <p style={{ color: 'var(--text-secondary)', marginBottom: '1.5rem' }}>
                Assign tasks, track medication schedules, coordinate appointments, and share care updates with the team.
              </p>
              <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))', gap: '0.5rem' }}>
                <button style={{
                  backgroundColor: 'var(--primary)',
                  color: 'white',
                  padding: '0.75rem 1rem',
                  borderRadius: '0.5rem',
                  border: 'none',
                  fontWeight: '600',
                  cursor: 'pointer',
                  fontSize: '0.875rem'
                }}>
                  Medication Tracker
                </button>
                <button style={{
                  backgroundColor: 'var(--bg-accent)',
                  color: 'var(--primary)',
                  padding: '0.75rem 1rem',
                  borderRadius: '0.5rem',
                  border: '1px solid var(--primary)',
                  fontWeight: '600',
                  cursor: 'pointer',
                  fontSize: '0.875rem'
                }}>
                  Care Plan
                </button>
                <button style={{
                  backgroundColor: 'var(--bg-accent)',
                  color: 'var(--primary)',
                  padding: '0.75rem 1rem',
                  borderRadius: '0.5rem',
                  border: '1px solid var(--primary)',
                  fontWeight: '600',
                  cursor: 'pointer',
                  fontSize: '0.875rem'
                }}>
                  Task Assignment
                </button>
              </div>
            </div>
          </div>
        )}

      </div>
    </div>
  )
}

export default CareGroupDetailPage
