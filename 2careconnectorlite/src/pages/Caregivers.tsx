import React, { useState, useEffect, useMemo, useCallback } from 'react'
import { useNavigate, useSearchParams } from 'react-router-dom'
import { dataService } from '../lib/dataService'

interface Caregiver {
  id: string
  email: string
  full_name: string
  role: string
  created_at: string
  updated_at: string
  bio?: string
  average_rating?: number
  years_of_experience?: number
  hourly_rate?: number
}

export default function Caregivers() {
  console.log('🚀 CAREGIVERS COMPONENT STARTING TO RENDER')
  
  const navigate = useNavigate()
  const [searchParams] = useSearchParams()
  const searchQuery = searchParams.get('search') || ''
  const [caregivers, setCaregivers] = useState<Caregiver[]>([])
  const [allCaregivers, setAllCaregivers] = useState<Caregiver[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [sortBy, setSortBy] = useState('name')
  const [filterBy, setFilterBy] = useState('all')
  const [currentPage, setCurrentPage] = useState(1)
  const [itemsPerPage] = useState(12)

  // Debounced search with useMemo for performance
  const filteredAndSortedCaregivers = useMemo(() => {
    let filtered = allCaregivers

    // Apply search filtering
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase()
      filtered = allCaregivers.filter(caregiver =>
        caregiver.full_name?.toLowerCase().includes(query) ||
        caregiver.email?.toLowerCase().includes(query) ||
        caregiver.bio?.toLowerCase().includes(query)
      )
    }

    // Apply role filtering
    if (filterBy !== 'all') {
      filtered = filtered.filter(caregiver => caregiver.role === filterBy)
    }

    // Apply sorting
    const sorted = [...filtered].sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return (a.full_name || '').localeCompare(b.full_name || '')
        case 'rating':
          return (b.average_rating || 0) - (a.average_rating || 0)
        case 'experience':
          return (b.years_of_experience || 0) - (a.years_of_experience || 0)
        case 'price':
          return (a.hourly_rate || 0) - (b.hourly_rate || 0)
        default:
          return 0
      }
    })

    return sorted
  }, [allCaregivers, searchQuery, filterBy, sortBy])

  // Pagination logic
  const totalPages = Math.ceil(filteredAndSortedCaregivers.length / itemsPerPage)
  const paginatedCaregivers = useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPage
    return filteredAndSortedCaregivers.slice(startIndex, startIndex + itemsPerPage)
  }, [filteredAndSortedCaregivers, currentPage, itemsPerPage])

  useEffect(() => {
    console.log('🔍 CAREGIVERS COMPONENT MOUNTED - useEffect triggered')

    const fetchCaregivers = async () => {
      try {
        setLoading(true)
        console.log('📡 Using dataService to fetch caregivers (HOLY RULE #1 compliant)...')

        // Use dataService which already filters out mock data per HOLY RULE #1
        const caregiverData = await dataService.getCaregivers()

        console.log('✅ Caregivers loaded via dataService:', caregiverData.length, 'results')
        console.log('🛡️ HOLY RULE #1 COMPLIANCE: All mock data filtered out by dataService')

        setAllCaregivers(caregiverData)
        setError(null)
      } catch (error) {
        console.error('❌ Error fetching caregivers:', error)
        setError('Failed to load caregivers. Please try again.')
        // Show empty state on error - complies with HOLY RULE #1
        setAllCaregivers([])
      } finally {
        setLoading(false)
      }
    }

    fetchCaregivers()
  }, [])

  // Update caregivers when filtered results change
  useEffect(() => {
    setCaregivers(paginatedCaregivers)
  }, [paginatedCaregivers])

  if (loading) {
    return (
      <div style={{
        minHeight: '100vh',
        backgroundColor: 'var(--bg-primary)',
        padding: '2rem'
      }}>
        {/* Header Skeleton */}
        <div className="text-center mb-8 sm:mb-12">
          <div className="h-12 w-64 mx-auto rounded-lg animate-pulse" style={{ backgroundColor: 'var(--border-light)' }}></div>
          <div className="h-6 w-48 mx-auto mt-4 rounded-lg animate-pulse" style={{ backgroundColor: 'var(--border-light)' }}></div>
        </div>

        {/* Search Filters Skeleton */}
        <div className="mb-8 sm:mb-12">
          <div className="max-w-4xl mx-auto">
            <div className="p-4 sm:p-6 md:p-8 rounded-2xl sm:rounded-3xl" style={{
              backgroundColor: 'var(--bg-primary)',
              border: '1px solid var(--border-light)',
              boxShadow: 'var(--shadow-large)'
            }}>
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
                {[1, 2, 3].map(i => (
                  <div key={i}>
                    <div className="h-4 w-20 mb-3 rounded animate-pulse" style={{ backgroundColor: 'var(--border-light)' }}></div>
                    <div className="h-12 w-full rounded-xl animate-pulse" style={{ backgroundColor: 'var(--border-light)' }}></div>
                  </div>
                ))}
              </div>
              <div className="mt-6 text-center">
                <div className="h-12 w-40 mx-auto rounded-xl animate-pulse" style={{ backgroundColor: 'var(--border-light)' }}></div>
              </div>
            </div>
          </div>
        </div>

        {/* Results Skeleton */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 lg:gap-8">
          {[1, 2, 3, 4, 5, 6].map(i => (
            <div key={i} className="rounded-2xl sm:rounded-3xl p-4 sm:p-6 md:p-8" style={{
              backgroundColor: 'var(--bg-primary)',
              border: '1px solid var(--border-light)',
              boxShadow: 'var(--shadow-card)'
            }}>
              <div className="text-center">
                <div className="w-16 h-16 mx-auto rounded-full animate-pulse mb-4" style={{ backgroundColor: 'var(--border-light)' }}></div>
                <div className="h-6 w-32 mx-auto rounded animate-pulse mb-2" style={{ backgroundColor: 'var(--border-light)' }}></div>
                <div className="h-4 w-24 mx-auto rounded animate-pulse mb-4" style={{ backgroundColor: 'var(--border-light)' }}></div>
                <div className="h-4 w-full rounded animate-pulse mb-2" style={{ backgroundColor: 'var(--border-light)' }}></div>
                <div className="h-4 w-3/4 mx-auto rounded animate-pulse" style={{ backgroundColor: 'var(--border-light)' }}></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div style={{
        minHeight: '100vh',
        backgroundColor: 'var(--bg-primary)',
        padding: '2rem',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }}>
        <div className="max-w-md mx-auto p-8 rounded-3xl text-center" style={{
          backgroundColor: 'var(--bg-primary)',
          border: '1px solid var(--border-light)',
          boxShadow: 'var(--shadow-card)'
        }}>
          <div className="w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center" style={{
            backgroundColor: 'var(--status-error-bg)',
            color: 'var(--status-error-text)'
          }}>
            ⚠️
          </div>
          <h2 className="text-xl font-semibold mb-4" style={{ color: 'var(--text-primary)' }}>
            Unable to Load Caregivers
          </h2>
          <p className="text-base mb-6" style={{ color: 'var(--text-secondary)' }}>
            {error}
          </p>
          <button
            onClick={() => window.location.reload()}
            className="px-6 py-3 rounded-xl font-medium transition-all duration-200 hover:shadow-lg"
            style={{
              backgroundColor: 'var(--primary)',
              color: 'var(--bg-primary)',
              border: 'none'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.transform = 'translateY(-2px)'
              e.currentTarget.style.boxShadow = 'var(--shadow-hover)'
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.transform = 'translateY(0)'
              e.currentTarget.style.boxShadow = 'none'
            }}
          >
            Try Again
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen px-4 sm:px-6 md:px-8 py-8 sm:py-12 md:py-16" style={{
      backgroundColor: 'var(--bg-secondary)'
    }}>
      <div className="max-w-7xl mx-auto">
        {/* Apple Mac Desktop Header - Mobile Optimized */}
        <div className="text-center mb-12 sm:mb-16">
          <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-light mb-4 sm:mb-6 macos-title" style={{
            color: 'var(--text-primary)',
            fontWeight: '300',
            letterSpacing: '-0.025em',
            lineHeight: '1.1'
          }}>
            Care Providers
          </h1>
          <p className="text-lg sm:text-xl text-center mb-6 sm:mb-8 px-4 macos-subtitle" style={{
            color: 'var(--text-secondary)',
            maxWidth: '600px',
            margin: '0 auto',
            lineHeight: '1.4'
          }}>
            Find verified healthcare professionals in your area
          </p>
          <div className="inline-flex items-center gap-3 px-4 sm:px-6 py-2 sm:py-3 rounded-full" style={{
            backgroundColor: 'var(--bg-primary)',
            border: '1px solid var(--border-light)',
            boxShadow: 'var(--shadow-card)'
          }}>
            <span className="text-base sm:text-lg font-medium macos-body" style={{ color: 'var(--text-primary)' }}>
              {caregivers.length} providers available
            </span>
          </div>
        </div>
      
        {/* Apple Mac Desktop Search and Filter Section - Mobile Optimized */}
        <div className="mb-8 sm:mb-12">
          <div className="max-w-4xl mx-auto">
            <div className="p-4 sm:p-6 md:p-8 rounded-2xl sm:rounded-3xl" style={{
              backgroundColor: 'var(--bg-primary)',
              border: '1px solid var(--border-light)',
              boxShadow: 'var(--shadow-large)',
              backdropFilter: 'blur(10px)'
            }}>
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
                <div>
                  <label
                    htmlFor="location-input"
                    className="block text-sm font-medium mb-2 sm:mb-3 macos-body"
                    style={{ color: 'var(--text-primary)' }}
                  >
                    Location
                  </label>
                  <input
                    id="location-input"
                    type="text"
                    placeholder="Enter city or zip code"
                    className="w-full px-3 sm:px-4 py-2 sm:py-3 rounded-xl sm:rounded-2xl border transition-all duration-300 macos-body text-sm sm:text-base focus:outline-none focus:ring-2 focus:ring-offset-2"
                    style={{
                      backgroundColor: 'var(--bg-secondary)',
                      borderColor: 'var(--border-light)',
                      color: 'var(--text-primary)'
                    }}
                    onFocus={(e) => {
                      e.currentTarget.style.borderColor = 'var(--primary)'
                      e.currentTarget.style.boxShadow = '0 0 0 3px rgba(var(--primary-rgb), 0.1)'
                    }}
                    onBlur={(e) => {
                      e.currentTarget.style.borderColor = 'var(--border-light)'
                      e.currentTarget.style.boxShadow = 'none'
                    }}
                    aria-describedby="location-help"
                  />
                  <div id="location-help" className="sr-only">
                    Enter a city name or zip code to filter caregivers by location
                  </div>
                </div>
                <div>
                  <label
                    htmlFor="specialty-select"
                    className="block text-sm font-medium mb-2 sm:mb-3 macos-body"
                    style={{ color: 'var(--text-primary)' }}
                  >
                    Specialty
                  </label>
                  <select
                    id="specialty-select"
                    className="w-full px-3 sm:px-4 py-2 sm:py-3 rounded-xl sm:rounded-2xl border transition-all duration-300 macos-body text-sm sm:text-base focus:outline-none focus:ring-2 focus:ring-offset-2"
                    style={{
                      backgroundColor: 'var(--bg-secondary)',
                      borderColor: 'var(--border-light)',
                      color: 'var(--text-primary)'
                    }}
                    onFocus={(e) => {
                      e.currentTarget.style.borderColor = 'var(--primary)'
                      e.currentTarget.style.boxShadow = '0 0 0 3px rgba(var(--primary-rgb), 0.1)'
                    }}
                    onBlur={(e) => {
                      e.currentTarget.style.borderColor = 'var(--border-light)'
                      e.currentTarget.style.boxShadow = 'none'
                    }}
                    aria-describedby="specialty-help"
                  >
                    <option value="">All Specialties</option>
                    <option value="senior-care">Senior Care</option>
                    <option value="child-care">Child Care</option>
                    <option value="medical-care">Medical Care</option>
                    <option value="companion-care">Companion Care</option>
                    <option value="respite-care">Respite Care</option>
                  </select>
                  <div id="specialty-help" className="sr-only">
                    Select a specialty to filter caregivers by their area of expertise
                  </div>
                </div>
                <div className="sm:col-span-2 lg:col-span-1">
                  <label
                    htmlFor="availability-select"
                    className="block text-sm font-medium mb-2 sm:mb-3 macos-body"
                    style={{ color: 'var(--text-primary)' }}
                  >
                    Availability
                  </label>
                  <select
                    id="availability-select"
                    className="w-full px-3 sm:px-4 py-2 sm:py-3 rounded-xl sm:rounded-2xl border transition-all duration-300 macos-body text-sm sm:text-base focus:outline-none focus:ring-2 focus:ring-offset-2"
                    style={{
                      backgroundColor: 'var(--bg-secondary)',
                      borderColor: 'var(--border-light)',
                      color: 'var(--text-primary)'
                    }}
                    onFocus={(e) => {
                      e.currentTarget.style.borderColor = 'var(--primary)'
                      e.currentTarget.style.boxShadow = '0 0 0 3px rgba(var(--primary-rgb), 0.1)'
                    }}
                    onBlur={(e) => {
                      e.currentTarget.style.borderColor = 'var(--border-light)'
                      e.currentTarget.style.boxShadow = 'none'
                    }}
                    aria-describedby="availability-help"
                  >
                    <option value="">Any Time</option>
                    <option value="weekdays">Weekdays</option>
                    <option value="weekends">Weekends</option>
                    <option value="evenings">Evenings</option>
                    <option value="nights">Nights</option>
                    <option value="24-7">24/7 Available</option>
                  </select>
                  <div id="availability-help" className="sr-only">
                    Select availability preference to filter caregivers by their schedule
                  </div>
                </div>
              </div>
              <div className="mt-4 sm:mt-6 text-center">
                <button
                  className="btn-primary w-full sm:w-auto px-6 sm:px-8 py-3 sm:py-4 rounded-xl sm:rounded-2xl font-medium transition-all duration-300 shadow-lg hover:shadow-xl text-sm sm:text-base"
                  style={{
                    fontWeight: '500',
                    letterSpacing: '-0.005em'
                  }}
                >
                  Search Providers
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Sorting and Results Count Section */}
        <div className="mb-6 sm:mb-8">
          <div className="max-w-4xl mx-auto">
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 p-4 rounded-xl" style={{
              backgroundColor: 'var(--bg-primary)',
              border: '1px solid var(--border-light)',
              boxShadow: 'var(--shadow-light)'
            }}>
              <div className="flex items-center gap-4">
                <span className="text-sm font-medium" style={{ color: 'var(--text-primary)' }}>
                  {filteredAndSortedCaregivers.length} provider{filteredAndSortedCaregivers.length !== 1 ? 's' : ''} found
                </span>
                {searchQuery && (
                  <span className="text-xs px-2 py-1 rounded-full" style={{
                    backgroundColor: 'var(--primary)',
                    color: 'var(--bg-primary)'
                  }}>
                    "{searchQuery}"
                  </span>
                )}
              </div>

              <div className="flex items-center gap-4">
                <label htmlFor="sort-select" className="text-sm font-medium" style={{ color: 'var(--text-primary)' }}>
                  Sort by:
                </label>
                <select
                  id="sort-select"
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value)}
                  className="px-3 py-2 rounded-lg border text-sm"
                  style={{
                    backgroundColor: 'var(--bg-primary)',
                    borderColor: 'var(--border-medium)',
                    color: 'var(--text-primary)'
                  }}
                  aria-label="Sort caregivers by"
                >
                  <option value="name">Name</option>
                  <option value="rating">Rating</option>
                  <option value="experience">Experience</option>
                  <option value="price">Price</option>
                </select>

                <label htmlFor="filter-select" className="text-sm font-medium" style={{ color: 'var(--text-primary)' }}>
                  Filter:
                </label>
                <select
                  id="filter-select"
                  value={filterBy}
                  onChange={(e) => setFilterBy(e.target.value)}
                  className="px-3 py-2 rounded-lg border text-sm"
                  style={{
                    backgroundColor: 'var(--bg-primary)',
                    borderColor: 'var(--border-medium)',
                    color: 'var(--text-primary)'
                  }}
                  aria-label="Filter caregivers by role"
                >
                  <option value="all">All Roles</option>
                  <option value="caregiver">Caregivers</option>
                  <option value="companion">Companions</option>
                  <option value="care_checker">Care Checkers</option>
                </select>
              </div>
            </div>
          </div>
        </div>

        {paginatedCaregivers.length === 0 ? (
          <div className="text-center py-16">
            <div className="max-w-md mx-auto p-8 rounded-3xl" style={{
              backgroundColor: 'var(--bg-primary)',
              border: '1px solid var(--border-light)',
              boxShadow: 'var(--shadow-card)'
            }}>
              <h3 className="text-xl font-semibold mb-4 macos-title" style={{ color: 'var(--text-primary)' }}>
                No caregivers found
              </h3>
              <p className="text-base macos-body" style={{ color: 'var(--text-secondary)' }}>
                Try adjusting your search criteria or check back later for new providers.
              </p>
            </div>
          </div>
        ) : (
          <>
            <section
              className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 lg:gap-8"
              role="region"
              aria-label="Search results"
              aria-live="polite"
              aria-describedby="results-summary"
            >
              <div id="results-summary" className="sr-only">
                Showing {paginatedCaregivers.length} of {filteredAndSortedCaregivers.length} caregivers on page {currentPage} of {totalPages}
              </div>
              {paginatedCaregivers.map((caregiver) => (
              <article
                key={caregiver.id}
                className="rounded-2xl sm:rounded-3xl p-4 sm:p-6 md:p-8 transition-all duration-500 cursor-pointer group"
                style={{
                  backgroundColor: 'var(--bg-primary)',
                  border: '1px solid var(--border-light)',
                  boxShadow: 'var(--shadow-card)',
                  backdropFilter: 'blur(10px)'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.transform = 'translateY(-2px)'
                  e.currentTarget.style.boxShadow = 'var(--shadow-card-hover)'
                  e.currentTarget.style.borderColor = 'var(--primary)'
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.transform = 'translateY(0)'
                  e.currentTarget.style.boxShadow = 'var(--shadow-card)'
                  e.currentTarget.style.borderColor = 'var(--border-light)'
                }}
                role="button"
                tabIndex={0}
                aria-label={`View profile for ${caregiver.full_name || 'caregiver'}`}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault()
                    navigate(`/caregiver/${caregiver.id}`)
                  }
                }}
                onClick={() => navigate(`/provider/caregiver/${caregiver.id}`)}
              >
                {/* Apple Mac Desktop Caregiver Card Content - Mobile Optimized */}
                <div className="text-center">
                  <div className="w-16 sm:w-20 h-16 sm:h-20 rounded-2xl sm:rounded-3xl mx-auto mb-4 sm:mb-6 flex items-center justify-center shadow-lg" style={{
                    backgroundColor: 'var(--primary)',
                    background: 'linear-gradient(135deg, var(--primary), var(--primary))'
                  }}>
                    <span className="text-lg sm:text-2xl font-bold" style={{ color: 'var(--bg-primary)' }}>
                      {(caregiver.full_name || 'UC').split(' ').map(n => n[0]).join('').toUpperCase()}
                    </span>
                  </div>

                  <h3 className="text-lg sm:text-xl font-semibold mb-2 sm:mb-3 macos-title" style={{
                    color: 'var(--text-primary)',
                    fontWeight: '600',
                    letterSpacing: '-0.01em'
                  }}>
                    {caregiver.full_name || 'Unnamed Caregiver'}
                  </h3>

                  <div className="flex items-center justify-center gap-2 mb-3 sm:mb-4 px-3 sm:px-4 py-1 sm:py-2 rounded-full" style={{
                    backgroundColor: 'var(--bg-secondary)',
                    border: '1px solid var(--border-light)'
                  }}>
                    <div className="w-2 h-2 rounded-full" style={{ backgroundColor: 'var(--primary)' }}></div>
                    <span className="text-xs sm:text-sm font-medium macos-body" style={{ color: 'var(--text-secondary)' }}>
                      Available Now
                    </span>
                  </div>

                  <p className="text-xs sm:text-sm macos-body mb-3 sm:mb-4 truncate" style={{ color: 'var(--text-secondary)' }}>
                    {caregiver.email}
                  </p>

                  <div className="flex items-center justify-center gap-2 sm:gap-4 text-xs macos-caption" style={{ color: 'var(--text-secondary)' }}>
                    <span className="truncate">Joined {new Date(caregiver.created_at).toLocaleDateString()}</span>
                    <span>•</span>
                    <span>Verified</span>
                  </div>
                </div>
              </article>
              ))}
            </section>

            {/* Pagination Controls */}
            {totalPages > 1 && (
              <div className="mt-8 sm:mt-12">
                <div className="max-w-4xl mx-auto">
                  <nav
                    className="flex justify-center items-center gap-2 p-4 rounded-xl"
                    style={{
                      backgroundColor: 'var(--bg-primary)',
                      border: '1px solid var(--border-light)',
                      boxShadow: 'var(--shadow-light)'
                    }}
                    role="navigation"
                    aria-label="Pagination navigation"
                  >
                    <button
                      onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                      disabled={currentPage === 1}
                      className="px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                      style={{
                        backgroundColor: currentPage === 1 ? 'transparent' : 'var(--bg-secondary)',
                        color: 'var(--text-primary)',
                        border: '1px solid var(--border-light)'
                      }}
                      aria-label="Go to previous page"
                    >
                      Previous
                    </button>

                    <div className="flex items-center gap-1">
                      {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                        const pageNum = i + 1
                        const isActive = pageNum === currentPage
                        return (
                          <button
                            key={pageNum}
                            onClick={() => setCurrentPage(pageNum)}
                            className="w-10 h-10 rounded-lg text-sm font-medium transition-all duration-200"
                            style={{
                              backgroundColor: isActive ? 'var(--primary)' : 'transparent',
                              color: isActive ? 'var(--bg-primary)' : 'var(--text-primary)',
                              border: '1px solid var(--border-light)'
                            }}
                            aria-label={`Go to page ${pageNum}`}
                            aria-current={isActive ? 'page' : undefined}
                          >
                            {pageNum}
                          </button>
                        )
                      })}
                    </div>

                    <button
                      onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                      disabled={currentPage === totalPages}
                      className="px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                      style={{
                        backgroundColor: currentPage === totalPages ? 'transparent' : 'var(--bg-secondary)',
                        color: 'var(--text-primary)',
                        border: '1px solid var(--border-light)'
                      }}
                      aria-label="Go to next page"
                    >
                      Next
                    </button>
                  </nav>

                  <div className="text-center mt-4">
                    <span className="text-sm" style={{ color: 'var(--text-secondary)' }}>
                      Page {currentPage} of {totalPages} • Showing {paginatedCaregivers.length} of {filteredAndSortedCaregivers.length} providers
                    </span>
                  </div>
                </div>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  )
}
