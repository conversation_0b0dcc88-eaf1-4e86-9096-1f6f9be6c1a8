import React, { useEffect, useState } from 'react';
import { Navigate } from 'react-router-dom';
import { supabase } from '../lib/supabase';

interface UserProfile {
  id: string;
  is_admin?: boolean;
  first_name?: string;
  last_name?: string;
}

interface PlatformSettings {
  id: number;
  platform_name: string | null;
  platform_description: string | null;
  maintenance_mode: boolean | null;
  registration_enabled: boolean | null;
  max_users: number | null;
  created_at: string;
  updated_at: string;
}

const AdminSettings: React.FC = () => {
  const [user, setUser] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [profileLoading, setProfileLoading] = useState(true);
  const [profileError, setProfileError] = useState<string | null>(null);

  const [platformName, setPlatformName] = useState('');
  const [platformDescription, setPlatformDescription] = useState('');
  const [maintenanceMode, setMaintenanceMode] = useState(false);
  const [registrationEnabled, setRegistrationEnabled] = useState(true);
  const [maxUsers, setMaxUsers] = useState(10000);

  const [isSaving, setIsSaving] = useState(false);
  const [settingsLoading, setSettingsLoading] = useState(true);
  const [settingsError, setSettingsError] = useState<string | null>(null);
  const [saveMessage, setSaveMessage] = useState<string | null>(null);

  // Check authentication
  useEffect(() => {
    const checkAuth = async () => {
      const { data: { session } } = await supabase.auth.getSession();
      setUser(session?.user || null);
      setLoading(false);
    };
    checkAuth();
  }, []);

  // Fetch user profile to check admin status
  useEffect(() => {
    const loadProfile = async () => {
      if (!user) {
        setProfileLoading(false);
        return;
      }
      setProfileLoading(true);
      setProfileError(null);
      
      try {
        const { data, error } = await supabase
          .schema('care_connector')
          .from('profiles')
          .select('*')
          .eq('id', user.id)
          .single();

        if (error) throw error;
        setProfile(data);
      } catch (err: any) {
        setProfileError('Failed to fetch your admin profile. Please try again.');
      } finally {
        setProfileLoading(false);
      }
    };

    if (!loading) {
      loadProfile();
    }
  }, [user, loading]);

  // Fetch platform settings
  useEffect(() => {
    const fetchSettings = async () => {
      if (!user || !profile?.is_admin) return;
      
      setSettingsLoading(true);
      setSettingsError(null);
      
      try {
        // Check if platform_settings table exists and fetch settings
        const { data, error } = await supabase
          .schema('care_connector')
          .from('platform_settings')
          .select('*')
          .single();

        if (error) {
          // If table doesn't exist or no data, set defaults
          if (error.code === 'PGRST116' || error.message.includes('relation') || error.message.includes('does not exist')) {
            // Set default values
            setPlatformName('CareConnectorLite');
            setPlatformDescription('Modern caregiving platform connecting families and care providers');
            setMaintenanceMode(false);
            setRegistrationEnabled(true);
            setMaxUsers(10000);
          } else {
            throw error;
          }
        } else {
          setPlatformName(data.platform_name || 'CareConnectorLite');
          setPlatformDescription(data.platform_description || 'Modern caregiving platform');
          setMaintenanceMode(data.maintenance_mode || false);
          setRegistrationEnabled(data.registration_enabled !== false);
          setMaxUsers(data.max_users || 10000);
        }
      } catch (err: any) {
        console.error('Error fetching platform settings:', err);
        setSettingsError(err.message);
        // Set defaults on error
        setPlatformName('CareConnectorLite');
        setPlatformDescription('Modern caregiving platform connecting families and care providers');
        setMaintenanceMode(false);
        setRegistrationEnabled(true);
        setMaxUsers(10000);
      } finally {
        setSettingsLoading(false);
      }
    };

    if (!loading && !profileLoading && profile?.is_admin) {
      fetchSettings();
    }
  }, [user, loading, profileLoading, profile]);

  const handleSaveSettings = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSaving(true);
    setSaveMessage(null);
    
    try {
      const settingsData = {
        platform_name: platformName.trim() || 'CareConnectorLite',
        platform_description: platformDescription.trim() || 'Modern caregiving platform',
        maintenance_mode: maintenanceMode,
        registration_enabled: registrationEnabled,
        max_users: maxUsers || 10000,
        updated_at: new Date().toISOString()
      };

      // Try to update existing settings
      const { data, error } = await supabase
        .schema('care_connector')
        .from('platform_settings')
        .upsert(settingsData)
        .select();

      if (error) {
        throw error;
      }

      setSaveMessage('Platform settings saved successfully!');
    } catch (err: any) {
      console.error('Error saving platform settings:', err);
      setSaveMessage(`Error saving settings: ${err.message}`);
    } finally {
      setIsSaving(false);
    }
  };

  if (loading || profileLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-lg" style={{color: 'var(--text-secondary)'}}>Loading admin settings...</div>
      </div>
    );
  }

  if (profileError) {
    return (
      <div className="container mx-auto px-4 py-12">
        <div className="text-base text-red-600 leading-relaxed font-medium">{profileError}</div>
      </div>
    );
  }
  
  if (!user || !profile?.is_admin) {
    return <Navigate to="/" />;
  }

  return (
    <div className="container mx-auto px-4 py-12 md:py-16">
      <header className="mb-8">
        <h1 className="text-3xl font-bold flex items-center" style={{color: 'var(--text-primary)'}}>
          <div className="w-8 h-8 mr-3 bg-blue-100 rounded-lg flex items-center justify-center">
            <span className="text-blue-600 text-sm">⚙️</span>
          </div>
          Admin Platform Settings
        </h1>
        <p className="mt-2" style={{ color: 'var(--text-secondary)' }}>
          Configure platform-wide settings and parameters.
        </p>
      </header>

      {settingsLoading ? (
        <div className="space-y-6">
          {[...Array(3)].map((_, i) => (
            <div key={i} className="rounded-lg p-6" style={{ backgroundColor: 'var(--bg-primary)', border: '1px solid var(--border-light)', boxShadow: 'var(--shadow-card)' }}>
              <div className="animate-pulse">
                <div className="h-4 rounded w-1/4 mb-4" style={{ backgroundColor: 'var(--bg-secondary)' }}></div>
                <div className="h-8 rounded w-full mb-2" style={{ backgroundColor: 'var(--bg-secondary)' }}></div>
                <div className="h-3 rounded w-3/4" style={{ backgroundColor: 'var(--bg-secondary)' }}></div>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="rounded-lg" style={{ backgroundColor: 'var(--bg-primary)', border: '1px solid var(--border-light)', boxShadow: 'var(--shadow-card)' }}>
          <div className="p-6" style={{ borderBottom: '1px solid var(--border-light)' }}>
            <h2 className="text-xl font-semibold" style={{ color: 'var(--text-primary)' }}>Platform Configuration</h2>
            <p className="text-sm mt-1" style={{ color: 'var(--text-secondary)' }}>
              Update the platform's core settings and operational parameters.
            </p>
          </div>
          
          <div className="p-6">
            <form onSubmit={handleSaveSettings} className="space-y-6">
              {/* Platform Name */}
              <div>
                <label htmlFor="platformName" className="block text-sm font-medium mb-2" style={{color: 'var(--text-secondary)'}}>
                  Platform Name
                </label>
                <input
                  type="text"
                  id="platformName"
                  value={platformName}
                  onChange={(e) => setPlatformName(e.target.value)}
                  className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" style={{borderColor: 'var(--border-light)'}}
                  placeholder="Enter platform name"
                />
                <p className="text-xs mt-1" style={{color: 'var(--text-secondary)'}}>
                  The name displayed across the platform interface.
                </p>
              </div>

              {/* Platform Description */}
              <div>
                <label htmlFor="platformDescription" className="block text-sm font-medium mb-2" style={{color: 'var(--text-secondary)'}}>
                  Platform Description
                </label>
                <textarea
                  id="platformDescription"
                  value={platformDescription}
                  onChange={(e) => setPlatformDescription(e.target.value)}
                  rows={3}
                  className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" style={{borderColor: 'var(--border-light)'}}
                  placeholder="Enter platform description"
                />
                <p className="text-xs mt-1" style={{color: 'var(--text-secondary)'}}>
                  A brief description of the platform's purpose and services.
                </p>
              </div>

              {/* Maximum Users */}
              <div>
                <label htmlFor="maxUsers" className="block text-sm font-medium mb-2" style={{color: 'var(--text-secondary)'}}>
                  Maximum Users
                </label>
                <input
                  type="number"
                  id="maxUsers"
                  value={maxUsers}
                  onChange={(e) => setMaxUsers(parseInt(e.target.value) || 10000)}
                  min="1"
                  max="1000000"
                  className="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500" style={{borderColor: 'var(--border-light)'}}
                />
                <p className="text-xs mt-1" style={{color: 'var(--text-secondary)'}}>
                  Maximum number of users allowed to register on the platform.
                </p>
              </div>

              {/* System Toggles */}
              <div className="space-y-4">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="registrationEnabled"
                    checked={registrationEnabled}
                    onChange={(e) => setRegistrationEnabled(e.target.checked)}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 rounded" style={{borderColor: 'var(--border-light)'}}
                  />
                  <label htmlFor="registrationEnabled" className="ml-2 block text-sm" style={{color: 'var(--text-primary)'}}>
                    Enable New User Registration
                  </label>
                </div>
                <p className="text-xs ml-6" style={{color: 'var(--text-secondary)'}}>
                  Allow new users to create accounts on the platform.
                </p>

                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="maintenanceMode"
                    checked={maintenanceMode}
                    onChange={(e) => setMaintenanceMode(e.target.checked)}
                    className="h-4 w-4 text-yellow-600 focus:ring-yellow-500 border-light rounded"
                  />
                  <label htmlFor="maintenanceMode" className="ml-2 block text-sm" style={{color: 'var(--text-primary)'}}>
                    Maintenance Mode
                  </label>
                </div>
                <p className="text-xs ml-6" style={{color: 'var(--text-secondary)'}}>
                  Enable maintenance mode to restrict access for system updates.
                </p>
              </div>

              {/* Save Message */}
              {saveMessage && (
                <div className={`p-3 rounded-md text-sm ${
                  saveMessage.includes('Error') 
                    ? 'bg-red-50 text-red-700 border border-red-200'
                    : 'bg-green-50 text-green-700 border border-green-200'
                }`}>
                  {saveMessage}
                </div>
              )}

              {/* Save Button */}
              <div className="flex justify-end pt-6 border-t" style={{borderColor: 'var(--border-light)'}}>
                <button
                  type="submit"
                  disabled={isSaving}
                  className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
                >
                  {isSaving && (
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                  )}
                  {isSaving ? 'Saving...' : 'Save Settings'}
                </button>
              </div>
            </form>
          </div>

          {/* Settings Error */}
          {settingsError && (
            <div className="p-6 border-t" style={{borderColor: 'var(--border-light)'}}>
              <div className="bg-red-50 border border-red-200 rounded-md p-3">
                <p className="text-sm text-red-700">
                  <strong>Settings Error:</strong> {settingsError}
                </p>
                <p className="text-xs text-red-600 mt-1">
                  Default values have been loaded. Changes may not persist without proper database setup.
                </p>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default AdminSettings;
