import { useState, useEffect } from 'react'
import { <PERSON> } from 'react-router-dom'
import { Heart, Shield, Users, Award, CheckCircle, Star } from 'lucide-react'
import { dataService } from '../lib/dataService'

export default function About() {
  const [stats, setStats] = useState({
    verifiedProfessionals: 0,
    averageRating: null,
    successfulBookings: 0,
    supportStatus: null,
    backgroundVerifiedPercentage: 100
  })
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchStats = async () => {
      try {
        const statsData = await dataService.getHomepageStats()
        setStats(statsData)
        setError(null)
      } catch (error) {
        console.error('Error fetching About page stats:', error)
        setError('Unable to load statistics. Please try again later.')
      } finally {
        setLoading(false)
      }
    }
    fetchStats()
  }, [])

  return (
    <main className="min-h-screen" style={{ backgroundColor: 'var(--bg-secondary)' }} role="main" aria-label="About Care Connector">
      {/* Hero Section */}
      <section className="py-16 px-4 sm:px-6 lg:px-8" style={{
        background: 'var(--primary)',
        color: 'var(--text-white)'
      }}>
        <div className="max-w-6xl mx-auto text-center">
          <div className="w-16 h-16 mx-auto mb-6 rounded-2xl backdrop-blur-sm flex items-center justify-center" style={{ backgroundColor: 'rgba(255, 255, 255, 0.2)' }}>
            <Heart className="w-8 h-8" style={{ color: 'var(--text-white)' }} />
          </div>
          <h1 className="text-4xl md:text-5xl font-bold mb-6 macos-title">
            About Care Connector
          </h1>
          <p className="text-xl md:text-2xl leading-relaxed max-w-4xl mx-auto" style={{ opacity: 0.9 }}>
            Connecting families with trusted healthcare professionals and building comprehensive care support systems with precision and elegance.
          </p>
        </div>
      </section>

      {/* Mission Section */}
      <section className="py-16 px-4 sm:px-6 lg:px-8">
        <div className="max-w-6xl mx-auto">
          <div className="grid md:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl md:text-4xl font-bold mb-6 text-primary macos-title">
                Our Mission
              </h2>
              <p className="text-lg text-secondary leading-relaxed mb-6">
                We believe that everyone deserves access to quality healthcare support. Care Connector bridges the gap between families seeking care and verified healthcare professionals, creating a trusted network where quality care meets genuine compassion.
              </p>
              <p className="text-lg text-secondary leading-relaxed">
                Our platform combines cutting-edge technology with human-centered design to deliver a seamless experience that prioritizes safety, trust, and meaningful connections.
              </p>
            </div>
            <div className="rounded-2xl p-8 shadow-lg" style={{ backgroundColor: 'var(--bg-primary)' }}>
              {error ? (
                <div className="text-center py-8">
                  <p style={{ color: 'var(--text-error)' }}>{error}</p>
                </div>
              ) : (
                <div className="grid grid-cols-2 gap-6">
                <div className="text-center">
                  <div className="w-12 h-12 mx-auto mb-4 rounded-xl bg-primary/10 flex items-center justify-center">
                    <Users className="w-6 h-6 text-primary" />
                  </div>
                  <h3 className="font-semibold text-primary">
                    {loading ? (
                      <div className="w-8 h-4 rounded animate-pulse" style={{ backgroundColor: 'var(--bg-progress)' }}></div>
                    ) : `${stats.verifiedProfessionals || 0}+`}
                  </h3>
                  <p className="text-sm text-secondary">Trusted Professionals</p>
                </div>
                <div className="text-center">
                  <div className="w-12 h-12 mx-auto mb-4 rounded-xl bg-primary/10 flex items-center justify-center">
                    <Shield className="w-6 h-6 text-primary" />
                  </div>
                  <h3 className="font-semibold text-primary">
                    {loading ? (
                      <div className="w-8 h-4 rounded animate-pulse" style={{ backgroundColor: 'var(--bg-progress)' }}></div>
                    ) : `${stats.backgroundVerifiedPercentage || 0}%`}
                  </h3>
                  <p className="text-sm text-secondary">Background Verified</p>
                </div>
                <div className="text-center">
                  <div className="w-12 h-12 mx-auto mb-4 rounded-xl bg-primary/10 flex items-center justify-center">
                    <Star className="w-6 h-6 text-primary" />
                  </div>
                  <h3 className="font-semibold text-primary">
                    {loading ? (
                      <div className="w-8 h-4 rounded animate-pulse" style={{ backgroundColor: 'var(--bg-progress)' }}></div>
                    ) : stats.averageRating ? `${stats.averageRating}/5` : 'N/A'}
                  </h3>
                  <p className="text-sm text-secondary">Average Rating</p>
                </div>
                <div className="text-center">
                  <div className="w-12 h-12 mx-auto mb-4 rounded-xl bg-primary/10 flex items-center justify-center">
                    <Award className="w-6 h-6 text-primary" />
                  </div>
                  <h3 className="font-semibold text-primary">
                    {loading ? (
                      <div className="w-16 h-4 rounded animate-pulse" style={{ backgroundColor: 'var(--bg-progress)' }}></div>
                    ) : stats.supportStatus ? stats.supportStatus : 'N/A'}
                  </h3>
                  <p className="text-sm text-secondary">Support Available</p>
                </div>
              </div>
              )}
            </div>
          </div>
        </div>
      </section>

      {/* Values Section */}
      <section className="py-16 px-4 sm:px-6 lg:px-8" style={{ backgroundColor: 'var(--bg-primary)' }}>
        <div className="max-w-6xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold mb-6 text-primary macos-title">
              Our Core Values
            </h2>
            <p className="text-lg text-secondary max-w-3xl mx-auto">
              These principles guide every decision we make and every feature we build.
            </p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8">
            <div className="text-center p-6">
              <div className="w-16 h-16 mx-auto mb-6 rounded-2xl bg-primary/10 flex items-center justify-center">
                <Shield className="w-8 h-8 text-primary" />
              </div>
              <h3 className="text-xl font-semibold mb-4 text-primary">Safety First</h3>
              <p className="text-secondary leading-relaxed">
                Every professional undergoes rigorous background checks, verification, and continuous monitoring to ensure the highest safety standards.
              </p>
            </div>
            
            <div className="text-center p-6">
              <div className="w-16 h-16 mx-auto mb-6 rounded-2xl bg-primary/10 flex items-center justify-center">
                <Heart className="w-8 h-8 text-primary" />
              </div>
              <h3 className="text-xl font-semibold mb-4 text-primary">Compassionate Care</h3>
              <p className="text-secondary leading-relaxed">
                We believe healthcare is fundamentally about human connection. Our platform facilitates meaningful relationships built on trust and empathy.
              </p>
            </div>
            
            <div className="text-center p-6">
              <div className="w-16 h-16 mx-auto mb-6 rounded-2xl bg-primary/10 flex items-center justify-center">
                <CheckCircle className="w-8 h-8 text-primary" />
              </div>
              <h3 className="text-xl font-semibold mb-4 text-primary">Excellence</h3>
              <p className="text-secondary leading-relaxed">
                We maintain the highest standards in service quality, platform reliability, and user experience through continuous improvement.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Team Section */}
      <section className="py-16 px-4 sm:px-6 lg:px-8">
        <div className="max-w-6xl mx-auto text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-6 text-primary macos-title">
            Built by Healthcare Professionals
          </h2>
          <p className="text-lg text-secondary leading-relaxed max-w-4xl mx-auto mb-12">
            Our team combines decades of healthcare experience with cutting-edge technology expertise. We understand the challenges of finding quality care because we've lived them ourselves.
          </p>
          
          <div className="rounded-2xl p-8 shadow-lg" style={{ backgroundColor: 'var(--bg-primary)' }}>
            <div className="grid md:grid-cols-3 gap-8 text-left">
              <div>
                <h3 className="font-semibold text-primary mb-2">Healthcare Experts</h3>
                <p className="text-secondary text-sm">
                  Registered nurses, physicians, and care coordinators who understand real-world healthcare needs.
                </p>
              </div>
              <div>
                <h3 className="font-semibold text-primary mb-2">Technology Leaders</h3>
                <p className="text-secondary text-sm">
                  Engineers from leading tech companies focused on building secure, scalable healthcare solutions.
                </p>
              </div>
              <div>
                <h3 className="font-semibold text-primary mb-2">Patient Advocates</h3>
                <p className="text-secondary text-sm">
                  Individuals with personal caregiving experience who ensure our platform serves real family needs.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 px-4 sm:px-6 lg:px-8" style={{
        backgroundColor: 'var(--primary)',
        color: 'var(--text-white)'
      }}>
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-6 macos-title">
            Ready to Find Your Care Team?
          </h2>
          <p className="text-xl opacity-90 leading-relaxed mb-8">
            Join thousands of families who trust Care Connector for their healthcare needs.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              to="/auth"
              className="inline-flex items-center justify-center px-8 py-4 text-lg font-semibold rounded-xl transition-all duration-200"
              style={{
                backgroundColor: 'var(--bg-primary)',
                color: 'var(--primary)'
              }}
            >
              Get Started Today
            </Link>
            <Link
              to="/how-it-works"
              className="inline-flex items-center justify-center px-8 py-4 text-lg font-semibold rounded-xl transition-all duration-200"
              style={{
                border: '2px solid var(--text-white)',
                color: 'var(--text-white)',
                backgroundColor: 'transparent'
              }}
            >
              Learn More
            </Link>
          </div>
        </div>
      </section>
    </main>
  )
}
