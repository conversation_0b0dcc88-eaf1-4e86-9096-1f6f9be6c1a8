import { useState, useEffect, useCallback, useMemo, Suspense } from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import { dataService } from '../lib/dataService'
import { supabase } from '../lib/supabase'
import { ArrowLeft, Star, MapPin, Clock, MessageCircle, CheckCircle, Calendar, DollarSign, Award, Users } from 'lucide-react'

interface Provider {
  id: string
  name: string
  bio: string
  location: string
  specialties: string[]
  verified: boolean
  provider_type: string
  hourly_rate?: number
  years_experience?: number
  profile_image?: string
  rating?: number
  reviews_count?: number
  availability?: string[]
}

interface Review {
  id: string
  rating: number
  review_text: string
  created_at: string
  reviewer_name: string
  reviewer_avatar?: string
  helpful_count?: number
}

export default function ProviderProfile() {
  const { providerId, providerType } = useParams()
  const navigate = useNavigate()
  const [provider, setProvider] = useState<Provider | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [selectedDate, setSelectedDate] = useState('')
  const [selectedTime, setSelectedTime] = useState('')
  const [bookingStep, setBookingStep] = useState(1)
  const [availableTimeSlots, setAvailableTimeSlots] = useState<string[]>([])
  const [paymentMethod, setPaymentMethod] = useState('')
  const [bookingError, setBookingError] = useState<string | null>(null)
  const [isBooking, setIsBooking] = useState(false)
  const [reviews, setReviews] = useState<Review[]>([])
  const [reviewsLoading, setReviewsLoading] = useState(false)
  const [activeTab, setActiveTab] = useState('overview')
  const [showAllReviews, setShowAllReviews] = useState(false)
  const [showAvailabilityCalendar, setShowAvailabilityCalendar] = useState(false)
  const [showMessageModal, setShowMessageModal] = useState(false)
  const [messageText, setMessageText] = useState('')
  const [messageSending, setMessageSending] = useState(false)

  // Generate dynamic time slots based on business hours
  const generateTimeSlots = () => {
    const slots = []
    // Business hours: 9 AM to 6 PM, excluding lunch hour (12-1 PM)
    for (let hour = 9; hour <= 17; hour++) {
      if (hour !== 12) { // Skip lunch hour
        const time12 = hour > 12 ? `${hour - 12}:00 PM` : `${hour}:00 AM`
        slots.push(time12)
      }
    }
    return slots
  }

  // Fetch reviews for the provider
  const fetchReviews = useCallback(async (providerId: string) => {
    if (!providerId) return

    setReviewsLoading(true)
    try {
      // Mock reviews data - in real app would fetch from database
      const mockReviews: Review[] = [
        {
          id: '1',
          rating: 5,
          review_text: 'Excellent caregiver! Very professional and caring. My mother loves her.',
          created_at: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
          reviewer_name: 'Sarah Johnson',
          helpful_count: 12
        },
        {
          id: '2',
          rating: 4,
          review_text: 'Great experience overall. Very reliable and punctual. Would recommend.',
          created_at: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000).toISOString(),
          reviewer_name: 'Michael Chen',
          helpful_count: 8
        },
        {
          id: '3',
          rating: 5,
          review_text: 'Outstanding care for my elderly father. Compassionate and skilled.',
          created_at: new Date(Date.now() - 21 * 24 * 60 * 60 * 1000).toISOString(),
          reviewer_name: 'Emily Rodriguez',
          helpful_count: 15
        }
      ]

      setReviews(mockReviews)
    } catch (error) {
      console.error('Error fetching reviews:', error)
      setReviews([])
    } finally {
      setReviewsLoading(false)
    }
  }, [])

  // Memoized rating statistics
  const ratingStats = useMemo(() => {
    if (reviews.length === 0) return null

    const ratingCounts = [0, 0, 0, 0, 0] // Index 0 = 1 star, Index 4 = 5 stars
    reviews.forEach(review => {
      if (review.rating >= 1 && review.rating <= 5) {
        ratingCounts[review.rating - 1]++
      }
    })

    const averageRating = reviews.reduce((sum, review) => sum + review.rating, 0) / reviews.length

    return {
      average: averageRating,
      total: reviews.length,
      distribution: ratingCounts.map((count, index) => ({
        stars: index + 1,
        count,
        percentage: (count / reviews.length) * 100
      })).reverse() // Show 5 stars first
    }
  }, [reviews])


  // Memoize fetchProvider to prevent infinite loops
  const fetchProvider = useCallback(async () => {
    if (!providerId || !providerType) {
      console.log('Missing providerId or providerType:', { providerId, providerType })
      setError('Invalid provider information')
      setLoading(false)
      return
    }

    setLoading(true)
    setError(null)
    
    try {
      console.log('=== ENHANCED FETCHPROVIDER DEBUG ===')
      console.log('Provider ID from URL:', providerId)
      console.log('Provider Type from URL:', providerType)
      console.log('Current provider state before fetch:', provider)
      
      let data: any[] = []
      
      console.log('Fetching provider:', { providerId, providerType })
      
      // Use real database connections for all provider types
      // Handle both singular (from URLs) and plural forms for compatibility
      switch (providerType) {
        case 'caregiver':
        case 'caregivers':
          data = await dataService.getCaregivers()
          break
        case 'companion':
        case 'companions':
          data = await dataService.getCompanions()
          break
        case 'professional':
        case 'professionals':
          data = await dataService.getProfessionals()
          break
        case 'care-checker':
        case 'care-checkers':
          data = await dataService.getCareCheckers()
          break
        default:
          throw new Error('Invalid provider type')
      }
      
      console.log('Available providers:', data.map(p => ({ id: p.id, name: p.name, idType: typeof p.id })))
      console.log('Looking for providerId:', providerId, 'Type:', typeof providerId)
      
      // Enhanced ID matching with type conversion and string comparison
      const foundProvider = data.find(p => {
        const match = p.id === providerId || String(p.id) === String(providerId)
        if (match) console.log('MATCH FOUND:', p.name, 'ID:', p.id)
        return match
      })
      
      if (!foundProvider) {
        console.error('Provider not found. Available IDs:', data.map(p => ({ id: p.id, type: typeof p.id })))
        console.error('Looking for ID:', providerId, 'Type:', typeof providerId)
        console.error('Exact comparison failed for all providers')
        throw new Error(`Provider with ID ${providerId} not found`)
      }
      
      console.log('Found provider:', foundProvider.name)
      console.log('Provider location:', foundProvider.location)
      console.log('Provider role:', foundProvider.provider_type)
      
      const processedProvider = {
        id: foundProvider.id,
        name: foundProvider.name || 'Unknown Provider', // Use the already processed name from dataService
        bio: foundProvider.bio || 'No bio available',
        location: foundProvider.location || 'Location not specified',
        specialties: foundProvider.specialties || [],
        verified: foundProvider.verified || false,
        hourly_rate: foundProvider.hourly_rate,
        years_experience: foundProvider.years_experience,
        rating: foundProvider.rating,
        reviews_count: foundProvider.reviews_count || 0,
        provider_type: foundProvider.provider_type,
      }
      
      console.log('Processed provider object:', processedProvider)
      console.log('About to call setProvider with:', processedProvider)
      setProvider(processedProvider)
      console.log('setProvider called - provider state should now be set')
      
      // Additional verification
      setTimeout(() => {
        console.log('Provider state after 100ms delay:', provider)
      }, 100)
      
    } catch (err) {
      console.error('fetchProvider error:', err)
      console.log('Setting error state:', err instanceof Error ? err.message : 'Failed to load provider')
      setError(err instanceof Error ? err.message : 'Failed to load provider')
    } finally {
      setLoading(false)
      console.log('Loading set to false')
      console.log('=== END ENHANCED FETCHPROVIDER DEBUG ===')
    }
  }, [providerId, providerType]) // Dependencies for useCallback

  useEffect(() => {
    fetchProvider()
  }, [fetchProvider]) // Now depends on memoized fetchProvider

  useEffect(() => {
    // Set available time slots when component mounts
    setAvailableTimeSlots(generateTimeSlots())
  }, [])

  // Fetch reviews when provider is loaded
  useEffect(() => {
    if (provider?.id) {
      fetchReviews(provider.id)
    }
  }, [provider?.id, fetchReviews])

  const handleSendMessage = async () => {
    if (!messageText.trim() || !provider?.id) return

    setMessageSending(true)
    try {
      // In real app, would send message to database
      console.log('Sending message to provider:', provider.id, messageText)

      // Mock API call
      await new Promise(resolve => setTimeout(resolve, 1000))

      // Reset form and close modal
      setMessageText('')
      setShowMessageModal(false)

      // Show success notification (in real app)
      alert('Message sent successfully!')
    } catch (error) {
      console.error('Error sending message:', error)
      alert('Failed to send message. Please try again.')
    } finally {
      setMessageSending(false)
    }
  }

  const validateBookingDate = (date: string): string | null => {
    const today = new Date()
    const selectedDateObj = new Date(date)
    const todayStr = today.toISOString().split('T')[0]

    // Past date validation
    if (date < todayStr) {
      return 'Please select a future date for your appointment.'
    }

    // Maximum advance booking (90 days)
    const maxAdvanceDays = 90
    const maxDate = new Date()
    maxDate.setDate(today.getDate() + maxAdvanceDays)
    if (selectedDateObj > maxDate) {
      return `Bookings can only be made up to ${maxAdvanceDays} days in advance.`
    }

    // Weekend validation (assuming weekends are not available)
    const dayOfWeek = selectedDateObj.getDay()
    if (dayOfWeek === 0 || dayOfWeek === 6) {
      return 'Appointments are only available Monday through Friday.'
    }

    return null // No validation errors
  }

  const handleBookAppointment = () => {
    if (bookingStep === 1 && selectedDate) {
      // Comprehensive date validation
      const validationError = validateBookingDate(selectedDate)
      if (validationError) {
        setBookingError(validationError)
        return
      }
      // Generate available time slots for selected date
      setAvailableTimeSlots(generateTimeSlots())
      setBookingStep(2)
    } else if (bookingStep === 2 && selectedTime) {
      // Move to payment step
      setBookingStep(3)
    } else if (bookingStep === 3 && paymentMethod) {
      // Save booking to database with payment info
      saveBookingToDatabase()
    }
  }

  const saveBookingToDatabase = async () => {
    setIsBooking(true)
    setBookingError(null)

    try {
      // Calculate start and end times
      const [time, period] = selectedTime.split(' ')
      const [hours, minutes] = time.split(':')
      let hour24 = parseInt(hours)
      if (period === 'PM' && hour24 !== 12) hour24 += 12
      if (period === 'AM' && hour24 === 12) hour24 = 0

      const startDateTime = new Date(selectedDate)
      startDateTime.setHours(hour24, parseInt(minutes || '0'), 0, 0)

      const endDateTime = new Date(startDateTime)
      endDateTime.setHours(startDateTime.getHours() + 1) // 1 hour session

      // Get current user
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) {
        alert('Please log in to book an appointment.')
        return
      }

      // Check for double booking
      const { data: existingBookings, error: checkError } = await supabase
        .from('bookings')
        .select('id')
        .eq('provider_id', provider?.id)
        .eq('start_time', startDateTime.toISOString())
        .eq('status', 'pending')

      if (checkError) {
        console.error('Error checking existing bookings:', checkError)
        alert('Error checking availability. Please try again.')
        return
      }

      if (existingBookings && existingBookings.length > 0) {
        alert('This time slot is already booked. Please select a different time.')
        return
      }

      // Save to database
      const { data, error } = await supabase
        .from('bookings')
        .insert({
          user_id: user.id,
          provider_id: provider?.id,
          provider_type: providerType || 'caregiver',
          start_time: startDateTime.toISOString(),
          end_time: endDateTime.toISOString(),
          status: 'pending',
          total_cost: provider?.hourly_rate,
          payment_status: 'pending',
          notes: `Booking for ${provider?.name} on ${selectedDate} at ${selectedTime}`
        })

      if (error) {
        console.error('Error saving booking:', error)
        alert('Error saving booking. Please try again.')
        return
      }

      console.log('Booking saved successfully:', data)

      // Create notifications for booking confirmation
      if (data && Array.isArray(data) && data.length > 0) {
        await createBookingNotifications(user.id, provider?.id, (data as any)[0].id)
      }

      setBookingStep(4)
    } catch (error) {
      console.error('Error in saveBookingToDatabase:', error)
      setBookingError('Failed to book appointment. Please try again.')
    } finally {
      setIsBooking(false)
    }
  }

  const createBookingNotifications = async (userId: string, providerId: string | undefined, bookingId: string) => {
    try {
      const notifications = [
        // Notification for the user (booking confirmation)
        {
          user_id: userId,
          type: 'booking_confirmation',
          title: 'Booking Confirmed',
          content: `Your appointment with ${provider?.name} has been confirmed for ${selectedDate} at ${selectedTime}.`,
          related_entity_id: bookingId,
          related_table: 'bookings',
          related_entity_type: 'booking',
          metadata: {
            booking_date: selectedDate,
            booking_time: selectedTime,
            provider_name: provider?.name
          }
        }
      ]

      // Add notification for provider if providerId exists
      if (providerId) {
        notifications.push({
          user_id: providerId,
          type: 'new_booking',
          title: 'New Booking Request',
          content: `You have a new booking request for ${selectedDate} at ${selectedTime}.`,
          related_entity_id: bookingId,
          related_table: 'bookings',
          related_entity_type: 'booking',
          metadata: {
            booking_date: selectedDate,
            booking_time: selectedTime,
            provider_name: provider?.name
          }
        })
      }

      const { error: notificationError } = await supabase
        .from('notifications')
        .insert(notifications)

      if (notificationError) {
        console.error('Error creating notifications:', notificationError)
        // Don't fail the booking if notifications fail
      } else {
        console.log('Booking notifications created successfully')
      }
    } catch (error) {
      console.error('Error in createBookingNotifications:', error)
      // Don't fail the booking if notifications fail
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center relative overflow-hidden" style={{ backgroundColor: 'var(--bg-primary)' }}>
        {/* Background Elements */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute top-20 right-20 w-32 h-32 rounded-full animate-pulse-subtle" style={{ backgroundColor: 'var(--bg-accent)' }}></div>
          <div className="absolute bottom-20 left-20 w-24 h-24 rounded-full animate-pulse-subtle" style={{ backgroundColor: 'var(--bg-accent)', animationDelay: '1s' }}></div>
        </div>

        <div className="text-center max-w-md mx-auto p-8 relative z-10 animate-fadeInUp">
          <div className="w-20 h-20 mx-auto mb-6 rounded-2xl flex items-center justify-center shadow-lg" style={{ backgroundColor: 'var(--bg-accent)' }}>
            <div className="w-8 h-8 border-4 rounded-full animate-spin border-t-transparent" style={{ borderColor: 'var(--primary)' }}></div>
          </div>
          <h2 className="text-2xl font-light mb-2" style={{ color: 'var(--text-primary)' }}>Loading Profile</h2>
          <p style={{ color: 'var(--text-secondary)' }}>Preparing provider information...</p>
        </div>
      </div>
    )
  }

  if (error || !provider) {
    return (
      <div className="min-h-screen flex items-center justify-center relative overflow-hidden" style={{ backgroundColor: 'var(--bg-primary)' }} role="main" aria-label="Provider profile error">
        {/* Background Elements */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute top-20 right-20 w-32 h-32 rounded-full animate-pulse-subtle" style={{ backgroundColor: 'var(--bg-accent)' }}></div>
          <div className="absolute bottom-20 left-20 w-24 h-24 rounded-full animate-pulse-subtle" style={{ backgroundColor: 'var(--bg-accent)', animationDelay: '1s' }}></div>
        </div>

        <div className="text-center max-w-md mx-auto p-8 relative z-10 animate-fadeInUp">
          <div className="w-20 h-20 mx-auto mb-6 rounded-2xl flex items-center justify-center shadow-lg" style={{ backgroundColor: 'var(--bg-error-light)' }}>
            <div className="w-8 h-8" style={{ color: 'var(--error)' }}>⚠️</div>
          </div>
          <h2 className="text-2xl font-light mb-2" style={{ color: 'var(--text-primary)' }}>Profile Not Found</h2>
          <p className="mb-6" style={{ color: 'var(--text-secondary)' }} role="alert">{error || 'Provider not found'}</p>
          <button
            onClick={() => navigate(-1)}
            className="px-5 py-2 rounded-lg font-medium transition-colors duration-150"
            style={{
              backgroundColor: 'var(--primary)',
              color: 'var(--text-white)',
              boxShadow: 'var(--shadow-card)',
              transition: 'all 0.2s ease'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.opacity = '0.9'
              e.currentTarget.style.transform = 'translateY(-1px)'
              e.currentTarget.style.boxShadow = 'var(--shadow-hover)'
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.opacity = '1'
              e.currentTarget.style.transform = 'translateY(0)'
              e.currentTarget.style.boxShadow = 'var(--shadow-card)'
            }}
            onFocus={(e) => {
              e.currentTarget.style.boxShadow = 'var(--focus-shadow)'
            }}
            onBlur={(e) => {
              e.currentTarget.style.boxShadow = 'var(--shadow-card)'
            }}
            aria-label="Go back to previous page"
          >
            Go Back
          </button>
        </div>
      </div>
    )
  }

  return (
    <main className="min-h-screen" style={{ backgroundColor: 'var(--bg-secondary)' }} role="main" aria-label="Provider profile">
      <div className="max-w-7xl mx-auto px-6 py-12">
        {/* Apple Mac Desktop Back Button */}
        <button
          onClick={() => navigate(-1)}
          className="flex items-center gap-3 mb-12 px-6 py-3 rounded-2xl transition-all duration-300 transform hover:scale-105 hover:shadow-lg group"
          style={{
            backgroundColor: 'var(--bg-primary)',
            border: '1px solid var(--border-light)',
            boxShadow: 'var(--shadow-card)'
          }}
          aria-label={`Go back to ${providerType?.replace('-', ' ')} listings`}
          onMouseEnter={(e) => {
            e.currentTarget.style.boxShadow = 'var(--shadow-large)'
            e.currentTarget.style.transform = 'translateY(-2px) scale(1.02)'
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.boxShadow = 'var(--shadow-card)'
            e.currentTarget.style.transform = 'translateY(0) scale(1)'
          }}
        >
          <ArrowLeft className="h-5 w-5 transition-colors" style={{ color: 'var(--text-secondary)' }} aria-hidden="true" />
          <span className="font-medium transition-colors macos-body" style={{ color: 'var(--text-primary)' }}>Back to {providerType?.replace('-', ' ')}</span>
        </button>

        <div className="grid grid-cols-1 xl:grid-cols-3 gap-12">
          {/* Apple Mac Desktop Provider Info Card */}
          <section className="lg:col-span-2" aria-labelledby="provider-name" role="region">
            <div className="rounded-3xl p-12 animate-fadeInUp" style={{
              backgroundColor: 'var(--bg-primary)',
              border: '1px solid var(--border-light)',
              boxShadow: 'var(--shadow-large)',
              backdropFilter: 'blur(10px)'
            }}>
              {/* Apple Mac Desktop Header */}
              <div className="flex items-start gap-8 mb-12">
                <div className="w-32 h-32 rounded-3xl flex items-center justify-center shadow-xl transition-all duration-300" style={{
                  backgroundColor: 'var(--primary)',
                  background: 'linear-gradient(135deg, var(--primary), var(--primary))'
                }} role="img" aria-label={`Profile picture for ${provider.name}`}>
                  <span className="text-4xl font-bold" style={{ color: 'var(--bg-primary)' }}>
                    {provider.name.split(' ').map(n => n[0]).join('').toUpperCase()}
                  </span>
                </div>
                <header className="flex-1">
                  <div className="flex items-center gap-4 mb-6">
                    <h1 className="text-4xl font-light macos-title" style={{
                      color: 'var(--text-primary)',
                      fontWeight: '300',
                      letterSpacing: '-0.02em',
                      lineHeight: '1.1'
                    }} id="provider-name">{provider.name}</h1>
                    {provider.verified && (
                      <div className="flex items-center gap-2 px-4 py-2 rounded-full" style={{
                        backgroundColor: 'var(--primary)',
                        boxShadow: 'var(--shadow-medium)'
                      }} role="status" aria-label="Verified provider badge">
                        <CheckCircle className="h-5 w-5" style={{ color: 'var(--bg-primary)' }} aria-hidden="true" />
                        <span className="text-sm font-medium" style={{ color: 'var(--bg-primary)' }}>Verified</span>
                      </div>
                    )}
                  </div>
                  <div className="flex items-center gap-8 mb-8">
                    <div className="flex items-center gap-3 px-6 py-4 rounded-2xl" style={{
                      backgroundColor: 'var(--bg-secondary)',
                      border: '1px solid var(--border-light)',
                      boxShadow: 'var(--shadow-card)'
                    }} role="group" aria-label={`Rating: ${provider.rating ? provider.rating.toFixed(1) : 'No rating'} out of 5 stars`}>
                      <Star className="h-6 w-6" style={{ color: 'var(--accent-warning)', fill: 'var(--accent-warning)' }} aria-hidden="true" />
                      <span className="text-xl font-semibold macos-title" style={{ color: 'var(--text-primary)' }}>{provider.rating ? provider.rating.toFixed(1) : 'No rating'}</span>
                      <span className="text-base macos-body" style={{ color: 'var(--text-secondary)' }}>({provider.reviews_count || 0} reviews)</span>
                    </div>
                    <div className="flex items-center gap-3 px-6 py-4 rounded-2xl" style={{
                      backgroundColor: 'var(--bg-secondary)',
                      border: '1px solid var(--border-light)',
                      boxShadow: 'var(--shadow-card)'
                    }} role="group" aria-label={`Location: ${provider.location}`}>
                      <MapPin className="h-6 w-6" style={{ color: 'var(--primary)' }} aria-hidden="true" />
                      <span className="text-lg font-medium macos-body" style={{ color: 'var(--text-primary)' }}>{provider.location}</span>
                    </div>
                  </div>
                  <div className="flex items-center gap-8">
                    <div className="px-8 py-4 rounded-2xl" style={{
                      backgroundColor: 'var(--primary)',
                      boxShadow: 'var(--shadow-large)'
                    }} role="group" aria-label={`Hourly rate: $${provider.hourly_rate} per hour`}>
                      <span className="text-2xl font-semibold macos-title" style={{ color: 'var(--bg-primary)' }}>
                        ${provider.hourly_rate}/hour
                      </span>
                    </div>
                    <div className="flex items-center gap-3 px-6 py-4 rounded-2xl" style={{
                      backgroundColor: 'var(--bg-secondary)',
                      border: '1px solid var(--border-light)',
                      boxShadow: 'var(--shadow-card)'
                    }} role="group" aria-label={`Experience: ${provider.years_experience} years`}>
                      <Clock className="h-6 w-6" style={{ color: 'var(--primary)' }} aria-hidden="true" />
                      <span className="text-lg font-medium macos-body" style={{ color: 'var(--text-primary)' }}>
                        {provider.years_experience} years experience
                      </span>
                    </div>
                  </div>
                </header>
              </div>

              {/* Apple Mac Desktop Bio Section */}
              <div className="mb-12">
                <h2 className="text-2xl font-semibold mb-6 macos-title" style={{
                  color: 'var(--text-primary)',
                  fontWeight: '600',
                  letterSpacing: '-0.01em'
                }}>About</h2>
                <div className="p-8 rounded-3xl" style={{
                  backgroundColor: 'var(--bg-secondary)',
                  border: '1px solid var(--border-light)',
                  boxShadow: 'var(--shadow-card)'
                }}>
                  <p className="text-lg leading-relaxed macos-body" style={{
                    color: 'var(--text-secondary)',
                    lineHeight: '1.6'
                  }}>{provider.bio}</p>
                </div>
              </div>

              {/* Apple Mac Desktop Availability Calendar Section */}
              <div className="mb-12">
                <div className="flex items-center justify-between mb-6">
                  <h2 className="text-2xl font-semibold macos-title" style={{
                    color: 'var(--text-primary)',
                    fontWeight: '600',
                    letterSpacing: '-0.01em'
                  }}>Availability</h2>
                  <button
                    onClick={() => setShowAvailabilityCalendar(!showAvailabilityCalendar)}
                    className="flex items-center gap-2 px-4 py-2 rounded-xl font-medium transition-all duration-200"
                    style={{
                      color: 'var(--primary)',
                      backgroundColor: 'var(--bg-accent)',
                      border: '1px solid var(--border-light)'
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.backgroundColor = 'var(--primary)'
                      e.currentTarget.style.color = 'var(--bg-primary)'
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.backgroundColor = 'var(--bg-accent)'
                      e.currentTarget.style.color = 'var(--primary)'
                    }}
                    aria-expanded={showAvailabilityCalendar}
                    aria-controls="availability-calendar"
                  >
                    <Calendar className="h-4 w-4" aria-hidden="true" />
                    {showAvailabilityCalendar ? 'Hide Calendar' : 'View Calendar'}
                  </button>
                </div>

                {showAvailabilityCalendar && (
                  <div id="availability-calendar" className="p-8 rounded-3xl" style={{
                    backgroundColor: 'var(--bg-secondary)',
                    border: '1px solid var(--border-light)',
                    boxShadow: 'var(--shadow-card)'
                  }}>
                    <div className="grid grid-cols-7 gap-2 mb-4">
                      {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map((day) => (
                        <div key={day} className="text-center text-sm font-medium py-2" style={{ color: 'var(--text-secondary)' }}>
                          {day}
                        </div>
                      ))}
                    </div>
                    <div className="grid grid-cols-7 gap-2">
                      {Array.from({ length: 35 }, (_, i) => {
                        const date = new Date()
                        date.setDate(date.getDate() - date.getDay() + i)
                        const isToday = date.toDateString() === new Date().toDateString()
                        const isPast = date < new Date(new Date().setHours(0, 0, 0, 0))
                        const isAvailable = !isPast && Math.random() > 0.3 // Mock availability

                        return (
                          <button
                            key={i}
                            className="aspect-square rounded-lg text-sm font-medium transition-all duration-200 hover:scale-105"
                            style={{
                              backgroundColor: isPast
                                ? 'var(--bg-muted)'
                                : isAvailable
                                  ? 'var(--bg-success)'
                                  : 'var(--bg-primary)',
                              color: isPast
                                ? 'var(--text-muted)'
                                : isAvailable
                                  ? 'var(--text-success)'
                                  : 'var(--text-secondary)',
                              border: isToday ? '2px solid var(--primary)' : '1px solid var(--border-light)',
                              cursor: isPast ? 'not-allowed' : isAvailable ? 'pointer' : 'default'
                            }}
                            disabled={isPast || !isAvailable}
                            onClick={() => {
                              if (isAvailable && !isPast) {
                                setSelectedDate(date.toISOString().split('T')[0])
                                setShowAvailabilityCalendar(false)
                              }
                            }}
                            aria-label={`${date.toLocaleDateString()}, ${isPast ? 'past date' : isAvailable ? 'available' : 'unavailable'}`}
                            title={`${date.toLocaleDateString()} - ${isPast ? 'Past' : isAvailable ? 'Available' : 'Unavailable'}`}
                          >
                            {date.getDate()}
                          </button>
                        )
                      })}
                    </div>
                    <div className="flex items-center justify-center gap-6 mt-6 text-sm">
                      <div className="flex items-center gap-2">
                        <div className="w-4 h-4 rounded" style={{ backgroundColor: 'var(--bg-success)' }}></div>
                        <span style={{ color: 'var(--text-secondary)' }}>Available</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="w-4 h-4 rounded" style={{ backgroundColor: 'var(--bg-primary)', border: '1px solid var(--border-light)' }}></div>
                        <span style={{ color: 'var(--text-secondary)' }}>Unavailable</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="w-4 h-4 rounded" style={{ backgroundColor: 'var(--bg-muted)' }}></div>
                        <span style={{ color: 'var(--text-secondary)' }}>Past</span>
                      </div>
                    </div>
                  </div>
                )}

                {!showAvailabilityCalendar && (
                  <div className="p-8 rounded-3xl" style={{
                    backgroundColor: 'var(--bg-secondary)',
                    border: '1px solid var(--border-light)',
                    boxShadow: 'var(--shadow-card)'
                  }}>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                      <div className="text-center">
                        <div className="w-12 h-12 mx-auto mb-3 rounded-2xl flex items-center justify-center" style={{
                          backgroundColor: 'var(--bg-success)',
                          color: 'var(--text-success)'
                        }}>
                          <Calendar className="h-6 w-6" aria-hidden="true" />
                        </div>
                        <h3 className="font-semibold mb-2" style={{ color: 'var(--text-primary)' }}>Weekdays</h3>
                        <p className="text-sm" style={{ color: 'var(--text-secondary)' }}>9:00 AM - 6:00 PM</p>
                      </div>
                      <div className="text-center">
                        <div className="w-12 h-12 mx-auto mb-3 rounded-2xl flex items-center justify-center" style={{
                          backgroundColor: 'var(--bg-success)',
                          color: 'var(--text-success)'
                        }}>
                          <Clock className="h-6 w-6" aria-hidden="true" />
                        </div>
                        <h3 className="font-semibold mb-2" style={{ color: 'var(--text-primary)' }}>Weekends</h3>
                        <p className="text-sm" style={{ color: 'var(--text-secondary)' }}>10:00 AM - 4:00 PM</p>
                      </div>
                      <div className="text-center">
                        <div className="w-12 h-12 mx-auto mb-3 rounded-2xl flex items-center justify-center" style={{
                          backgroundColor: 'var(--primary)',
                          color: 'var(--bg-primary)'
                        }}>
                          <CheckCircle className="h-6 w-6" aria-hidden="true" />
                        </div>
                        <h3 className="font-semibold mb-2" style={{ color: 'var(--text-primary)' }}>Response Time</h3>
                        <p className="text-sm" style={{ color: 'var(--text-secondary)' }}>Usually within 2 hours</p>
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {/* Apple Mac Desktop Specialties Section */}
              <div className="mb-12">
                <h2 className="text-2xl font-semibold mb-6 macos-title" style={{
                  color: 'var(--text-primary)',
                  fontWeight: '600',
                  letterSpacing: '-0.01em'
                }}>Specialties</h2>
                <div className="flex flex-wrap gap-4" role="list" aria-label="Provider specialties">
                  {provider.specialties.map((specialty, index) => (
                    <span
                      key={index}
                      className="px-6 py-3 rounded-2xl text-base font-medium macos-body"
                      style={{
                        backgroundColor: 'var(--primary)',
                        color: 'var(--bg-primary)',
                        boxShadow: 'var(--shadow-medium)'
                      }}
                      role="listitem"
                    >
                      {specialty}
                    </span>
                  ))}
                </div>
              </div>

              {/* Apple Mac Desktop Additional Information */}
              <div className="mb-12">
                <h2 className="text-2xl font-semibold mb-6 macos-title" style={{
                  color: 'var(--text-primary)',
                  fontWeight: '600',
                  letterSpacing: '-0.01em'
                }}>Additional Information</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="p-8 rounded-3xl" style={{
                    backgroundColor: 'var(--bg-primary)',
                    border: '1px solid var(--border-light)',
                    boxShadow: 'var(--shadow-card)'
                  }}>
                    <h3 className="text-lg font-semibold mb-4 macos-title" style={{ color: 'var(--text-primary)' }}>Background Check</h3>
                    <div className="flex items-center gap-3">
                      <CheckCircle className="h-6 w-6" style={{ color: 'var(--primary)' }} aria-hidden="true" />
                      <span className="text-base font-medium macos-body" style={{ color: 'var(--text-secondary)' }}>Verified & Current</span>
                    </div>
                  </div>
                  <div className="p-8 rounded-3xl" style={{
                    backgroundColor: 'var(--bg-primary)',
                    border: '1px solid var(--border-light)',
                    boxShadow: 'var(--shadow-card)'
                  }}>
                    <h3 className="text-lg font-semibold mb-4 macos-title" style={{ color: 'var(--text-primary)' }}>Languages</h3>
                    <p className="text-base macos-body" style={{ color: 'var(--text-secondary)' }}>English, Spanish</p>
                  </div>
                  <div className="p-8 rounded-3xl" style={{
                    backgroundColor: 'var(--bg-primary)',
                    border: '1px solid var(--border-light)',
                    boxShadow: 'var(--shadow-card)'
                  }}>
                    <h3 className="text-lg font-semibold mb-4 macos-title" style={{ color: 'var(--text-primary)' }}>Insurance</h3>
                    <p className="text-base macos-body" style={{ color: 'var(--text-secondary)' }}>Liability & Bonded</p>
                  </div>
                  <div className="p-4 rounded-lg" style={{ backgroundColor: 'var(--bg-secondary)' }}>
                    <h3 className="font-medium mb-2 macos-body" style={{ color: 'var(--text-primary)' }}>Availability</h3>
                    <p className="text-sm macos-body" style={{ color: 'var(--text-secondary)' }}>Weekdays & Weekends</p>
                  </div>
                </div>
              </div>

              {/* Contact Actions */}
              <div className="mb-6">
                <div className="flex gap-3">
                  <button className="flex-1 px-4 py-3 rounded-lg transition-all duration-200 flex items-center justify-center gap-2 macos-body"
                          style={{
                            backgroundColor: 'var(--primary)',
                            color: 'var(--text-white)',
                            boxShadow: 'var(--shadow-card)'
                          }}
                          onMouseEnter={(e) => {
                            e.currentTarget.style.opacity = '0.9'
                            e.currentTarget.style.transform = 'translateY(-1px)'
                            e.currentTarget.style.boxShadow = 'var(--shadow-hover)'
                          }}
                          onMouseLeave={(e) => {
                            e.currentTarget.style.opacity = '1'
                            e.currentTarget.style.transform = 'translateY(0)'
                            e.currentTarget.style.boxShadow = 'var(--shadow-card)'
                          }}
                          onFocus={(e) => {
                            e.currentTarget.style.boxShadow = 'var(--focus-shadow)'
                          }}
                          onBlur={(e) => {
                            e.currentTarget.style.boxShadow = 'var(--shadow-card)'
                          }}
                          aria-label={`Send message to ${provider.name}`}
                          onClick={() => setShowMessageModal(true)}>
                    <MessageCircle className="h-4 w-4" aria-hidden="true" />
                    Send Message
                  </button>
                  <button className="flex-1 px-4 py-3 rounded-lg transition-all duration-200 macos-body"
                          style={{
                            border: '1px solid var(--border-medium)',
                            color: 'var(--text-primary)',
                            backgroundColor: 'var(--bg-primary)',
                            boxShadow: 'var(--shadow-sm)'
                          }}
                          onMouseEnter={(e) => {
                            e.currentTarget.style.backgroundColor = 'var(--bg-accent)'
                            e.currentTarget.style.transform = 'translateY(-1px)'
                            e.currentTarget.style.boxShadow = 'var(--shadow-card)'
                          }}
                          onMouseLeave={(e) => {
                            e.currentTarget.style.backgroundColor = 'var(--bg-primary)'
                            e.currentTarget.style.transform = 'translateY(0)'
                            e.currentTarget.style.boxShadow = 'var(--shadow-sm)'
                          }}
                          onFocus={(e) => {
                            e.currentTarget.style.boxShadow = 'var(--focus-shadow)'
                          }}
                          onBlur={(e) => {
                            e.currentTarget.style.boxShadow = 'var(--shadow-sm)'
                          }}
                          aria-label={`Save ${provider.name}'s profile`}>
                    Save Profile
                  </button>
                </div>
              </div>

              {/* Tab Navigation */}
              <div className="mb-8">
                <nav className="flex space-x-1 p-1 rounded-2xl" style={{
                  backgroundColor: 'var(--bg-secondary)',
                  border: '1px solid var(--border-light)'
                }} role="tablist" aria-label="Provider information tabs">
                  {[
                    { id: 'overview', label: 'Overview', icon: Users },
                    { id: 'reviews', label: 'Reviews & Ratings', icon: Star }
                  ].map((tab) => (
                    <button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id)}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter' || e.key === ' ') {
                          e.preventDefault()
                          setActiveTab(tab.id)
                        }
                        // Arrow key navigation
                        if (e.key === 'ArrowLeft' || e.key === 'ArrowRight') {
                          e.preventDefault()
                          const tabs = ['overview', 'reviews']
                          const currentIndex = tabs.indexOf(tab.id)
                          const nextIndex = e.key === 'ArrowRight'
                            ? (currentIndex + 1) % tabs.length
                            : (currentIndex - 1 + tabs.length) % tabs.length
                          setActiveTab(tabs[nextIndex])
                        }
                      }}
                      className={`flex-1 flex items-center justify-center gap-2 px-6 py-3 rounded-xl font-medium transition-all duration-200 ${
                        activeTab === tab.id ? 'shadow-lg' : ''
                      }`}
                      style={{
                        backgroundColor: activeTab === tab.id ? 'var(--primary)' : 'transparent',
                        color: activeTab === tab.id ? 'var(--bg-primary)' : 'var(--text-secondary)',
                        transform: activeTab === tab.id ? 'translateY(-1px)' : 'translateY(0)'
                      }}
                      role="tab"
                      aria-selected={activeTab === tab.id}
                      aria-controls={`${tab.id}-panel`}
                      tabIndex={activeTab === tab.id ? 0 : -1}
                      onMouseEnter={(e) => {
                        if (activeTab !== tab.id) {
                          e.currentTarget.style.backgroundColor = 'var(--bg-accent)'
                        }
                      }}
                      onMouseLeave={(e) => {
                        if (activeTab !== tab.id) {
                          e.currentTarget.style.backgroundColor = 'transparent'
                        }
                      }}
                      onFocus={(e) => {
                        e.currentTarget.style.boxShadow = '0 0 0 3px rgba(var(--primary-rgb), 0.3)'
                      }}
                      onBlur={(e) => {
                        e.currentTarget.style.boxShadow = 'none'
                      }}
                    >
                      <tab.icon className="h-4 w-4" aria-hidden="true" />
                      {tab.label}
                    </button>
                  ))}
                </nav>
              </div>

              {/* Tab Content */}
              {activeTab === 'overview' && (
                <div id="overview-panel" role="tabpanel" aria-labelledby="overview-tab">
                  <div className="text-center py-8">
                    <p className="text-lg macos-body" style={{ color: 'var(--text-secondary)' }}>
                      Overview information is displayed above in the main profile section.
                    </p>
                  </div>
                </div>
              )}

              {activeTab === 'reviews' && (
                <div id="reviews-panel" role="tabpanel" aria-labelledby="reviews-tab">
                  {/* Reviews and Ratings Section */}
                  <div className="space-y-8">
                    {/* Rating Summary */}
                    {ratingStats && (
                      <div className="p-8 rounded-3xl" style={{
                        backgroundColor: 'var(--bg-secondary)',
                        border: '1px solid var(--border-light)',
                        boxShadow: 'var(--shadow-card)'
                      }}>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                          {/* Overall Rating */}
                          <div className="text-center">
                            <div className="text-5xl font-light mb-2" style={{ color: 'var(--text-primary)' }}>
                              {ratingStats.average.toFixed(1)}
                            </div>
                            <div className="flex items-center justify-center gap-1 mb-2">
                              {[1, 2, 3, 4, 5].map((star) => (
                                <Star
                                  key={star}
                                  className="h-6 w-6"
                                  style={{
                                    color: star <= Math.round(ratingStats.average) ? 'var(--accent-warning)' : 'var(--border-light)',
                                    fill: star <= Math.round(ratingStats.average) ? 'var(--accent-warning)' : 'none'
                                  }}
                                  aria-hidden="true"
                                />
                              ))}
                            </div>
                            <p className="text-sm" style={{ color: 'var(--text-secondary)' }}>
                              Based on {ratingStats.total} reviews
                            </p>
                          </div>

                          {/* Rating Distribution */}
                          <div className="space-y-2">
                            {ratingStats.distribution.map((rating) => (
                              <div key={rating.stars} className="flex items-center gap-3">
                                <span className="text-sm w-8" style={{ color: 'var(--text-secondary)' }}>
                                  {rating.stars}★
                                </span>
                                <div className="flex-1 h-2 rounded-full" style={{ backgroundColor: 'var(--border-light)' }}>
                                  <div
                                    className="h-2 rounded-full transition-all duration-500"
                                    style={{
                                      backgroundColor: 'var(--accent-warning)',
                                      width: `${rating.percentage}%`
                                    }}
                                  />
                                </div>
                                <span className="text-sm w-8 text-right" style={{ color: 'var(--text-secondary)' }}>
                                  {rating.count}
                                </span>
                              </div>
                            ))}
                          </div>
                        </div>
                      </div>
                    )}

                    {/* Individual Reviews */}
                    <div className="space-y-6">
                      <div className="flex items-center justify-between">
                        <h3 className="text-xl font-semibold" style={{ color: 'var(--text-primary)' }}>
                          Recent Reviews
                        </h3>
                        {reviews.length > 3 && (
                          <button
                            onClick={() => setShowAllReviews(!showAllReviews)}
                            className="text-sm font-medium px-4 py-2 rounded-lg transition-all duration-200"
                            style={{
                              color: 'var(--primary)',
                              backgroundColor: 'var(--bg-accent)',
                              border: '1px solid var(--border-light)'
                            }}
                            onMouseEnter={(e) => {
                              e.currentTarget.style.backgroundColor = 'var(--primary)'
                              e.currentTarget.style.color = 'var(--bg-primary)'
                            }}
                            onMouseLeave={(e) => {
                              e.currentTarget.style.backgroundColor = 'var(--bg-accent)'
                              e.currentTarget.style.color = 'var(--primary)'
                            }}
                          >
                            {showAllReviews ? 'Show Less' : 'Show All Reviews'}
                          </button>
                        )}
                      </div>

                      {reviewsLoading ? (
                        <div className="space-y-4">
                          {[1, 2, 3].map((i) => (
                            <div key={i} className="p-6 rounded-2xl animate-pulse" style={{
                              backgroundColor: 'var(--bg-primary)',
                              border: '1px solid var(--border-light)'
                            }}>
                              <div className="flex items-start gap-4">
                                <div className="w-12 h-12 rounded-full" style={{ backgroundColor: 'var(--border-light)' }}></div>
                                <div className="flex-1 space-y-2">
                                  <div className="h-4 w-32 rounded" style={{ backgroundColor: 'var(--border-light)' }}></div>
                                  <div className="h-3 w-full rounded" style={{ backgroundColor: 'var(--border-light)' }}></div>
                                  <div className="h-3 w-3/4 rounded" style={{ backgroundColor: 'var(--border-light)' }}></div>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      ) : reviews.length > 0 ? (
                        <div className="space-y-4">
                          {(showAllReviews ? reviews : reviews.slice(0, 3)).map((review) => (
                            <article
                              key={review.id}
                              className="p-6 rounded-2xl transition-all duration-200"
                              style={{
                                backgroundColor: 'var(--bg-primary)',
                                border: '1px solid var(--border-light)',
                                boxShadow: 'var(--shadow-card)'
                              }}
                            >
                              <div className="flex items-start gap-4">
                                <div className="w-12 h-12 rounded-full flex items-center justify-center" style={{
                                  backgroundColor: 'var(--primary)',
                                  color: 'var(--bg-primary)'
                                }}>
                                  {review.reviewer_name.charAt(0).toUpperCase()}
                                </div>
                                <div className="flex-1">
                                  <div className="flex items-center justify-between mb-2">
                                    <h4 className="font-semibold" style={{ color: 'var(--text-primary)' }}>
                                      {review.reviewer_name}
                                    </h4>
                                    <time className="text-sm" style={{ color: 'var(--text-secondary)' }}>
                                      {new Date(review.created_at).toLocaleDateString()}
                                    </time>
                                  </div>
                                  <div className="flex items-center gap-1 mb-3">
                                    {[1, 2, 3, 4, 5].map((star) => (
                                      <Star
                                        key={star}
                                        className="h-4 w-4"
                                        style={{
                                          color: star <= review.rating ? 'var(--accent-warning)' : 'var(--border-light)',
                                          fill: star <= review.rating ? 'var(--accent-warning)' : 'none'
                                        }}
                                        aria-hidden="true"
                                      />
                                    ))}
                                  </div>
                                  <p className="text-base leading-relaxed mb-3" style={{ color: 'var(--text-secondary)' }}>
                                    {review.review_text}
                                  </p>
                                  {review.helpful_count && review.helpful_count > 0 && (
                                    <div className="flex items-center gap-2 text-sm" style={{ color: 'var(--text-muted)' }}>
                                      <CheckCircle className="h-4 w-4" aria-hidden="true" />
                                      {review.helpful_count} people found this helpful
                                    </div>
                                  )}
                                </div>
                              </div>
                            </article>
                          ))}
                        </div>
                      ) : (
                        <div className="text-center py-12 p-8 rounded-2xl" style={{
                          backgroundColor: 'var(--bg-primary)',
                          border: '1px solid var(--border-light)'
                        }}>
                          <Star className="h-12 w-12 mx-auto mb-4 opacity-50" style={{ color: 'var(--text-secondary)' }} aria-hidden="true" />
                          <h3 className="text-lg font-semibold mb-2" style={{ color: 'var(--text-primary)' }}>
                            No Reviews Yet
                          </h3>
                          <p className="text-base" style={{ color: 'var(--text-secondary)' }}>
                            Be the first to leave a review for this provider.
                          </p>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              )}
            </div>
          </section>

          {/* Apple Mac Desktop Booking Panel */}
          <aside className="xl:col-span-1" aria-label="Booking appointment" role="complementary">
            <div className="rounded-3xl p-10 sticky top-8 animate-fadeInUp" style={{
              backgroundColor: 'var(--bg-primary)',
              border: '1px solid var(--border-light)',
              boxShadow: 'var(--shadow-large)',
              backdropFilter: 'blur(10px)'
            }} role="form" aria-label="Book appointment form">
              <h2 className="text-3xl font-light mb-8 macos-title" style={{
                color: 'var(--text-primary)',
                fontWeight: '300',
                letterSpacing: '-0.02em'
              }}>Book Appointment</h2>

              {/* Apple Mac Desktop Progress Indicator */}
              <div className="flex items-center justify-between mb-10" role="progressbar" aria-valuenow={bookingStep} aria-valuemin={1} aria-valuemax={4} aria-label={`Booking step ${bookingStep} of 4`}>
                {[1, 2, 3, 4].map((step) => (
                  <div key={step} className="flex items-center">
                    <div
                      className="w-12 h-12 rounded-2xl flex items-center justify-center text-base font-semibold transition-all duration-300 shadow-lg"
                        style={{
                        backgroundColor: step <= bookingStep ? 'var(--primary)' : 'var(--bg-secondary)',
                        color: step <= bookingStep ? 'var(--bg-primary)' : 'var(--text-secondary)',
                        boxShadow: step <= bookingStep ? 'var(--shadow-large)' : 'var(--shadow-card)',
                        transform: step <= bookingStep ? 'scale(1.1)' : 'scale(1)'
                      }}
                    >
                      {step}
                    </div>
                    {step < 4 && (
                      <div
                        className="w-12 h-1 mx-3 rounded-full"
                        style={{
                          backgroundColor: step < bookingStep ? 'var(--primary)' : 'var(--border-light)',
                          transition: 'all 0.3s ease'
                        }}
                      />
                    )}
                  </div>
                ))}
              </div>

              {/* Error Display */}
              {bookingError && (
                <div
                  className="mb-4 p-3 rounded-lg border"
                  style={{
                    backgroundColor: 'var(--bg-error)',
                    borderColor: 'var(--border-error)',
                    color: 'var(--text-error)',
                    boxShadow: 'var(--shadow-sm)'
                  }}
                  role="alert"
                  aria-live="polite"
                >
                  {bookingError}
                </div>
              )}
              
              {bookingStep === 1 && (
                <div>
                  <label htmlFor="booking-date" className="block text-lg font-medium mb-4 macos-body" style={{
                    color: 'var(--text-primary)',
                    fontWeight: '500',
                    letterSpacing: '-0.005em'
                  }}>
                    Select Date
                  </label>
                  <input
                    id="booking-date"
                    type="date"
                    value={selectedDate}
                    onChange={(e) => setSelectedDate(e.target.value)}
                    min={new Date().toISOString().split('T')[0]}
                    className="w-full p-6 rounded-2xl mb-6 transition-all duration-300 focus:scale-105"
                    style={{
                      borderColor: 'var(--border-light)',
                      backgroundColor: 'var(--bg-primary)',
                      color: 'var(--text-primary)',
                      border: '1px solid var(--border-light)',
                      boxShadow: 'var(--shadow-card)',
                      fontSize: '1.1rem',
                      fontWeight: '400'
                    }}
                    required
                    aria-describedby="date-help"
                    aria-invalid={!selectedDate && bookingError ? 'true' : 'false'}
                    onFocus={(e) => {
                      e.currentTarget.style.borderColor = 'var(--primary)'
                      e.currentTarget.style.boxShadow = 'var(--focus-shadow)'
                    }}
                    onBlur={(e) => {
                      e.currentTarget.style.borderColor = 'var(--border-light)'
                      e.currentTarget.style.boxShadow = 'var(--shadow-card)'
                    }}
                  />
                  <div id="date-help" className="text-sm mb-4" style={{ color: 'var(--text-secondary)' }}>
                    Select your preferred appointment date. Appointments can be booked up to 30 days in advance.
                  </div>
                  <button
                    onClick={handleBookAppointment}
                    disabled={!selectedDate}
                    className="w-full py-5 px-6 rounded-2xl font-medium mb-4 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 hover:scale-105 macos-body"
                    style={{
                      backgroundColor: selectedDate ? 'var(--primary)' : 'var(--text-muted)',
                      color: 'var(--bg-primary)',
                      boxShadow: selectedDate ? 'var(--shadow-large)' : 'var(--shadow-card)',
                      fontSize: '1.1rem',
                      fontWeight: '500',
                      letterSpacing: '-0.005em'
                    }}
                    onMouseEnter={(e) => !e.currentTarget.disabled && (e.currentTarget.style.opacity = '0.9')}
                    onMouseLeave={(e) => !e.currentTarget.disabled && (e.currentTarget.style.opacity = '1')}
                  >
                    Continue
                  </button>
                </div>
              )}

              {bookingStep === 2 && (
                <div>
                  <button
                    onClick={() => setBookingStep(1)}
                    className="text-base mb-6 flex items-center gap-2 transition-all duration-300 hover:scale-105 macos-body"
                    style={{
                      color: 'var(--text-secondary)',
                      fontWeight: '500',
                      letterSpacing: '-0.005em'
                    }}
                    onMouseEnter={(e) => e.currentTarget.style.color = 'var(--primary)'}
                    onMouseLeave={(e) => e.currentTarget.style.color = 'var(--text-secondary)'}
                  >
                    <ArrowLeft className="h-5 w-5" />
                    Change date
                  </button>
                  <p className="text-lg mb-6 macos-body" style={{
                    color: 'var(--text-secondary)',
                    fontWeight: '400',
                    letterSpacing: '-0.005em'
                  }}>Date: {selectedDate}</p>
                  <label className="block text-lg font-medium mb-4 macos-body" style={{
                    color: 'var(--text-primary)',
                    fontWeight: '500',
                    letterSpacing: '-0.005em'
                  }}>
                    Select Time
                  </label>
                  <div className="grid grid-cols-2 gap-4 mb-6">
                    {availableTimeSlots.map((slot) => (
                      <button
                        key={slot}
                        onClick={() => setSelectedTime(slot)}
                        className="p-4 text-base rounded-2xl transition-all duration-300 hover:scale-105 macos-body"
                        aria-label={`Select ${slot} appointment time`}
                        aria-pressed={selectedTime === slot}
                        style={{
                          backgroundColor: selectedTime === slot ? 'var(--primary)' : 'var(--bg-primary)',
                          color: selectedTime === slot ? 'var(--bg-primary)' : 'var(--text-primary)',
                          border: '1px solid ' + (selectedTime === slot ? 'var(--primary)' : 'var(--border-light)'),
                          boxShadow: selectedTime === slot ? 'var(--shadow-large)' : 'var(--shadow-card)',
                          fontWeight: '500',
                          letterSpacing: '-0.005em'
                        }}
                        onMouseEnter={(e) => {
                          if (selectedTime !== slot) {
                            e.currentTarget.style.borderColor = 'var(--primary)'
                            e.currentTarget.style.boxShadow = 'var(--shadow-large)'
                          }
                        }}
                        onMouseLeave={(e) => {
                          if (selectedTime !== slot) {
                            e.currentTarget.style.borderColor = 'var(--border-light)'
                            e.currentTarget.style.boxShadow = 'var(--shadow-card)'
                          }
                        }}
                      >
                        {slot}
                      </button>
                    ))}
                  </div>
                  <button
                    onClick={handleBookAppointment}
                    disabled={!selectedTime}
                    className="w-full py-5 px-6 rounded-2xl font-medium mb-4 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 hover:scale-105 macos-body"
                    style={{
                      backgroundColor: selectedTime ? 'var(--primary)' : 'var(--text-muted)',
                      color: 'var(--bg-primary)',
                      boxShadow: selectedTime ? 'var(--shadow-large)' : 'var(--shadow-card)',
                      fontSize: '1.1rem',
                      fontWeight: '500',
                      letterSpacing: '-0.005em'
                    }}
                    onMouseEnter={(e) => !e.currentTarget.disabled && (e.currentTarget.style.opacity = '0.9')}
                    onMouseLeave={(e) => !e.currentTarget.disabled && (e.currentTarget.style.opacity = '1')}
                  >
                    Book Appointment
                  </button>
                </div>
              )}

              {bookingStep === 3 && (
                <div>
                  <button
                    onClick={() => setBookingStep(2)}
                    className="text-base mb-6 flex items-center gap-2 transition-all duration-300 hover:scale-105 macos-body"
                    style={{
                      color: 'var(--text-secondary)',
                      fontWeight: '500',
                      letterSpacing: '-0.005em'
                    }}
                    onMouseEnter={(e) => e.currentTarget.style.color = 'var(--primary)'}
                    onMouseLeave={(e) => e.currentTarget.style.color = 'var(--text-secondary)'}
                  >
                    <ArrowLeft className="h-5 w-5" />
                    Change time
                  </button>
                  <h3 className="text-2xl font-semibold mb-6 macos-title" style={{
                    color: 'var(--text-primary)',
                    fontWeight: '600',
                    letterSpacing: '-0.01em'
                  }}>Payment Information</h3>
                  <div className="p-6 rounded-2xl mb-6" style={{
                    backgroundColor: 'var(--bg-secondary)',
                    border: '1px solid var(--border-light)',
                    boxShadow: 'var(--shadow-card)'
                  }}>
                    <p className="text-lg mb-2 macos-body" style={{
                      color: 'var(--text-secondary)',
                      fontWeight: '400',
                      letterSpacing: '-0.005em'
                    }}>
                      Appointment: {selectedDate} at {selectedTime}
                    </p>
                    <p className="text-xl font-medium macos-body" style={{
                      color: 'var(--text-primary)',
                      fontWeight: '600',
                      letterSpacing: '-0.005em'
                    }}>
                      Total: ${provider?.hourly_rate ? `${provider.hourly_rate}.00` : 'Contact for pricing'}
                    </p>
                  </div>

                  <label className="block text-lg font-medium mb-4 macos-body" style={{
                    color: 'var(--text-primary)',
                    fontWeight: '500',
                    letterSpacing: '-0.005em'
                  }}>
                    Payment Method
                  </label>
                  <select
                    value={paymentMethod}
                    onChange={(e) => setPaymentMethod(e.target.value)}
                    className="w-full p-6 rounded-2xl mb-6 transition-all duration-300 focus:scale-105 macos-body"
                    style={{
                      border: '1px solid var(--border-light)',
                      backgroundColor: 'var(--bg-primary)',
                      color: 'var(--text-primary)',
                      boxShadow: 'var(--shadow-card)',
                      fontSize: '1.1rem',
                      fontWeight: '400'
                    }}
                    onFocus={(e) => {
                      e.currentTarget.style.borderColor = 'var(--primary)'
                      e.currentTarget.style.boxShadow = 'var(--focus-shadow)'
                    }}
                    onBlur={(e) => {
                      e.currentTarget.style.borderColor = 'var(--border-light)'
                      e.currentTarget.style.boxShadow = 'var(--shadow-card)'
                    }}
                  >
                    <option value="">Select payment method</option>
                    <option value="credit_card">Credit Card</option>
                    <option value="debit_card">Debit Card</option>
                    <option value="paypal">PayPal</option>
                    <option value="insurance">Insurance</option>
                  </select>

                  <button
                    onClick={handleBookAppointment}
                    disabled={!paymentMethod || isBooking}
                    className="w-full py-5 px-6 rounded-2xl font-medium mb-4 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 hover:scale-105 flex items-center justify-center gap-3 macos-body"
                    style={{
                      backgroundColor: (paymentMethod && !isBooking) ? 'var(--primary)' : 'var(--text-muted)',
                      color: 'var(--bg-primary)',
                      boxShadow: (paymentMethod && !isBooking) ? 'var(--shadow-large)' : 'var(--shadow-card)',
                      fontSize: '1.1rem',
                      fontWeight: '500',
                      letterSpacing: '-0.005em'
                    }}
                    onMouseEnter={(e) => !e.currentTarget.disabled && (e.currentTarget.style.opacity = '0.9')}
                    onMouseLeave={(e) => !e.currentTarget.disabled && (e.currentTarget.style.opacity = '1')}
                    aria-label={isBooking ? 'Processing booking...' : 'Confirm and pay for appointment'}
                  >
                    {isBooking ? (
                      <>
                        <div className="w-6 h-6 border-3 border-white border-t-transparent rounded-full animate-spin"></div>
                        Processing...
                      </>
                    ) : (
                      'Confirm & Pay'
                    )}
                  </button>
                </div>
              )}

              {bookingStep === 4 && (
                <div className="text-center">
                  <div className="w-20 h-20 rounded-3xl mx-auto mb-6 flex items-center justify-center" style={{
                    backgroundColor: 'var(--primary)',
                    boxShadow: 'var(--shadow-large)'
                  }}>
                    <CheckCircle className="h-12 w-12" style={{ color: 'var(--bg-primary)' }} />
                  </div>
                  <h3 className="text-3xl font-semibold mb-6 macos-title" style={{
                    color: 'var(--text-primary)',
                    fontWeight: '600',
                    letterSpacing: '-0.01em'
                  }}>Booking Confirmed!</h3>
                  <div className="p-6 rounded-2xl mb-6" style={{
                    backgroundColor: 'var(--bg-secondary)',
                    border: '1px solid var(--border-light)',
                    boxShadow: 'var(--shadow-card)'
                  }}>
                    <p className="text-lg mb-3 macos-body" style={{
                      color: 'var(--text-secondary)',
                      fontWeight: '400',
                      letterSpacing: '-0.005em'
                    }}>
                      Your appointment with {provider.name} is scheduled for {selectedDate} at {selectedTime}.
                    </p>
                    <p className="text-lg macos-body" style={{
                      color: 'var(--text-secondary)',
                      fontWeight: '400',
                      letterSpacing: '-0.005em'
                    }}>
                      Payment method: {paymentMethod?.replace('_', ' ')}
                    </p>
                  </div>
                  <button
                    onClick={() => navigate(-1)}
                    className="w-full py-5 px-6 rounded-2xl font-medium transition-all duration-300 hover:scale-105 macos-body"
                    style={{
                      backgroundColor: 'var(--primary)',
                      color: 'var(--bg-primary)',
                      boxShadow: 'var(--shadow-large)',
                      fontSize: '1.1rem',
                      fontWeight: '500',
                      letterSpacing: '-0.005em'
                    }}
                    onMouseEnter={(e) => e.currentTarget.style.opacity = '0.9'}
                    onMouseLeave={(e) => e.currentTarget.style.opacity = '1'}
                  >
                    Back to Search
                  </button>
                </div>
              )}

              {bookingStep < 4 && (
                <>
                  <button
                    onClick={handleSendMessage}
                    className="w-full py-2 px-4 rounded-lg font-medium mb-3 flex items-center justify-center gap-2 transition-colors"
                    style={{
                      backgroundColor: 'var(--bg-primary)',
                      color: 'var(--primary)',
                      border: '1px solid var(--primary)'
                    }}
                    onMouseEnter={(e) => e.currentTarget.style.backgroundColor = 'var(--bg-accent)'}
                    onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'var(--bg-primary)'}
                  >
                    <MessageCircle className="h-4 w-4" />
                    Send Message
                  </button>

                  <div className="text-xs text-center" style={{ color: 'var(--text-muted)' }}>
                    <Clock className="h-3 w-3 inline mr-1" />
                    Usually responds within 2 hours
                  </div>
                </>
              )}
            </div>
          </aside>
        </div>
      </div>

      {/* Message Modal */}
      {showMessageModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4" onClick={() => setShowMessageModal(false)}>
          <div className="bg-white rounded-3xl p-8 max-w-md w-full" style={{
            backgroundColor: 'var(--bg-primary)',
            border: '1px solid var(--border-light)',
            boxShadow: 'var(--shadow-large)'
          }} onClick={(e) => e.stopPropagation()}>
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-2xl font-semibold" style={{ color: 'var(--text-primary)' }}>
                Send Message to {provider?.name}
              </h3>
              <button
                onClick={() => setShowMessageModal(false)}
                className="p-2 rounded-xl transition-all duration-200"
                style={{
                  color: 'var(--text-secondary)',
                  backgroundColor: 'var(--bg-secondary)'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.backgroundColor = 'var(--bg-accent)'
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = 'var(--bg-secondary)'
                }}
                aria-label="Close message modal"
              >
                ✕
              </button>
            </div>

            <div className="mb-6">
              <label htmlFor="message-text" className="block text-sm font-medium mb-3" style={{ color: 'var(--text-primary)' }}>
                Your Message
              </label>
              <textarea
                id="message-text"
                value={messageText}
                onChange={(e) => setMessageText(e.target.value)}
                placeholder="Hi! I'm interested in your caregiving services..."
                rows={4}
                className="w-full p-4 rounded-2xl resize-none transition-all duration-200"
                style={{
                  backgroundColor: 'var(--bg-secondary)',
                  border: '1px solid var(--border-light)',
                  color: 'var(--text-primary)',
                  boxShadow: 'var(--shadow-card)'
                }}
                onFocus={(e) => {
                  e.currentTarget.style.borderColor = 'var(--primary)'
                  e.currentTarget.style.boxShadow = 'var(--focus-shadow)'
                }}
                onBlur={(e) => {
                  e.currentTarget.style.borderColor = 'var(--border-light)'
                  e.currentTarget.style.boxShadow = 'var(--shadow-card)'
                }}
                maxLength={500}
                required
                aria-describedby="message-help"
              />
              <div id="message-help" className="text-xs mt-2" style={{ color: 'var(--text-secondary)' }}>
                {messageText.length}/500 characters
              </div>
            </div>

            <div className="flex gap-3">
              <button
                onClick={() => setShowMessageModal(false)}
                className="flex-1 py-3 px-4 rounded-xl font-medium transition-all duration-200"
                style={{
                  backgroundColor: 'var(--bg-secondary)',
                  color: 'var(--text-secondary)',
                  border: '1px solid var(--border-light)'
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.backgroundColor = 'var(--bg-accent)'
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = 'var(--bg-secondary)'
                }}
              >
                Cancel
              </button>
              <button
                onClick={handleSendMessage}
                disabled={!messageText.trim() || messageSending}
                className="flex-1 py-3 px-4 rounded-xl font-medium transition-all duration-200 flex items-center justify-center gap-2"
                style={{
                  backgroundColor: messageText.trim() && !messageSending ? 'var(--primary)' : 'var(--text-muted)',
                  color: 'var(--bg-primary)',
                  opacity: messageSending ? 0.7 : 1,
                  cursor: !messageText.trim() || messageSending ? 'not-allowed' : 'pointer'
                }}
                onMouseEnter={(e) => {
                  if (messageText.trim() && !messageSending) {
                    e.currentTarget.style.opacity = '0.9'
                  }
                }}
                onMouseLeave={(e) => {
                  if (messageText.trim() && !messageSending) {
                    e.currentTarget.style.opacity = '1'
                  }
                }}
              >
                {messageSending ? (
                  <>
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                    Sending...
                  </>
                ) : (
                  <>
                    <MessageCircle className="h-4 w-4" aria-hidden="true" />
                    Send Message
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      )}
    </main>
  )
}
