import React, { useState, useEffect, useRef } from 'react'
import { supabase } from '../lib/supabase'

interface Message {
  id: string
  sender_id: string
  receiver_id: string
  content: string
  message_type: 'text' | 'image' | 'file'
  read_status: boolean
  created_at: string
  updated_at: string
  sender?: Profile
  receiver?: Profile
}

interface Conversation {
  id: string
  participant_1_id: string
  participant_2_id: string
  created_at: string
  updated_at: string
  last_message?: Message
  participant_1?: Profile
  participant_2?: Profile
  unread_count?: number
}

interface Profile {
  id: string
  email: string
  full_name: string | null
  avatar_url: string | null
  role: string
}

const MessagingSystem: React.FC = () => {
  const [conversations, setConversations] = useState<Conversation[]>([])
  const [messages, setMessages] = useState<Message[]>([])
  const [selectedConversation, setSelectedConversation] = useState<Conversation | null>(null)
  const [newMessage, setNewMessage] = useState('')
  const [loading, setLoading] = useState(true)
  const [messagesLoading, setMessagesLoading] = useState(false)
  const [currentUser, setCurrentUser] = useState<any>(null)
  const [searchQuery, setSearchQuery] = useState('')
  const messagesEndRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    getCurrentUser()
  }, [])

  useEffect(() => {
    if (currentUser) {
      loadConversations()
    }
  }, [currentUser])

  useEffect(() => {
    if (selectedConversation) {
      loadMessages(selectedConversation.id)
    }
  }, [selectedConversation])

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  const getCurrentUser = async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser()
      setCurrentUser(user)
    } catch (error) {
      console.error('Error getting current user:', error)
    }
  }

  const loadConversations = async () => {
    if (!currentUser) return

    try {
      // Load conversations where current user is a participant
      const { data: conversationsData, error } = await supabase
        .schema('care_connector')
        .from('conversations')
        .select(`
          *,
          participant_1:participant_1_id(id, email, full_name, avatar_url, role),
          participant_2:participant_2_id(id, email, full_name, avatar_url, role)
        `)
        .or(`participant_1_id.eq.${currentUser.id},participant_2_id.eq.${currentUser.id}`)
        .order('updated_at', { ascending: false })

      if (error) throw error

      // Load last message and unread count for each conversation
      const conversationsWithDetails = await Promise.all(
        (conversationsData || []).map(async (conv) => {
          // Get last message
          const { data: lastMessage } = await supabase
            .schema('care_connector')
            .from('messages')
            .select(`
              *,
              sender:sender_id(id, email, full_name, avatar_url, role)
            `)
            .eq('conversation_id', conv.id)
            .order('created_at', { ascending: false })
            .limit(1)
            .single()

          // Get unread count for current user
          const { count: unreadCount } = await supabase
            .schema('care_connector')
            .from('messages')
            .select('*', { count: 'exact' })
            .eq('conversation_id', conv.id)
            .eq('receiver_id', currentUser.id)
            .eq('read_status', false)

          return {
            ...conv,
            last_message: lastMessage,
            unread_count: unreadCount || 0
          }
        })
      )

      setConversations(conversationsWithDetails)
    } catch (error) {
      console.error('Error loading conversations:', error)
    } finally {
      setLoading(false)
    }
  }

  const loadMessages = async (conversationId: string) => {
    setMessagesLoading(true)
    try {
      const { data, error } = await supabase
        .schema('care_connector')
        .from('messages')
        .select(`
          *,
          sender:sender_id(id, email, full_name, avatar_url, role),
          receiver:receiver_id(id, email, full_name, avatar_url, role)
        `)
        .eq('conversation_id', conversationId)
        .order('created_at', { ascending: true })

      if (error) throw error
      setMessages(data || [])

      // Mark messages as read
      await markMessagesAsRead(conversationId)
    } catch (error) {
      console.error('Error loading messages:', error)
    } finally {
      setMessagesLoading(false)
    }
  }

  const markMessagesAsRead = async (conversationId: string) => {
    if (!currentUser) return

    try {
      await supabase
        .schema('care_connector')
        .from('messages')
        .update({ read_status: true })
        .eq('conversation_id', conversationId)
        .eq('receiver_id', currentUser.id)
        .eq('read_status', false)

      // Refresh conversations to update unread counts
      loadConversations()
    } catch (error) {
      console.error('Error marking messages as read:', error)
    }
  }

  const sendMessage = async () => {
    if (!newMessage.trim() || !selectedConversation || !currentUser) return

    const otherParticipant = selectedConversation.participant_1_id === currentUser.id 
      ? selectedConversation.participant_2_id 
      : selectedConversation.participant_1_id

    try {
      const { data, error } = await supabase
        .schema('care_connector')
        .from('messages')
        .insert({
          conversation_id: selectedConversation.id,
          sender_id: currentUser.id,
          receiver_id: otherParticipant,
          content: newMessage.trim(),
          message_type: 'text',
          read_status: false
        })
        .select(`
          *,
          sender:sender_id(id, email, full_name, avatar_url, role),
          receiver:receiver_id(id, email, full_name, avatar_url, role)
        `)
        .single()

      if (error) throw error

      // Add message to current messages
      setMessages(prev => [...prev, data])
      setNewMessage('')

      // Update conversation timestamp
      await supabase
        .schema('care_connector')
        .from('conversations')
        .update({ updated_at: new Date().toISOString() })
        .eq('id', selectedConversation.id)

      // Refresh conversations
      loadConversations()
    } catch (error) {
      console.error('Error sending message:', error)
    }
  }

  const startNewConversation = async (participantId: string) => {
    if (!currentUser) return

    try {
      // Check if conversation already exists
      const { data: existingConv } = await supabase
        .schema('care_connector')
        .from('conversations')
        .select('*')
        .or(
          `and(participant_1_id.eq.${currentUser.id},participant_2_id.eq.${participantId}),` +
          `and(participant_1_id.eq.${participantId},participant_2_id.eq.${currentUser.id})`
        )
        .single()

      if (existingConv) {
        // Conversation exists, select it
        const fullConv = conversations.find(c => c.id === existingConv.id)
        if (fullConv) {
          setSelectedConversation(fullConv)
        }
        return
      }

      // Create new conversation
      const { data, error } = await supabase
        .schema('care_connector')
        .from('conversations')
        .insert({
          participant_1_id: currentUser.id,
          participant_2_id: participantId
        })
        .select(`
          *,
          participant_1:participant_1_id(id, email, full_name, avatar_url, role),
          participant_2:participant_2_id(id, email, full_name, avatar_url, role)
        `)
        .single()

      if (error) throw error

      setConversations(prev => [{ ...data, unread_count: 0 }, ...prev])
      setSelectedConversation({ ...data, unread_count: 0 })
    } catch (error) {
      console.error('Error starting new conversation:', error)
    }
  }

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  const filteredConversations = conversations.filter(conv => {
    if (!searchQuery) return true
    
    const otherParticipant = conv.participant_1_id === currentUser?.id 
      ? conv.participant_2 
      : conv.participant_1

    return (
      otherParticipant?.full_name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      otherParticipant?.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
      conv.last_message?.content.toLowerCase().includes(searchQuery.toLowerCase())
    )
  })

  const formatMessageTime = (timestamp: string) => {
    const date = new Date(timestamp)
    const now = new Date()
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60)

    if (diffInHours < 24) {
      return date.toLocaleTimeString('en-US', { 
        hour: 'numeric', 
        minute: '2-digit',
        hour12: true 
      })
    } else if (diffInHours < 48) {
      return 'Yesterday'
    } else {
      return date.toLocaleDateString('en-US', { 
        month: 'short', 
        day: 'numeric' 
      })
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center" style={{ backgroundColor: 'var(--bg-secondary)' }}>
        <div className="text-center">
          <div className="inline-flex items-center gap-5 px-12 py-8 rounded-3xl" style={{
            backgroundColor: 'var(--bg-primary)',
            border: '1px solid var(--border-light)',
            boxShadow: 'var(--shadow-large)',
            backdropFilter: 'blur(10px)'
          }}>
            <div className="w-10 h-10 rounded-full animate-spin" style={{
              border: '3px solid var(--border-light)',
              borderTop: '3px solid var(--primary)'
            }}></div>
            <div>
              <div className="text-2xl font-semibold mb-2 macos-title" style={{
                color: 'var(--text-primary)',
                fontWeight: '600',
                letterSpacing: '-0.01em'
              }}>Loading Messages</div>
              <div className="text-lg macos-body" style={{
                color: 'var(--text-secondary)',
                fontWeight: '400',
                letterSpacing: '-0.005em'
              }}>Connecting to secure messaging system...</div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="h-screen flex flex-col md:flex-row" style={{backgroundColor: 'var(--bg-secondary)'}}>
      {/* Enhanced Conversations Sidebar - Mobile Optimized */}
      <div className={`${selectedConversation ? 'hidden md:flex' : 'flex'} w-full md:w-1/3 flex-col`} style={{
        borderRight: '1px solid var(--border-light)',
        backgroundColor: 'var(--bg-primary)',
        boxShadow: 'var(--shadow-large)',
        backdropFilter: 'blur(10px)'
      }}>
        <div className="p-8" style={{ borderBottom: '1px solid var(--border-light)' }}>
          <h1 className="text-4xl font-semibold tracking-tight leading-tight mb-8 macos-title" style={{
            color: 'var(--text-primary)',
            fontWeight: '600',
            letterSpacing: '-0.01em'
          }}>Messages</h1>
          <div className="relative">
            <input
              type="text"
              placeholder="Search conversations and messages..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-14 pr-6 py-5 rounded-2xl focus:ring-0 focus:outline-none transition-all duration-300 macos-body"
              style={{
                border: '1px solid var(--border-light)',
                backgroundColor: 'var(--bg-secondary)',
                color: 'var(--text-primary)',
                boxShadow: 'var(--shadow-card)',
                fontSize: '1.1rem',
                fontWeight: '400',
                letterSpacing: '-0.005em'
              }}
              onFocus={(e) => {
                e.currentTarget.style.borderColor = 'var(--primary)'
                e.currentTarget.style.boxShadow = 'var(--shadow-large)'
                e.currentTarget.style.transform = 'scale(1.02)'
              }}
              onBlur={(e) => {
                e.currentTarget.style.borderColor = 'var(--border-light)'
                e.currentTarget.style.boxShadow = 'var(--shadow-card)'
                e.currentTarget.style.transform = 'scale(1)'
              }}
            />
            <svg
              className="absolute left-4 top-1/2 transform -translate-y-1/2 h-6 w-6"
              style={{ color: 'var(--text-muted)' }}
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
        </div>

        <div className="flex-1 overflow-y-auto">
          {filteredConversations.map((conversation) => {
            const otherParticipant = conversation.participant_1_id === currentUser?.id 
              ? conversation.participant_2 
              : conversation.participant_1

            return (
              <div
                key={conversation.id}
                onClick={() => setSelectedConversation(conversation)}
                className="p-6 cursor-pointer transition-all duration-300 hover:scale-105 group mx-4 mb-3"
                style={{
                  backgroundColor: selectedConversation?.id === conversation.id ? 'var(--bg-secondary)' : 'var(--bg-primary)',
                  borderRadius: '1rem',
                  border: '1px solid var(--border-light)',
                  boxShadow: selectedConversation?.id === conversation.id ? 'var(--shadow-large)' : 'var(--shadow-card)',
                  backdropFilter: 'blur(10px)'
                }}
                onMouseEnter={(e) => {
                  if (selectedConversation?.id !== conversation.id) {
                    e.currentTarget.style.backgroundColor = 'var(--bg-secondary)'
                    e.currentTarget.style.boxShadow = 'var(--shadow-large)'
                  }
                }}
                onMouseLeave={(e) => {
                  if (selectedConversation?.id !== conversation.id) {
                    e.currentTarget.style.backgroundColor = 'var(--bg-primary)'
                    e.currentTarget.style.boxShadow = 'var(--shadow-card)'
                  }
                }}
              >
                <div className="flex items-center space-x-5">
                  {otherParticipant?.avatar_url ? (
                    <img
                      src={otherParticipant.avatar_url}
                      alt=""
                      className="h-16 w-16 rounded-2xl object-cover transition-all duration-300"
                      style={{
                        boxShadow: 'var(--shadow-card)',
                        border: '2px solid var(--border-light)'
                      }}
                    />
                  ) : (
                    <div className="h-16 w-16 rounded-2xl flex items-center justify-center text-lg font-semibold transition-all duration-300 macos-body"
                         style={{
                           backgroundColor: 'var(--primary)',
                           color: 'var(--bg-primary)',
                           boxShadow: 'var(--shadow-card)',
                           border: '2px solid var(--border-light)',
                           letterSpacing: '-0.005em'
                         }}>
                      {otherParticipant?.full_name
                        ? otherParticipant.full_name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2)
                        : otherParticipant?.email[0].toUpperCase() || '?'}
                    </div>
                  )}

                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between mb-2">
                      <p className="text-lg font-medium truncate macos-body" style={{
                        color: 'var(--text-primary)',
                        fontWeight: '500',
                        letterSpacing: '-0.005em'
                      }}>
                        {otherParticipant?.full_name || otherParticipant?.email || 'Unknown User'}
                      </p>
                      {conversation.last_message && (
                        <p className="text-sm macos-body" style={{
                          color: 'var(--text-secondary)',
                          fontWeight: '400'
                        }}>
                          {formatMessageTime(conversation.last_message.created_at)}
                        </p>
                      )}
                    </div>
                    <div className="flex items-center justify-between">
                      <p className="text-base truncate macos-body" style={{
                        color: 'var(--text-secondary)',
                        fontWeight: '400',
                        letterSpacing: '-0.005em'
                      }}>
                        {conversation.last_message?.content || 'No messages yet'}
                      </p>
                      {(conversation.unread_count || 0) > 0 && (
                        <span className="inline-flex items-center justify-center px-3 py-1 text-sm font-semibold leading-none rounded-full macos-body"
                              style={{
                                backgroundColor: 'var(--primary)',
                                color: 'var(--bg-primary)',
                                boxShadow: 'var(--shadow-card)',
                                letterSpacing: '-0.005em'
                              }}>
                          {conversation.unread_count}
                        </span>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            )
          })}

          {filteredConversations.length === 0 && (
            <div className="p-12 text-center">
              <div className="mx-auto h-16 w-16 rounded-2xl flex items-center justify-center mb-6 border" style={{
                backgroundColor: 'var(--bg-primary)',
                borderColor: 'var(--primary)',
                boxShadow: 'var(--shadow-light)'
              }}>
                <svg
                  className="h-8 w-8"
                  style={{ color: 'var(--primary)' }}
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                </svg>
              </div>
              <h3 className="text-lg font-medium mb-2" style={{ color: 'var(--text-primary)' }}>
                {searchQuery ? 'No conversations found' : 'No conversations yet'}
              </h3>
              <p className="text-sm leading-relaxed max-w-sm mx-auto" style={{ color: 'var(--text-secondary)' }}>
                {searchQuery 
                  ? 'Try adjusting your search terms or check your message history.' 
                  : 'Start meaningful conversations with caregivers and families in your network.'}
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Messages Area - Mobile Optimized */}
      <div className={`${selectedConversation ? 'flex' : 'hidden md:flex'} flex-1 flex-col`} style={{ backgroundColor: 'var(--bg-secondary)' }}>
        {selectedConversation ? (
          <div className="flex flex-col h-full">
            {/* Chat Header - Mobile Optimized */}
            <div className="p-4 sm:p-6 md:p-8" style={{
              backgroundColor: 'var(--bg-primary)',
              borderBottom: '1px solid var(--border-light)',
              boxShadow: 'var(--shadow-card)',
              backdropFilter: 'blur(10px)'
            }}>
              <div className="flex items-center justify-between">
                {/* Back button for mobile */}
                <button
                  onClick={() => setSelectedConversation(null)}
                  className="md:hidden mr-3 p-2 rounded-lg transition-colors"
                  style={{ color: 'var(--text-secondary)' }}
                  onMouseEnter={(e) => e.currentTarget.style.backgroundColor = 'var(--bg-secondary)'}
                  onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'transparent'}
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                  </svg>
                </button>
                {(() => {
                  const otherParticipant = selectedConversation.participant_1_id === currentUser?.id
                    ? selectedConversation.participant_2
                    : selectedConversation.participant_1

                  return (
                    <div className="flex items-center space-x-3 sm:space-x-5 flex-1">
                      {otherParticipant?.avatar_url ? (
                        <img
                          src={otherParticipant.avatar_url}
                          alt=""
                          className="h-14 w-14 rounded-2xl object-cover"
                          style={{
                            boxShadow: 'var(--shadow-card)',
                            border: '2px solid var(--border-light)'
                          }}
                        />
                      ) : (
                        <div className="h-14 w-14 rounded-2xl flex items-center justify-center text-lg font-semibold macos-body"
                             style={{
                               backgroundColor: 'var(--primary)',
                               color: 'var(--bg-primary)',
                               boxShadow: 'var(--shadow-card)',
                               border: '2px solid var(--border-light)',
                               letterSpacing: '-0.005em'
                             }}>
                          {otherParticipant?.full_name
                            ? otherParticipant.full_name.split(' ').map(n => n[0]).join('').toUpperCase().slice(0, 2)
                            : otherParticipant?.email[0].toUpperCase() || '?'}
                        </div>
                      )}
                      <div>
                        <h2 className="text-2xl font-semibold macos-title" style={{
                          color: 'var(--text-primary)',
                          fontWeight: '600',
                          letterSpacing: '-0.01em'
                        }}>
                          {otherParticipant?.full_name || otherParticipant?.email || 'Unknown User'}
                        </h2>
                        <p className="text-sm capitalize" style={{ color: 'var(--text-secondary)' }}>
                          {otherParticipant?.role?.replace('_', ' ') || 'User'}
                        </p>
                      </div>
                    </div>
                  )
                })()}
              </div>
            </div>

            {/* Messages List */}
            <div className="flex-1 overflow-y-auto p-8 space-y-6">
              {messagesLoading ? (
                <div className="flex justify-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-3 border-primary border-t-transparent" style={{ borderColor: 'var(--primary)' }}></div>
                </div>
              ) : (
                messages.map((message) => {
                  const isFromCurrentUser = message.sender_id === currentUser?.id

                  return (
                    <div
                      key={message.id}
                      className={`flex ${isFromCurrentUser ? 'justify-end' : 'justify-start'}`}
                    >
                      <div className="max-w-xs lg:max-w-md px-6 py-4 rounded-2xl transition-all duration-300 hover:scale-105 macos-body"
                           style={{
                             backgroundColor: isFromCurrentUser ? 'var(--primary)' : 'var(--bg-primary)',
                             border: '1px solid ' + (isFromCurrentUser ? 'var(--primary)' : 'var(--border-light)'),
                             color: isFromCurrentUser ? 'var(--bg-primary)' : 'var(--text-primary)',
                             boxShadow: 'var(--shadow-card)',
                             backdropFilter: 'blur(10px)'
                           }}>
                        <p className="text-base leading-relaxed" style={{
                          fontWeight: '400',
                          letterSpacing: '-0.005em'
                        }}>{message.content}</p>
                        <p className="text-sm mt-2 macos-body"
                           style={{
                             color: isFromCurrentUser ? 'rgba(255, 255, 255, 0.8)' : 'var(--text-secondary)',
                             fontWeight: '400'
                           }}>
                          {formatMessageTime(message.created_at)}
                        </p>
                      </div>
                    </div>
                  )
                })
              )}
              <div ref={messagesEndRef} />
            </div>

            {/* Message Input */}
            <div className="p-8" style={{
              backgroundColor: 'var(--bg-primary)',
              borderTop: '1px solid var(--border-light)',
              boxShadow: 'var(--shadow-card)',
              backdropFilter: 'blur(10px)'
            }}>
              <div className="flex space-x-4">
                <input
                  type="text"
                  value={newMessage}
                  onChange={(e) => setNewMessage(e.target.value)}
                  onKeyDown={(e) => e.key === 'Enter' && !e.shiftKey && sendMessage()}
                  placeholder="Type your message..."
                  className="flex-1 px-6 py-4 rounded-2xl focus:outline-none transition-all duration-300 hover:scale-105 macos-body"
                  style={{
                    border: '1px solid var(--border-light)',
                    backgroundColor: 'var(--bg-secondary)',
                    color: 'var(--text-primary)',
                    boxShadow: 'var(--shadow-card)',
                    fontSize: '1.1rem',
                    fontWeight: '400',
                    letterSpacing: '-0.005em'
                  }}
                  onFocus={(e) => {
                    e.currentTarget.style.borderColor = 'var(--primary)'
                    e.currentTarget.style.boxShadow = 'var(--shadow-large)'
                    e.currentTarget.style.transform = 'scale(1.02)'
                  }}
                  onBlur={(e) => {
                    e.currentTarget.style.borderColor = 'var(--border-light)'
                    e.currentTarget.style.boxShadow = 'var(--shadow-card)'
                    e.currentTarget.style.transform = 'scale(1)'
                  }}
                />
                <button
                  onClick={sendMessage}
                  disabled={!newMessage.trim()}
                  className="px-8 py-4 rounded-2xl disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 hover:scale-105 macos-body"
                  style={{
                    backgroundColor: newMessage.trim() ? 'var(--primary)' : 'var(--text-muted)',
                    color: 'var(--bg-primary)',
                    border: 'none',
                    boxShadow: newMessage.trim() ? 'var(--shadow-large)' : 'var(--shadow-card)',
                    fontSize: '1.1rem',
                    fontWeight: '500',
                    letterSpacing: '-0.005em'
                  }}
                >
                  Send
                </button>
              </div>
            </div>
          </div>
        ) : (
          <div className="flex-1 flex items-center justify-center" style={{ backgroundColor: 'var(--bg-secondary)' }}>
            <div className="text-center p-12 rounded-3xl" style={{
              backgroundColor: 'var(--bg-primary)',
              border: '1px solid var(--border-light)',
              boxShadow: 'var(--shadow-large)',
              backdropFilter: 'blur(10px)'
            }}>
              <div className="w-20 h-20 rounded-3xl mx-auto mb-6 flex items-center justify-center" style={{
                backgroundColor: 'var(--primary)',
                boxShadow: 'var(--shadow-large)'
              }}>
                <svg
                  className="h-10 w-10"
                  style={{ color: 'var(--bg-primary)' }}
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                </svg>
              </div>
              <h3 className="text-2xl font-semibold mb-4 macos-title" style={{
                color: 'var(--text-primary)',
                fontWeight: '600',
                letterSpacing: '-0.01em'
              }}>Select a conversation</h3>
              <p className="text-lg macos-body" style={{
                color: 'var(--text-secondary)',
                fontWeight: '400',
                letterSpacing: '-0.005em'
              }}>Choose a conversation from the sidebar to start messaging.</p>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default MessagingSystem
