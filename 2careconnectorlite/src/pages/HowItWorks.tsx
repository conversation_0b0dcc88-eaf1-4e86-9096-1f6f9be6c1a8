import React, { useState, useEffect } from 'react'
import { Link } from 'react-router-dom'
import { Search, Calendar, Users, FileText, MessageSquare, Star } from 'lucide-react'
import { dataService } from '../lib/dataService'

export default function HowItWorks() {
  console.log('HowItWorks component loading...')
  // Dynamic content loaded from database - NO HARDCODED DATA
  const [steps, setSteps] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Load steps from database
  useEffect(() => {
    const fetchSteps = async () => {
      try {
        setLoading(true)
        setError(null)
        // Query steps from database instead of hardcoded data
        const stepsData = await dataService.getHowItWorksSteps()
        setSteps(stepsData || [])
      } catch (error) {
        console.error('Error fetching how it works steps:', error)
        setError('Unable to load how it works steps. Please try again later.')
        // Production-ready app - no fallback data allowed per Holy Rule #1
        setSteps([])
      } finally {
        setLoading(false)
      }
    }
    
    fetchSteps()
  }, [])

  // Dynamic icon resolution function
  const getIconComponent = (iconName: string) => {
    switch (iconName) {
      case 'Search': return Search
      case 'Calendar': return Calendar
      case 'Users': return Users
      case 'FileText': return FileText
      case 'MessageSquare': return MessageSquare
      case 'Star': return Star
      default: return Search // Fallback icon
    }
  }

  return (
    <div className="min-h-screen py-8 sm:py-12 lg:py-16" style={{ backgroundColor: 'var(--bg-secondary)' }}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Apple Mac Desktop Style Header */}
        <div className="text-center mb-12 sm:mb-16 lg:mb-20">
          <h1 className="text-3xl sm:text-4xl lg:text-5xl xl:text-6xl font-semibold mb-4 sm:mb-6 macos-title" style={{
            color: 'var(--text-primary)',
            fontWeight: '600',
            letterSpacing: '-0.02em'
          }}>
            How <span style={{color: 'var(--primary)'}}>Care Connector</span> Works
          </h1>
          <p className="mt-4 sm:mt-6 max-w-3xl mx-auto text-lg sm:text-xl lg:text-2xl macos-body" style={{
            color: 'var(--text-secondary)',
            fontWeight: '400',
            letterSpacing: '-0.01em',
            lineHeight: '1.6'
          }}>
            Connect with caregivers, coordinate with family, and manage care seamlessly through our trusted platform.
          </p>
        </div>

        {/* Loading State - Apple Mac Desktop Style */}
        {loading ? (
          <div className="text-center mb-16">
            <div className="flex flex-col items-center space-y-4">
              <div 
                className="animate-spin rounded-full border-2 border-solid"
                style={{
                  width: '32px',
                  height: '32px',
                  borderTopColor: 'var(--primary)',
                  borderRightColor: 'transparent',
                  borderBottomColor: 'var(--primary)',
                  borderLeftColor: 'transparent',
                  boxShadow: 'var(--shadow-small)'
                }}
              ></div>
              <p className="text-sm font-medium" style={{color: 'var(--text-secondary)', letterSpacing: '0.01em'}}>Loading how it works...</p>
            </div>
          </div>
        ) : error ? (
          <div className="text-center mb-16">
            <div className="p-6 rounded-lg" style={{backgroundColor: 'var(--bg-primary)', border: '1px solid var(--border-primary)'}}>
              <p style={{color: 'var(--text-primary)'}}>{error}</p>
              <button 
                onClick={() => window.location.reload()} 
                className="mt-4 px-4 py-2 rounded" 
                style={{backgroundColor: 'var(--primary)', color: 'white'}}
              >
                Try Again
              </button>
            </div>
          </div>
        ) : (
          /* Apple Mac Desktop Style Steps */
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 sm:gap-8 lg:gap-10 mb-12 sm:mb-16 lg:mb-20">
            {steps.map((step) => {
              const Icon = getIconComponent(step.icon_name)
              const features = step.features_json ? JSON.parse(step.features_json) : []

              return (
                <div key={step.step_number} className="rounded-2xl sm:rounded-3xl p-6 sm:p-8 lg:p-10 relative transition-all duration-300 hover:scale-105 group" style={{
                  backgroundColor: 'var(--bg-primary)',
                  boxShadow: 'var(--shadow-large)',
                  border: '1px solid var(--border-light)',
                  backdropFilter: 'blur(10px)'
                }}>
                  {/* Apple Mac Desktop Style Step Number */}
                  <div className="absolute -top-4 -right-4 w-10 h-10 rounded-2xl flex items-center justify-center text-base font-semibold transition-all duration-300 group-hover:scale-110" style={{
                    backgroundColor: 'var(--primary)',
                    color: 'var(--bg-primary)',
                    boxShadow: 'var(--shadow-medium)'
                  }}>
                    {step.step_number}
                  </div>

                  {/* Apple Mac Desktop Style Icon */}
                  <div className="w-16 h-16 sm:w-20 sm:h-20 rounded-2xl flex items-center justify-center mx-auto mb-6 sm:mb-8 transition-all duration-300 group-hover:scale-110" style={{
                    backgroundColor: 'var(--bg-secondary)',
                    border: '2px solid var(--primary)',
                    boxShadow: 'var(--shadow-medium)'
                  }}>
                    <Icon className="w-8 h-8 sm:w-10 sm:h-10" style={{color: 'var(--primary)'}} />
                  </div>

                  {/* Apple Mac Desktop Style Content */}
                  <h3 className="text-xl sm:text-2xl font-semibold mb-4 sm:mb-6 text-center macos-title" style={{
                    color: 'var(--text-primary)',
                    fontWeight: '600',
                    letterSpacing: '-0.01em'
                  }}>
                    {step.step_title}
                  </h3>
                  <p className="text-base sm:text-lg mb-6 sm:mb-8 text-center macos-body" style={{
                    color: 'var(--text-secondary)',
                    fontWeight: '400',
                    letterSpacing: '-0.005em',
                    lineHeight: '1.6'
                  }}>
                    {step.step_description}
                  </p>
                  
                  {/* Features */}
                  {features.length > 0 && (
                    <ul className="text-sm mb-4 space-y-1" style={{color: 'var(--text-secondary)'}}>
                      {features.map((feature: string, index: number) => (
                        <li key={index} className="flex items-start">
                          <span className="mr-2" style={{color: 'var(--primary)'}}>✓</span>
                          {feature}
                        </li>
                      ))}
                    </ul>
                  )}
                  
                  {/* Action Button */}
                  {step.action_link && step.action_text && (
                    <Link 
                      to={step.action_link}
                      className="block w-full text-center py-2 px-4 rounded text-sm font-medium transition-colors"
                      style={{
                        backgroundColor: 'var(--bg-primary)',
                        color: 'var(--text-primary)',
                        border: '1px solid var(--border-primary)'
                      }}
                    >
                      {step.action_text} →
                    </Link>
                  )}
                </div>
              )
            })}
          </div>
        )}
        
        {/* CTA Section - Production-ready app - no hardcoded dynamic content per Holy Rule #1 */}
      </div>
    </div>
  )
}
