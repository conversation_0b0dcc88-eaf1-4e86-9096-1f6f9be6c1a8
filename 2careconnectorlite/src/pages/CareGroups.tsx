import React, { useState, useEffect } from 'react'
import { dataService } from '../lib/dataService'
import { Search, Plus, Heart, X, Users, MapPin, MessageSquare, Calendar } from 'lucide-react'
import { supabase } from '../lib/supabase'

interface CareGroup {
  id: string
  name: string
  description?: string
  category?: string
  location?: string
  privacy_setting?: string
  member_count?: number
  created_at?: string
  avatar_url?: string
}

export default function CareGroups() {
  const [careGroups, setCareGroups] = useState<CareGroup[]>([])
  const [loading, setLoading] = useState(true)
  const [showCreateModal, setShowCreateModal] = useState(false)
  const [creating, setCreating] = useState(false)
  const [createError, setCreateError] = useState('')
  const [searchQuery, setSearchQuery] = useState('')
  const [validationErrors, setValidationErrors] = useState<{[key: string]: string}>({})
  const [activeTab, setActiveTab] = useState<'my-groups' | 'browse'>('my-groups')
  const [currentUserId, setCurrentUserId] = useState<string | null>(null)
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    category: '',
    location: '',
    privacy: 'public'
  })

  useEffect(() => {
    async function initializeData() {
      try {
        // Get current user
        const { data: { session } } = await supabase.auth.getSession()
        if (session?.user) {
          setCurrentUserId(session.user.id)
          // Load user's joined groups by default
          const userGroups = await dataService.getUserCareGroups(session.user.id)
          setCareGroups(userGroups)
        } else {
          // If not authenticated, show browse tab
          setActiveTab('browse')
          const publicGroups = await dataService.getCareGroups()
          setCareGroups(publicGroups)
        }
      } catch (err) {
        console.error('Failed to initialize care groups data:', err)
      } finally {
        setLoading(false)
      }
    }
    initializeData()
  }, [])

  // Handle tab switching
  const handleTabChange = async (tab: 'my-groups' | 'browse') => {
    setActiveTab(tab)
    setLoading(true)
    try {
      if (tab === 'my-groups' && currentUserId) {
        const userGroups = await dataService.getUserCareGroups(currentUserId)
        setCareGroups(userGroups)
      } else {
        const publicGroups = await dataService.getCareGroups()
        setCareGroups(publicGroups)
      }
    } catch (err) {
      console.error('Failed to load care groups:', err)
    } finally {
      setLoading(false)
    }
  }

  const validateForm = () => {
    const errors: {[key: string]: string} = {}

    if (!formData.name.trim()) {
      errors.name = 'Group name is required'
    } else if (formData.name.trim().length < 3) {
      errors.name = 'Group name must be at least 3 characters'
    }

    if (!formData.description.trim()) {
      errors.description = 'Description is required'
    } else if (formData.description.trim().length < 10) {
      errors.description = 'Description must be at least 10 characters'
    }

    if (!formData.category.trim()) {
      errors.category = 'Category is required'
    }

    setValidationErrors(errors)
    return Object.keys(errors).length === 0
  }

  const handleCreateGroup = async (e: React.FormEvent) => {
    e.preventDefault()
    setCreating(true)
    setCreateError('')
    setValidationErrors({})

    if (!validateForm()) {
      setCreating(false)
      return
    }

    try {
      // Get current user
      const { data: { session } } = await supabase.auth.getSession()
      if (!session) {
        setCreateError('You must be signed in to create a care group')
        return
      }

      // Create care group in database
      const { data, error } = await supabase
        .from('care_groups')
        .insert({
          name: formData.name,
          description: formData.description,
          category: formData.category,
          location: formData.location,
          privacy: formData.privacy,
          created_by: session.user.id,
          member_count: 1
        })
        .select()
        .single()

      if (error) throw error

      // Add creator as first member
      await supabase
        .from('care_group_members')
        .insert({
          group_id: data.id,
          user_id: session.user.id,
          role: 'admin',
          joined_at: new Date().toISOString()
        })

      // Switch to My Groups tab and refresh user's groups since they just joined one
      setActiveTab('my-groups')
      if (currentUserId) {
        const userGroups = await dataService.getUserCareGroups(currentUserId)
        setCareGroups(userGroups)
      }

      // Close modal and reset form
      setShowCreateModal(false)
      setFormData({
        name: '',
        description: '',
        category: '',
        location: '',
        privacy: 'public'
      })

      console.log('Care group created successfully:', data)
    } catch (error: any) {
      console.error('Error creating care group:', error)
      setCreateError(error.message || 'Failed to create care group')
    } finally {
      setCreating(false)
    }
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center" style={{ backgroundColor: 'var(--bg-secondary)' }}>
        <div className="text-center">
          <div className="inline-flex items-center gap-5 px-12 py-8 rounded-3xl" style={{
            backgroundColor: 'var(--bg-primary)',
            border: '1px solid var(--border-light)',
            boxShadow: 'var(--shadow-large)',
            backdropFilter: 'blur(10px)'
          }}>
            <div className="w-10 h-10 rounded-full animate-spin" style={{
              border: '3px solid var(--border-light)',
              borderTop: '3px solid var(--primary)'
            }}></div>
            <div>
              <div className="text-2xl font-semibold mb-2 macos-title" style={{
                color: 'var(--text-primary)',
                fontWeight: '600',
                letterSpacing: '-0.01em'
              }}>Loading Care Groups</div>
              <div className="text-lg macos-body" style={{
                color: 'var(--text-secondary)',
                fontWeight: '400',
                letterSpacing: '-0.005em'
              }}>Discovering communities near you...</div>
            </div>
          </div>
        </div>
      </div>
    )
  }



  return (
    <div className="min-h-screen" style={{ backgroundColor: 'var(--bg-secondary)' }}>
      {/* ARIA Live Region for Screen Reader Announcements */}
      <div aria-live="polite" aria-atomic="true" style={{
        position: 'absolute',
        left: '-10000px',
        width: '1px',
        height: '1px',
        overflow: 'hidden'
      }}>
        {creating && "Creating care group..."}
        {createError && `Error: ${createError}`}
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 md:px-8 py-8 sm:py-12">
        {/* Header Section - Mobile Optimized */}
        <div className="text-center mb-8 sm:mb-12">
          <h1 className="text-3xl sm:text-4xl md:text-5xl font-semibold mb-4 sm:mb-6 macos-title" style={{
            color: 'var(--text-primary)',
            fontWeight: '600',
            letterSpacing: '-0.02em',
            lineHeight: '1.1'
          }}>
            Care Groups
          </h1>

          <p className="text-lg sm:text-xl mb-8 sm:mb-10 px-4 macos-body" style={{
            color: 'var(--text-secondary)',
            fontWeight: '400',
            letterSpacing: '-0.005em',
            lineHeight: '1.5',
            maxWidth: '900px',
            margin: '0 auto'
          }}>
            {activeTab === 'my-groups' 
              ? 'Connect with others who share your health journey and experiences.' 
              : 'Discover and join care groups in your community.'}
          </p>
        </div>

        {/* Tab Navigation */}
        <div className="max-w-md mx-auto mb-8 sm:mb-12">
          <div className="flex rounded-xl p-1" style={{
            backgroundColor: 'var(--bg-primary)',
            border: '1px solid var(--border-light)',
            boxShadow: 'var(--shadow-small)'
          }}>
            <button
              onClick={() => handleTabChange('my-groups')}
              className="flex-1 px-4 py-2.5 rounded-lg font-medium transition-all duration-200 macos-body"
              style={{
                backgroundColor: activeTab === 'my-groups' ? 'var(--primary)' : 'transparent',
                color: activeTab === 'my-groups' ? 'var(--bg-primary)' : 'var(--text-secondary)',
                fontSize: '0.875rem',
                fontWeight: '500',
                letterSpacing: '-0.005em'
              }}
            >
              My Groups
            </button>
            <button
              onClick={() => handleTabChange('browse')}
              className="flex-1 px-4 py-2.5 rounded-lg font-medium transition-all duration-200 macos-body"
              style={{
                backgroundColor: activeTab === 'browse' ? 'var(--primary)' : 'transparent',
                color: activeTab === 'browse' ? 'var(--bg-primary)' : 'var(--text-secondary)',
                fontSize: '0.875rem',
                fontWeight: '500',
                letterSpacing: '-0.005em'
              }}
            >
              Browse Groups
            </button>
          </div>
        </div>

        {/* Create Group Button - Mobile Optimized */}
        <div className="text-center mb-8 sm:mb-12">
          <button
            onClick={() => setShowCreateModal(true)}
            className="inline-flex items-center gap-2 sm:gap-3 px-6 sm:px-10 py-3 sm:py-5 rounded-xl sm:rounded-2xl font-medium transition-all duration-300 hover:scale-105 macos-body"
            style={{
              backgroundColor: 'var(--primary)',
              color: 'var(--bg-primary)',
              fontSize: '1rem',
              fontWeight: '600',
              letterSpacing: '-0.005em',
              boxShadow: 'var(--shadow-large)'
            }}
          >
            <Plus className="w-4 sm:w-5 h-4 sm:h-5" />
            <span className="text-sm sm:text-base">Create Your Care Group</span>
          </button>
        </div>

        {/* Search Bar - Mobile Optimized */}
        <div className="max-w-2xl mx-auto mb-12 sm:mb-16">
          <form onSubmit={(e) => e.preventDefault()} className="relative" role="search" aria-label="Search care groups">
            <div className="relative">
              <Search className="absolute left-4 sm:left-6 top-1/2 transform -translate-y-1/2 w-5 sm:w-6 h-5 sm:h-6" style={{ color: 'var(--text-secondary)' }} />
              <input
                type="text"
                placeholder="Search care groups..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-12 sm:pl-16 pr-4 sm:pr-6 py-3 sm:py-5 rounded-xl sm:rounded-2xl border transition-all duration-300 focus:scale-105 macos-body text-sm sm:text-base"
                style={{
                  border: '1px solid var(--border-light)',
                  backgroundColor: 'var(--bg-primary)',
                  color: 'var(--text-primary)',
                  fontWeight: '400',
                  letterSpacing: '-0.005em',
                  boxShadow: 'var(--shadow-card)',
                  backdropFilter: 'blur(10px)'
                }}
                onFocus={(e) => {
                  e.currentTarget.style.borderColor = 'var(--primary)'
                  e.currentTarget.style.boxShadow = 'var(--shadow-large)'
                }}
                onBlur={(e) => {
                  e.currentTarget.style.borderColor = 'var(--border-light)'
                  e.currentTarget.style.boxShadow = 'var(--shadow-card)'
                }}
                aria-label="Search care groups by name, description, or category"
              />
            </div>
          </form>
        </div>

      {/* Filter care groups based on search query */}
      {(() => {
        const filteredGroups = careGroups.filter(group =>
          group.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          (group.description && group.description.toLowerCase().includes(searchQuery.toLowerCase())) ||
          (group.category && group.category.toLowerCase().includes(searchQuery.toLowerCase()))
        )

        return filteredGroups.length === 0 ? (
          <div className="text-center py-20">
            <div className="max-w-md mx-auto rounded-3xl p-12" style={{
              backgroundColor: 'var(--bg-primary)',
              border: '1px solid var(--border-light)',
              boxShadow: 'var(--shadow-large)',
              backdropFilter: 'blur(10px)'
            }}>
              <div className="w-20 h-20 rounded-3xl mx-auto mb-8 flex items-center justify-center" style={{
                backgroundColor: 'var(--primary)',
                boxShadow: 'var(--shadow-large)'
              }}>
                <Heart className="w-10 h-10" style={{ color: 'var(--bg-primary)' }} />
              </div>

              <h3 className="text-3xl font-semibold mb-4 macos-title" style={{
                color: 'var(--text-primary)',
                fontWeight: '600',
                letterSpacing: '-0.01em'
              }}>
                {activeTab === 'my-groups' ? 'No Care Groups Joined' : 'No care groups available'}
              </h3>

              <p className="text-lg mb-10 macos-body" style={{
                color: 'var(--text-secondary)',
                fontWeight: '400',
                letterSpacing: '-0.005em',
                lineHeight: '1.5'
              }}>
                {activeTab === 'my-groups' 
                  ? 'You haven\'t joined any care groups yet. Browse available groups or create your own.' 
                  : 'Be the first to create a care group in your community.'}
              </p>

              <button
                onClick={() => setShowCreateModal(true)}
                className="inline-flex items-center gap-3 px-8 py-4 rounded-2xl font-medium transition-all duration-300 hover:scale-105 macos-body"
                style={{
                  backgroundColor: 'var(--primary)',
                  color: 'var(--bg-primary)',
                  fontSize: '1.1rem',
                  fontWeight: '600',
                  letterSpacing: '-0.005em',
                  boxShadow: 'var(--shadow-large)'
                }}
                aria-label="Create your first care group"
              >
                <Plus className="w-5 h-5" />
                Create First Care Group
              </button>
            </div>
          </div>
      ) : (
        <>
          <h2 className="text-2xl sm:text-3xl md:text-4xl font-semibold text-center mb-8 sm:mb-12 macos-title" style={{
            color: 'var(--text-primary)',
            fontWeight: '600',
            letterSpacing: '-0.01em'
          }}>
            Available Care Groups
          </h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 lg:gap-8 max-w-7xl mx-auto">
          {filteredGroups.map((group: CareGroup) => (
            <div key={group.id} className="flex flex-col rounded-2xl sm:rounded-3xl p-4 sm:p-6 md:p-8 transition-all duration-300 hover:scale-105" style={{
              backgroundColor: 'var(--bg-primary)',
              border: '1px solid var(--border-light)',
              boxShadow: 'var(--shadow-large)',
              backdropFilter: 'blur(10px)',
              minHeight: '320px'
            }}>
              {/* Group Avatar */}
              <div className="w-20 h-20 rounded-3xl mx-auto mb-6 flex items-center justify-center" style={{
                backgroundColor: 'var(--primary)',
                boxShadow: 'var(--shadow-large)'
              }}>
                <Users className="w-10 h-10" style={{ color: 'var(--bg-primary)' }} />
              </div>

              <h3 className="text-2xl font-semibold mb-4 macos-title" style={{
                color: 'var(--text-primary)',
                fontWeight: '600',
                letterSpacing: '-0.01em',
                textAlign: 'center'
              }}>
                {group.name}
              </h3>

              {group.description && (
                <p className="text-lg mb-6 macos-body flex-grow" style={{
                  color: 'var(--text-secondary)',
                  fontWeight: '400',
                  letterSpacing: '-0.005em',
                  lineHeight: '1.5',
                  textAlign: 'center'
                }}>
                  {group.description}
                </p>
              )}

              {/* Group Info */}
              <div className="flex items-center justify-center gap-6 mb-8">
                {group.location && (
                  <div className="flex items-center gap-2">
                    <MapPin className="w-4 h-4" style={{ color: 'var(--text-secondary)' }} />
                    <span className="text-sm macos-body" style={{
                      color: 'var(--text-secondary)',
                      fontWeight: '400',
                      letterSpacing: '-0.005em'
                    }}>{group.location}</span>
                  </div>
                )}
                {group.member_count && (
                  <div className="flex items-center gap-2">
                    <Users className="w-4 h-4" style={{ color: 'var(--text-secondary)' }} />
                    <span className="text-sm macos-body" style={{
                      color: 'var(--text-secondary)',
                      fontWeight: '400',
                      letterSpacing: '-0.005em'
                    }}>{group.member_count} members</span>
                  </div>
                )}
              </div>

              {/* Collaboration Action Buttons */}
              <div className="flex gap-3 mb-6">
                <button
                  onClick={() => window.location.href = `/care-groups/${group.id}/chat`}
                  className="flex-1 flex items-center justify-center gap-2 px-4 py-3 rounded-xl border transition-all duration-300 hover:scale-105 macos-body"
                  style={{
                    backgroundColor: 'var(--bg-secondary)',
                    color: 'var(--primary)',
                    border: '1px solid var(--primary)',
                    fontSize: '0.95rem',
                    fontWeight: '500',
                    letterSpacing: '-0.005em',
                    boxShadow: 'var(--shadow-card)'
                  }}
                  title="Join group discussions"
                >
                  <MessageSquare className="w-4 h-4" />
                  Chat
                </button>
                <button
                  onClick={() => window.location.href = `/care-groups/${group.id}/calendar`}
                  className="flex-1 flex items-center justify-center gap-2 px-4 py-3 rounded-xl border transition-all duration-300 hover:scale-105 macos-body"
                  style={{
                    backgroundColor: 'var(--bg-secondary)',
                    color: 'var(--primary)',
                    border: '1px solid var(--primary)',
                    fontSize: '0.95rem',
                    fontWeight: '500',
                    letterSpacing: '-0.005em',
                    boxShadow: 'var(--shadow-card)'
                  }}
                  title="View group calendar"
                >
                  <Calendar className="w-4 h-4" />
                  Calendar
                </button>
              </div>

              <button
                onClick={() => alert(`Joining ${group.name}...`)}
                className="w-full flex items-center justify-center gap-3 px-6 py-4 rounded-2xl font-medium transition-all duration-300 hover:scale-105 macos-body"
                style={{
                  backgroundColor: 'var(--primary)',
                  color: 'var(--bg-primary)',
                  fontSize: '1.1rem',
                  fontWeight: '600',
                  letterSpacing: '-0.005em',
                  boxShadow: 'var(--shadow-large)'
                }}
                aria-label={`Join ${group.name} care group`}>
                <Users className="w-5 h-5" />
                Join Group
              </button>
            </div>
          ))}
          </div>
        </>
      )
      })()}

      {/* Create Care Group Modal */}
      {showCreateModal && (
        <div
          style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: 'var(--overlay-dark)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            zIndex: 1000,
            padding: '1rem'
          }}
          role="dialog"
          aria-modal="true"
          aria-labelledby="modal-title"
        >
          <div style={{
            backgroundColor: 'var(--bg-primary)',
            borderRadius: '0.75rem',
            padding: '2rem',
            width: '100%',
            maxWidth: '500px',
            maxHeight: '90vh',
            overflowY: 'auto',
            position: 'relative',
            boxShadow: 'var(--shadow-modal)',
            border: '1px solid var(--border-light)'
          }}>
            {/* Modal Header */}
            <div style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              marginBottom: '1.5rem'
            }}>
              <h2 id="modal-title" style={{
                fontSize: '1.5rem',
                fontWeight: '600',
                color: 'var(--text-primary)',
                margin: 0
              }}>
                Create Care Group
              </h2>
              <button
                onClick={() => {
                  setShowCreateModal(false)
                  setCreateError('')
                  setFormData({
                    name: '',
                    description: '',
                    category: '',
                    location: '',
                    privacy: 'public'
                  })
                }}
                style={{
                  background: 'none',
                  border: 'none',
                  padding: '0.5rem',
                  cursor: 'pointer',
                  borderRadius: '0.25rem',
                  color: 'var(--text-secondary)'
                }}
              >
                <X style={{ width: '1.25rem', height: '1.25rem' }} />
              </button>
            </div>

            {/* Error Display */}
            {createError && (
              <div style={{
                backgroundColor: 'var(--bg-error)',
                border: '1px solid var(--border-error)',
                color: 'var(--text-error)',
                padding: '0.75rem',
                borderRadius: '0.375rem',
                marginBottom: '1.5rem',
                fontSize: '0.875rem'
              }}>
                {createError}
              </div>
            )}

            {/* Form */}
            <form onSubmit={handleCreateGroup}>
              {/* Group Name */}
              <div style={{ marginBottom: '1.5rem' }}>
                <label style={{
                  display: 'block',
                  fontSize: '0.875rem',
                  fontWeight: '500',
                  color: 'var(--text-primary)',
                  marginBottom: '0.5rem'
                }}>
                  Group Name *
                </label>
                <input
                  type="text"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  required
                  placeholder="Enter group name"
                  aria-describedby={validationErrors.name ? "name-error" : undefined}
                  aria-invalid={!!validationErrors.name}
                  style={{
                    width: '100%',
                    padding: '0.75rem',
                    borderRadius: '0.375rem',
                    border: `1px solid ${validationErrors.name ? 'var(--error)' : 'var(--border-medium)'}`,
                    fontSize: '1rem',
                    backgroundColor: 'var(--bg-primary)'
                  }}
                />
                {validationErrors.name && (
                  <div id="name-error" role="alert" style={{
                    color: 'var(--error)',
                    fontSize: '0.875rem',
                    marginTop: '0.25rem'
                  }}>
                    {validationErrors.name}
                  </div>
                )}
              </div>

              {/* Description */}
              <div style={{ marginBottom: '1.5rem' }}>
                <label style={{
                  display: 'block',
                  fontSize: '0.875rem',
                  fontWeight: '500',
                  color: 'var(--text-primary)',
                  marginBottom: '0.5rem'
                }}>
                  Description
                </label>
                <textarea
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  rows={3}
                  placeholder="Describe your care group's purpose and goals"
                  aria-describedby={validationErrors.description ? "description-error" : undefined}
                  aria-invalid={!!validationErrors.description}
                  style={{
                    width: '100%',
                    padding: '0.75rem',
                    borderRadius: '0.375rem',
                    border: `1px solid ${validationErrors.description ? 'var(--border-error)' : 'var(--border-medium)'}`,
                    fontSize: '1rem',
                    backgroundColor: 'var(--bg-primary)',
                    resize: 'vertical'
                  }}
                />
                {validationErrors.description && (
                  <div id="description-error" role="alert" style={{
                    color: 'var(--text-error)',
                    fontSize: '0.875rem',
                    marginTop: '0.25rem'
                  }}>
                    {validationErrors.description}
                  </div>
                )}
              </div>

              {/* Category */}
              <div style={{ marginBottom: '1.5rem' }}>
                <label style={{
                  display: 'block',
                  fontSize: '0.875rem',
                  fontWeight: '600',
                  color: 'var(--text-primary)',
                  marginBottom: '0.5rem'
                }}>
                  Category
                </label>
                <select
                  name="category"
                  value={formData.category}
                  onChange={handleInputChange}
                  required
                  aria-describedby={validationErrors.category ? "category-error" : undefined}
                  aria-invalid={!!validationErrors.category}
                  style={{
                    width: '100%',
                    padding: '0.75rem',
                    borderRadius: '0.375rem',
                    border: `1px solid ${validationErrors.category ? 'var(--border-error)' : 'var(--border-medium)'}`,
                    fontSize: '1rem',
                    backgroundColor: 'var(--bg-primary)'
                  }}
                >
                  <option value="">Select a category</option>
                  <option value="alzheimers">Alzheimer's & Dementia</option>
                  <option value="cancer">Cancer Support</option>
                  <option value="chronic-illness">Chronic Illness</option>
                  <option value="mental-health">Mental Health</option>
                  <option value="senior-care">Senior Care</option>
                  <option value="disability">Disability Support</option>
                  <option value="family-caregivers">Family Caregivers</option>
                  <option value="other">Other</option>
                </select>
                {validationErrors.category && (
                  <div id="category-error" role="alert" style={{
                    color: 'var(--error)',
                    fontSize: '0.875rem',
                    marginTop: '0.25rem'
                  }}>
                    {validationErrors.category}
                  </div>
                )}
              </div>

              {/* Location */}
              <div style={{ marginBottom: '1.5rem' }}>
                <label style={{
                  display: 'block',
                  fontSize: '0.875rem',
                  fontWeight: '500',
                  color: 'var(--text-primary)',
                  marginBottom: '0.5rem'
                }}>
                  <MapPin style={{ width: '1rem', height: '1rem', display: 'inline', marginRight: '0.25rem' }} />
                  Location
                </label>
                <input
                  type="text"
                  name="location"
                  value={formData.location}
                  onChange={handleInputChange}
                  placeholder="City, State or Online"
                  style={{
                    width: '100%',
                    padding: '0.75rem',
                    borderRadius: '0.375rem',
                    border: '1px solid var(--border-medium)',
                    fontSize: '1rem',
                    backgroundColor: 'var(--bg-primary)'
                  }}
                />
              </div>

              {/* Privacy Setting */}
              <div style={{ marginBottom: '2rem' }}>
                <label style={{
                  display: 'block',
                  fontSize: '0.875rem',
                  fontWeight: '500',
                  color: 'var(--text-primary)',
                  marginBottom: '0.5rem'
                }}>
                  Privacy Setting
                </label>
                <select
                  name="privacy"
                  value={formData.privacy}
                  onChange={handleInputChange}
                  style={{
                    width: '100%',
                    padding: '0.75rem',
                    borderRadius: '0.375rem',
                    border: '1px solid var(--border-medium)',
                    fontSize: '1rem',
                    backgroundColor: 'var(--bg-primary)'
                  }}
                >
                  <option value="public">Public - Anyone can find and join</option>
                  <option value="private">Private - Invitation only</option>
                </select>
              </div>

              {/* Action Buttons */}
              <div style={{
                display: 'flex',
                gap: '1rem',
                justifyContent: 'flex-end'
              }}>
                <button
                  type="button"
                  onClick={() => {
                    setShowCreateModal(false)
                    setCreateError('')
                    setFormData({
                      name: '',
                      description: '',
                      category: '',
                      location: '',
                      privacy: 'public'
                    })
                  }}
                  style={{
                    padding: '0.75rem 1.5rem',
                    borderRadius: '0.375rem',
                    border: '1px solid var(--border-medium)',
                    backgroundColor: 'var(--bg-primary)',
                    color: 'var(--text-secondary)',
                    fontWeight: '500',
                    fontSize: '0.875rem',
                    cursor: 'pointer'
                  }}
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={creating || !formData.name.trim()}
                  style={{
                    padding: '0.75rem 1.5rem',
                    borderRadius: '0.375rem',
                    border: 'none',
                    backgroundColor: creating || !formData.name.trim() ? 'var(--text-muted)' : 'var(--primary)',
                    color: 'white',
                    fontWeight: '500',
                    fontSize: '0.875rem',
                    cursor: creating || !formData.name.trim() ? 'not-allowed' : 'pointer',
                    display: 'flex',
                    alignItems: 'center',
                    gap: '0.5rem'
                  }}
                >
                  {creating ? (
                    <>
                      <div style={{
                        width: '1rem',
                        height: '1rem',
                        border: '2px solid transparent',
                        borderTop: '2px solid white',
                        borderRadius: '50%',
                        animation: 'spin 1s linear infinite'
                      }} />
                      Creating...
                    </>
                  ) : (
                    <>
                      <Users style={{ width: '1rem', height: '1rem' }} />
                      Create Group
                    </>
                  )}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
      </div>
    </div>
  )
}
