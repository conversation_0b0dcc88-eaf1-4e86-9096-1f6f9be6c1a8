import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { supabase } from '../lib/supabase';
import { Loader2, Star, Send, ArrowLeft, CheckCircle2, AlertTriangle, MessageSquare, ThumbsUp, ThumbsDown } from 'lucide-react';
import { format, parseISO } from 'date-fns';

interface Booking {
  id: string;
  user_id: string;
  provider_id: string;
  provider_type: string;
  service_type: string;
  start_time: string;
  end_time: string;
  status: string;
  total_cost: number;
  special_requirements?: string;
  location?: string;
  provider_name?: string;
  created_at: string;
}

interface ReviewFormData {
  rating: number;
  review_text: string;
  would_recommend: boolean;
  service_quality: number;
  communication: number;
  professionalism: number;
  punctuality: number;
  value_for_money: number;
}

const SubmitBookingReviewPage: React.FC = () => {
  const navigate = useNavigate();
  const { bookingId } = useParams<{ bookingId: string }>();
  
  const [booking, setBooking] = useState<Booking | null>(null);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [existingReview, setExistingReview] = useState<any>(null);
  
  const [formData, setFormData] = useState<ReviewFormData>({
    rating: 0,
    review_text: '',
    would_recommend: true,
    service_quality: 0,
    communication: 0,
    professionalism: 0,
    punctuality: 0,
    value_for_money: 0
  });

  useEffect(() => {
    const fetchBookingAndReview = async () => {
      if (!bookingId) {
        setError("Booking ID is missing.");
        setLoading(false);
        return;
      }

      try {
        // Get current user
        const { data: { user } } = await supabase.auth.getUser();
        if (!user) {
          setError("Please sign in to submit a review.");
          setLoading(false);
          return;
        }

        // Fetch booking details
        const { data: bookingData, error: bookingError } = await supabase
          .schema('care_connector')
          .from('service_provider_bookings')
          .select('*')
          .eq('id', bookingId)
          .eq('user_id', user.id) // Ensure user owns this booking
          .single();

        if (bookingError) {
          console.error('Booking fetch error:', bookingError);
          throw bookingError;
        }

        if (!bookingData) {
          throw new Error("Booking not found or you don't have permission to review it.");
        }

        // Check if booking is completed
        if (bookingData.status !== 'completed') {
          throw new Error("You can only review completed bookings.");
        }

        setBooking(bookingData);

        // Check if review already exists
        const { data: reviewData, error: reviewError } = await supabase
          .schema('care_connector')
          .from('service_provider_reviews')
          .select('*')
          .eq('booking_id', bookingId)
          .eq('user_id', user.id)
          .single();

        if (reviewError && reviewError.code !== 'PGRST116') { // PGRST116 = not found
          console.warn('Review fetch error (non-critical):', reviewError);
        } else if (reviewData) {
          setExistingReview(reviewData);
          // Pre-populate form with existing review
          setFormData({
            rating: reviewData.rating || 0,
            review_text: reviewData.review_text || '',
            would_recommend: reviewData.would_recommend ?? true,
            service_quality: reviewData.service_quality || 0,
            communication: reviewData.communication || 0,
            professionalism: reviewData.professionalism || 0,
            punctuality: reviewData.punctuality || 0,
            value_for_money: reviewData.value_for_money || 0
          });
        }

      } catch (err: any) {
        console.error("Error fetching booking and review:", err.message);
        setError(err.message || "Failed to load booking details.");
        setBooking(null);
      } finally {
        setLoading(false);
      }
    };

    fetchBookingAndReview();
  }, [bookingId]);

  const handleRatingChange = (field: keyof ReviewFormData, value: number) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleTextChange = (field: keyof ReviewFormData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleRecommendationChange = (value: boolean) => {
    setFormData(prev => ({
      ...prev,
      would_recommend: value
    }));
  };

  const validateForm = () => {
    if (formData.rating === 0) {
      return "Please provide an overall rating.";
    }
    
    if (formData.review_text.trim().length < 10) {
      return "Please write at least 10 characters in your review.";
    }
    
    if (formData.service_quality === 0 || formData.communication === 0 || 
        formData.professionalism === 0 || formData.punctuality === 0 || 
        formData.value_for_money === 0) {
      return "Please rate all aspects of the service.";
    }

    return null;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    const validationError = validateForm();
    if (validationError) {
      alert(validationError);
      return;
    }

    setSubmitting(true);
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        throw new Error('Please sign in to submit a review.');
      }

      const reviewData = {
        booking_id: bookingId,
        user_id: user.id,
        provider_id: booking!.provider_id,
        rating: formData.rating,
        review_text: formData.review_text.trim(),
        would_recommend: formData.would_recommend,
        service_quality: formData.service_quality,
        communication: formData.communication,
        professionalism: formData.professionalism,
        punctuality: formData.punctuality,
        value_for_money: formData.value_for_money,
        service_type: booking!.service_type,
        provider_type: booking!.provider_type
      };

      let result;
      if (existingReview) {
        // Update existing review
        result = await supabase
          .schema('care_connector')
          .from('service_provider_reviews')
          .update(reviewData)
          .eq('id', existingReview.id)
          .select()
          .single();
      } else {
        // Create new review
        result = await supabase
          .schema('care_connector')
          .from('service_provider_reviews')
          .insert([reviewData])
          .select()
          .single();
      }

      if (result.error) {
        throw result.error;
      }

      console.log('Review submitted successfully:', result.data);
      
      // Navigate back to booking detail page with success message
      navigate(`/booking/${bookingId}`, { 
        state: { message: existingReview ? 'Review updated successfully!' : 'Review submitted successfully!' }
      });

    } catch (err: any) {
      console.error('Error submitting review:', err);
      alert(`Failed to submit review: ${err.message}`);
    } finally {
      setSubmitting(false);
    }
  };

  const renderStarRating = (field: keyof ReviewFormData, value: number, label: string) => {
    return (
      <div className="space-y-3">
        <label className="block text-lg font-medium macos-body" style={{
          color: 'var(--text-primary)',
          fontWeight: '500',
          letterSpacing: '-0.005em'
        }}>{label}</label>
        <div className="flex space-x-2">
          {[1, 2, 3, 4, 5].map((star) => (
            <button
              key={star}
              type="button"
              onClick={() => handleRatingChange(field, star)}
              className="p-2 rounded-2xl transition-all duration-300 hover:scale-110"
              style={{
                color: star <= value ? '#FFD700' : 'var(--text-muted)',
                backgroundColor: star <= value ? 'rgba(255, 215, 0, 0.1)' : 'transparent'
              }}
            >
              <Star className={`h-8 w-8 ${star <= value ? 'fill-current' : ''}`} />
            </button>
          ))}
        </div>
      </div>
    );
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center" style={{ backgroundColor: 'var(--bg-secondary)' }}>
        <div className="text-center">
          <div className="inline-flex items-center gap-5 px-12 py-8 rounded-3xl" style={{
            backgroundColor: 'var(--bg-primary)',
            border: '1px solid var(--border-light)',
            boxShadow: 'var(--shadow-large)',
            backdropFilter: 'blur(10px)'
          }}>
            <div className="w-10 h-10 rounded-full animate-spin" style={{
              border: '3px solid var(--border-light)',
              borderTop: '3px solid var(--primary)'
            }}></div>
            <div>
              <div className="text-2xl font-semibold mb-2 macos-title" style={{
                color: 'var(--text-primary)',
                fontWeight: '600',
                letterSpacing: '-0.01em'
              }}>Loading Review Form</div>
              <div className="text-lg macos-body" style={{
                color: 'var(--text-secondary)',
                fontWeight: '400',
                letterSpacing: '-0.005em'
              }}>Preparing your booking details...</div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center" style={{ backgroundColor: 'var(--bg-secondary)' }}>
        <div className="rounded-3xl p-12 max-w-md w-full mx-4" style={{
          backgroundColor: 'var(--bg-primary)',
          border: '1px solid var(--border-light)',
          boxShadow: 'var(--shadow-large)',
          backdropFilter: 'blur(10px)'
        }}>
          <div className="text-center">
            <div className="w-20 h-20 rounded-3xl mx-auto mb-6 flex items-center justify-center" style={{
              backgroundColor: 'var(--error)',
              boxShadow: 'var(--shadow-large)'
            }}>
              <AlertTriangle className="h-10 w-10" style={{ color: 'var(--bg-primary)' }} />
            </div>
            <h2 className="text-2xl font-semibold mb-4 macos-title" style={{
              color: 'var(--text-primary)',
              fontWeight: '600',
              letterSpacing: '-0.01em'
            }}>Cannot Submit Review</h2>
            <p className="mb-8 text-lg macos-body" style={{
              color: 'var(--text-secondary)',
              fontWeight: '400',
              letterSpacing: '-0.005em'
            }}>{error}</p>
            <button
              onClick={() => navigate(-1)}
              className="px-8 py-4 rounded-2xl font-medium transition-all duration-300 hover:scale-105 macos-body"
              style={{
                backgroundColor: 'var(--primary)',
                color: 'var(--bg-primary)',
                fontSize: '1.1rem',
                fontWeight: '500',
                letterSpacing: '-0.005em',
                boxShadow: 'var(--shadow-large)'
              }}
            >
              Go Back
            </button>
          </div>
        </div>
      </div>
    );
  }

  if (!booking) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="rounded-2xl shadow-sm p-8 max-w-md w-full mx-4" style={{ backgroundColor: 'var(--bg-primary)', borderColor: 'var(--border-light)' }}>
          <div className="text-center">
            <MessageSquare className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h2 className="text-xl font-semibold text-gray-900 mb-2">Booking Not Found</h2>
            <p className="text-gray-600 mb-6">The booking you are trying to review could not be found.</p>
            <button
              onClick={() => navigate('/my-bookings')}
              className="px-6 py-3 rounded-xl font-medium transition-colors"
              style={{ backgroundColor: 'var(--primary)', color: 'var(--bg-primary)' }}
              onMouseEnter={(e) => e.currentTarget.style.backgroundColor = 'var(--primary)'}
              onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'var(--primary)'}
            >
              View My Bookings
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen" style={{ backgroundColor: 'var(--bg-secondary)' }}>
      {/* Header */}
      <div style={{
        backgroundColor: 'var(--bg-primary)',
        borderBottomColor: 'var(--border-light)',
        boxShadow: 'var(--shadow-card)',
        backdropFilter: 'blur(10px)'
      }} className="border-b">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex items-center">
            <button
              onClick={() => navigate(`/booking/${booking.id}`)}
              className="flex items-center mr-6 font-medium transition-all duration-300 hover:scale-105 macos-body"
              style={{
                color: 'var(--text-secondary)',
                fontWeight: '500',
                letterSpacing: '-0.005em'
              }}
              onMouseEnter={(e) => e.currentTarget.style.color = 'var(--primary)'}
              onMouseLeave={(e) => e.currentTarget.style.color = 'var(--text-secondary)'}
            >
              <ArrowLeft className="mr-3 h-5 w-5" />
              Back to Booking
            </button>
            <div>
              <h1 className="text-4xl font-semibold mb-2 macos-title" style={{
                color: 'var(--text-primary)',
                fontWeight: '600',
                letterSpacing: '-0.01em'
              }}>
                {existingReview ? 'Edit Review' : 'Submit Review'}
              </h1>
              <p className="text-xl macos-body" style={{
                color: 'var(--text-secondary)',
                fontWeight: '400',
                letterSpacing: '-0.005em'
              }}>Share your experience with {booking.provider_name}</p>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">

          {/* Main Form */}
          <div className="lg:col-span-2">
            <form onSubmit={handleSubmit} className="space-y-8">

              {/* Booking Information */}
              <div className="rounded-3xl p-8" style={{
                backgroundColor: 'var(--bg-primary)',
                border: '1px solid var(--border-light)',
                boxShadow: 'var(--shadow-large)',
                backdropFilter: 'blur(10px)'
              }}>
                <h2 className="text-2xl font-semibold mb-6 macos-title" style={{
                  color: 'var(--text-primary)',
                  fontWeight: '600',
                  letterSpacing: '-0.01em'
                }}>Booking Details</h2>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-base font-medium mb-2 macos-body" style={{
                      color: 'var(--text-secondary)',
                      fontWeight: '500',
                      letterSpacing: '-0.005em'
                    }}>Service</label>
                    <p className="text-lg font-medium macos-body" style={{
                      color: 'var(--text-primary)',
                      fontWeight: '500',
                      letterSpacing: '-0.005em'
                    }}>{booking.service_type}</p>
                  </div>

                  <div>
                    <label className="block text-base font-medium mb-2 macos-body" style={{
                      color: 'var(--text-secondary)',
                      fontWeight: '500',
                      letterSpacing: '-0.005em'
                    }}>Provider</label>
                    <p className="text-lg font-medium macos-body" style={{
                      color: 'var(--text-primary)',
                      fontWeight: '500',
                      letterSpacing: '-0.005em'
                    }}>{booking.provider_name}</p>
                  </div>

                  <div>
                    <label className="block text-base font-medium mb-2 macos-body" style={{
                      color: 'var(--text-secondary)',
                      fontWeight: '500',
                      letterSpacing: '-0.005em'
                    }}>Date</label>
                    <p className="text-lg font-medium macos-body" style={{
                      color: 'var(--text-primary)',
                      fontWeight: '500',
                      letterSpacing: '-0.005em'
                    }}>{format(parseISO(booking.start_time), 'MMMM d, yyyy')}</p>
                  </div>

                  <div>
                    <label className="block text-base font-medium mb-2 macos-body" style={{
                      color: 'var(--text-secondary)',
                      fontWeight: '500',
                      letterSpacing: '-0.005em'
                    }}>Time</label>
                    <p className="text-lg font-medium macos-body" style={{
                      color: 'var(--text-primary)',
                      fontWeight: '500',
                      letterSpacing: '-0.005em'
                    }}>
                      {format(parseISO(booking.start_time), 'h:mm a')} - {format(parseISO(booking.end_time), 'h:mm a')}
                    </p>
                  </div>

                  <div>
                    <label className="block text-base font-medium mb-2 macos-body" style={{
                      color: 'var(--text-secondary)',
                      fontWeight: '500',
                      letterSpacing: '-0.005em'
                    }}>Total Cost</label>
                    <p className="text-lg font-medium macos-body" style={{
                      color: 'var(--text-primary)',
                      fontWeight: '500',
                      letterSpacing: '-0.005em'
                    }}>${booking.total_cost}</p>
                  </div>

                  <div>
                    <label className="block text-base font-medium mb-2 macos-body" style={{
                      color: 'var(--text-secondary)',
                      fontWeight: '500',
                      letterSpacing: '-0.005em'
                    }}>Status</label>
                    <p className="text-lg font-medium capitalize macos-body" style={{
                      color: 'var(--text-primary)',
                      fontWeight: '500',
                      letterSpacing: '-0.005em'
                    }}>{booking.status}</p>
                  </div>
                </div>
              </div>

              {/* Overall Rating */}
              <div className="rounded-3xl p-8" style={{
                backgroundColor: 'var(--bg-primary)',
                border: '1px solid var(--border-light)',
                boxShadow: 'var(--shadow-large)',
                backdropFilter: 'blur(10px)'
              }}>
                <h2 className="text-2xl font-semibold mb-6 macos-title" style={{
                  color: 'var(--text-primary)',
                  fontWeight: '600',
                  letterSpacing: '-0.01em'
                }}>Overall Rating</h2>

                <div className="space-y-6">
                  <div className="flex flex-col space-y-4">
                    <span className="text-lg font-medium macos-body" style={{
                      color: 'var(--text-primary)',
                      fontWeight: '500',
                      letterSpacing: '-0.005em'
                    }}>How would you rate this service overall?</span>
                    <div className="flex space-x-2">
                      {[1, 2, 3, 4, 5].map((star) => (
                        <button
                          key={star}
                          type="button"
                          onClick={() => handleRatingChange('rating', star)}
                          className="p-2 rounded-2xl transition-all duration-300 hover:scale-110"
                          style={{
                            color: star <= formData.rating ? '#FFD700' : 'var(--text-muted)',
                            backgroundColor: star <= formData.rating ? 'rgba(255, 215, 0, 0.1)' : 'transparent'
                          }}
                        >
                          <Star className={`h-10 w-10 ${star <= formData.rating ? 'fill-current' : ''}`} />
                        </button>
                      ))}
                    </div>
                  </div>

                  {formData.rating > 0 && (
                    <div className="p-4 rounded-2xl" style={{
                      backgroundColor: 'var(--bg-secondary)',
                      border: '1px solid var(--border-light)'
                    }}>
                      <p className="text-lg macos-body" style={{
                        color: 'var(--text-secondary)',
                        fontWeight: '400',
                        letterSpacing: '-0.005em'
                      }}>
                        {formData.rating === 1 && "Poor - Very unsatisfied"}
                        {formData.rating === 2 && "Fair - Somewhat unsatisfied"}
                        {formData.rating === 3 && "Good - Satisfied"}
                        {formData.rating === 4 && "Very Good - Very satisfied"}
                        {formData.rating === 5 && "Excellent - Extremely satisfied"}
                      </p>
                    </div>
                  )}
                </div>
              </div>

              {/* Detailed Ratings */}
              <div className="rounded-3xl p-8" style={{
                backgroundColor: 'var(--bg-primary)',
                border: '1px solid var(--border-light)',
                boxShadow: 'var(--shadow-large)',
                backdropFilter: 'blur(10px)'
              }}>
                <h2 className="text-2xl font-semibold mb-6 macos-title" style={{
                  color: 'var(--text-primary)',
                  fontWeight: '600',
                  letterSpacing: '-0.01em'
                }}>Rate Different Aspects</h2>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                  {renderStarRating('service_quality', formData.service_quality, 'Service Quality')}
                  {renderStarRating('communication', formData.communication, 'Communication')}
                  {renderStarRating('professionalism', formData.professionalism, 'Professionalism')}
                  {renderStarRating('punctuality', formData.punctuality, 'Punctuality')}
                  {renderStarRating('value_for_money', formData.value_for_money, 'Value for Money')}
                </div>
              </div>

              {/* Written Review */}
              <div className="rounded-3xl p-8" style={{
                backgroundColor: 'var(--bg-primary)',
                border: '1px solid var(--border-light)',
                boxShadow: 'var(--shadow-large)',
                backdropFilter: 'blur(10px)'
              }}>
                <h2 className="text-2xl font-semibold mb-6 macos-title" style={{
                  color: 'var(--text-primary)',
                  fontWeight: '600',
                  letterSpacing: '-0.01em'
                }}>Written Review</h2>

                <textarea
                  placeholder="Share your experience... What went well? What could be improved? Would you recommend this provider to others?"
                  value={formData.review_text}
                  onChange={(e) => handleTextChange('review_text', e.target.value)}
                  rows={8}
                  className="w-full px-6 py-4 rounded-2xl focus:outline-none transition-all duration-300 resize-none macos-body"
                  style={{
                    border: '1px solid var(--border-light)',
                    backgroundColor: 'var(--bg-secondary)',
                    color: 'var(--text-primary)',
                    fontSize: '1.1rem',
                    fontWeight: '400',
                    letterSpacing: '-0.005em',
                    lineHeight: '1.6'
                  }}
                  onFocus={(e) => {
                    e.currentTarget.style.borderColor = 'var(--primary)'
                    e.currentTarget.style.boxShadow = 'var(--shadow-large)'
                    e.currentTarget.style.transform = 'scale(1.01)'
                  }}
                  onBlur={(e) => {
                    e.currentTarget.style.borderColor = 'var(--border-light)'
                    e.currentTarget.style.boxShadow = 'var(--shadow-card)'
                    e.currentTarget.style.transform = 'scale(1)'
                  }}
                  required
                />

                <div className="mt-4 flex justify-between text-lg macos-body" style={{ color: 'var(--text-secondary)' }}>
                  <span style={{ fontWeight: '400', letterSpacing: '-0.005em' }}>Minimum 10 characters</span>
                  <span style={{ fontWeight: '500', letterSpacing: '-0.005em' }}>{formData.review_text.length} characters</span>
                </div>
              </div>

              {/* Recommendation */}
              <div className="rounded-3xl p-8" style={{
                backgroundColor: 'var(--bg-primary)',
                border: '1px solid var(--border-light)',
                boxShadow: 'var(--shadow-large)',
                backdropFilter: 'blur(10px)'
              }}>
                <h2 className="text-2xl font-semibold mb-6 macos-title" style={{
                  color: 'var(--text-primary)',
                  fontWeight: '600',
                  letterSpacing: '-0.01em'
                }}>Recommendation</h2>

                <div className="space-y-6">
                  <p className="text-lg macos-body" style={{
                    color: 'var(--text-primary)',
                    fontWeight: '400',
                    letterSpacing: '-0.005em'
                  }}>Would you recommend this provider to others?</p>

                  <div className="flex space-x-6">
                    <button
                      type="button"
                      onClick={() => handleRecommendationChange(true)}
                      className="flex items-center px-8 py-4 rounded-2xl border-2 transition-all duration-300 hover:scale-105 macos-body"
                      style={{
                        borderColor: formData.would_recommend ? 'var(--primary)' : 'var(--border-light)',
                        backgroundColor: formData.would_recommend ? 'var(--primary)' : 'var(--bg-secondary)',
                        color: formData.would_recommend ? 'var(--bg-primary)' : 'var(--text-primary)',
                        fontSize: '1.1rem',
                        fontWeight: '500',
                        letterSpacing: '-0.005em',
                        boxShadow: formData.would_recommend ? 'var(--shadow-large)' : 'var(--shadow-card)'
                      }}
                    >
                      <ThumbsUp className="mr-3 h-5 w-5" />
                      Yes, I recommend
                    </button>

                    <button
                      type="button"
                      onClick={() => handleRecommendationChange(false)}
                      className="flex items-center px-8 py-4 rounded-2xl border-2 transition-all duration-300 hover:scale-105 macos-body"
                      style={{
                        borderColor: !formData.would_recommend ? 'var(--error)' : 'var(--border-light)',
                        backgroundColor: !formData.would_recommend ? 'var(--error)' : 'var(--bg-secondary)',
                        color: !formData.would_recommend ? 'var(--bg-primary)' : 'var(--text-primary)',
                        fontSize: '1.1rem',
                        fontWeight: '500',
                        letterSpacing: '-0.005em',
                        boxShadow: !formData.would_recommend ? 'var(--shadow-large)' : 'var(--shadow-card)'
                      }}
                    >
                      <ThumbsDown className="mr-3 h-5 w-5" />
                      No, I don't recommend
                    </button>
                  </div>
                </div>
              </div>
            </form>
          </div>

          {/* Review Summary Sidebar */}
          <div className="space-y-8">
            <div className="rounded-3xl p-8 sticky top-6" style={{
              backgroundColor: 'var(--bg-primary)',
              border: '1px solid var(--border-light)',
              boxShadow: 'var(--shadow-large)',
              backdropFilter: 'blur(10px)'
            }}>
              <h3 className="text-2xl font-semibold mb-6 macos-title" style={{
                color: 'var(--text-primary)',
                fontWeight: '600',
                letterSpacing: '-0.01em'
              }}>
                {existingReview ? 'Update Review' : 'Review Summary'}
              </h3>

              <div className="space-y-6 mb-8">
                <div className="flex items-center justify-between">
                  <span className="text-lg macos-body" style={{
                    color: 'var(--text-secondary)',
                    fontWeight: '400',
                    letterSpacing: '-0.005em'
                  }}>Overall Rating:</span>
                  <div className="flex items-center space-x-2">
                    {formData.rating > 0 ? (
                      <>
                        <Star className="h-5 w-5" style={{ color: '#FFD700', fill: '#FFD700' }} />
                        <span className="font-medium text-lg macos-body" style={{
                          color: 'var(--text-primary)',
                          fontWeight: '500',
                          letterSpacing: '-0.005em'
                        }}>{formData.rating}/5</span>
                      </>
                    ) : (
                      <span className="text-lg macos-body" style={{
                        color: 'var(--text-muted)',
                        fontWeight: '400',
                        letterSpacing: '-0.005em'
                      }}>Not rated</span>
                    )}
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-lg macos-body" style={{
                    color: 'var(--text-secondary)',
                    fontWeight: '400',
                    letterSpacing: '-0.005em'
                  }}>Review Length:</span>
                  <span className="font-medium text-lg macos-body" style={{
                    color: 'var(--text-primary)',
                    fontWeight: '500',
                    letterSpacing: '-0.005em'
                  }}>{formData.review_text.length} characters</span>
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-lg macos-body" style={{
                    color: 'var(--text-secondary)',
                    fontWeight: '400',
                    letterSpacing: '-0.005em'
                  }}>Recommendation:</span>
                  <span className="font-medium text-lg macos-body" style={{
                    color: formData.would_recommend ? 'var(--primary)' : 'var(--error)',
                    fontWeight: '500',
                    letterSpacing: '-0.005em'
                  }}>
                    {formData.would_recommend ? 'Yes' : 'No'}
                  </span>
                </div>

                <div className="pt-6" style={{ borderTop: '1px solid var(--border-light)' }}>
                  <div className="space-y-4">
                    <div className="flex justify-between">
                      <span className="text-lg macos-body" style={{
                        color: 'var(--text-secondary)',
                        fontWeight: '400',
                        letterSpacing: '-0.005em'
                      }}>Service Quality:</span>
                      <span className="font-medium text-lg macos-body" style={{
                        color: 'var(--text-primary)',
                        fontWeight: '500',
                        letterSpacing: '-0.005em'
                      }}>{formData.service_quality}/5</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-lg macos-body" style={{
                        color: 'var(--text-secondary)',
                        fontWeight: '400',
                        letterSpacing: '-0.005em'
                      }}>Communication:</span>
                      <span className="font-medium text-lg macos-body" style={{
                        color: 'var(--text-primary)',
                        fontWeight: '500',
                        letterSpacing: '-0.005em'
                      }}>{formData.communication}/5</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-lg macos-body" style={{
                        color: 'var(--text-secondary)',
                        fontWeight: '400',
                        letterSpacing: '-0.005em'
                      }}>Professionalism:</span>
                      <span className="font-medium text-lg macos-body" style={{
                        color: 'var(--text-primary)',
                        fontWeight: '500',
                        letterSpacing: '-0.005em'
                      }}>{formData.professionalism}/5</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-lg macos-body" style={{
                        color: 'var(--text-secondary)',
                        fontWeight: '400',
                        letterSpacing: '-0.005em'
                      }}>Punctuality:</span>
                      <span className="font-medium text-lg macos-body" style={{
                        color: 'var(--text-primary)',
                        fontWeight: '500',
                        letterSpacing: '-0.005em'
                      }}>{formData.punctuality}/5</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-lg macos-body" style={{
                        color: 'var(--text-secondary)',
                        fontWeight: '400',
                        letterSpacing: '-0.005em'
                      }}>Value:</span>
                      <span className="font-medium text-lg macos-body" style={{
                        color: 'var(--text-primary)',
                        fontWeight: '500',
                        letterSpacing: '-0.005em'
                      }}>{formData.value_for_money}/5</span>
                    </div>
                  </div>
                </div>
              </div>

              <button
                type="submit"
                onClick={handleSubmit}
                disabled={submitting || formData.rating === 0 || formData.review_text.trim().length < 10}
                className="w-full flex items-center justify-center px-8 py-5 rounded-2xl font-medium transition-all duration-300 hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100 macos-body"
                style={{
                  backgroundColor: 'var(--primary)',
                  color: 'var(--bg-primary)',
                  fontSize: '1.2rem',
                  fontWeight: '600',
                  letterSpacing: '-0.005em',
                  boxShadow: 'var(--shadow-large)'
                }}
              >
                {submitting ? (
                  <>
                    <Loader2 className="mr-3 h-5 w-5 animate-spin" />
                    {existingReview ? 'Updating...' : 'Submitting...'}
                  </>
                ) : (
                  <>
                    <Send className="mr-3 h-5 w-5" />
                    {existingReview ? 'Update Review' : 'Submit Review'}
                  </>
                )}
              </button>
              
              <button
                type="button"
                onClick={() => navigate(`/booking/${booking.id}`)}
                className="w-full mt-3 px-6 py-3 border border-gray-300 text-gray-700 rounded-xl font-medium hover:bg-gray-50 transition-colors"
              >
                Cancel
              </button>
              
              <div className="mt-4 p-3 bg-blue-50 rounded-xl">
                <p className="text-xs text-blue-800">
                  <CheckCircle2 className="inline h-3 w-3 mr-1" />
                  Your review helps other families find quality care providers and helps providers improve their services.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SubmitBookingReviewPage;
