import React from 'react'
import { Browser<PERSON>outer as Router, Routes, Route } from 'react-router-dom'
import Header from './components/Header'

// Page Imports
import Home from './pages/Home'
import Caregivers from './pages/Caregivers'
import Companions from './pages/Companions'
import Professionals from './pages/Professionals'
import CareCheckers from './pages/CareCheckers'
import CareGroups from './pages/CareGroups'
import ProvideCare from './pages/ProvideCare'
import JoinGroup from './pages/JoinGroup'
import Dashboard from './pages/Dashboard'
import Auth from './pages/Auth'
import SignIn from './pages/SignIn'
import AuthTest from './pages/AuthTest'
import GetStarted from './pages/GetStarted'
import HowItWorks from './pages/HowItWorks'
import Features from './pages/Features'
import Products from './pages/Products'
import ProviderProfile from './pages/ProviderProfile'
import BookingConfirmationPage from './pages/BookingConfirmationPage'
import CreateBookingPage from './pages/CreateBookingPage'
import MyBookingsPage from './pages/MyBookingsPage'
import BookingSearchPage from './pages/BookingSearchPage'
import BookingDetailPage from './pages/BookingDetailPage'
import BookingNotificationsPage from './pages/BookingNotificationsPage'
import BookingTransactionPage from './pages/BookingTransactionPage'
import BookingRemindersPage from './pages/BookingRemindersPage'
import CareGroupSettingsPage from './pages/CareGroupSettingsPage'
import ProfileEdit from './pages/ProfileEdit'
import MessagingSystem from './pages/MessagingSystem'
import AIAssistant from './pages/AIAssistant'
import Connections from './pages/Connections'
import Settings from './pages/Settings'
import About from './pages/About'
import Contact from './pages/Contact'
import Privacy from './pages/Privacy'
import SafetyLocation from './pages/SafetyLocation'
import MedicationManagement from './pages/MedicationManagement'

import './index.css'

function App() {
  return (
    <Router>
      <div style={{ minHeight: '100vh', backgroundColor: 'var(--bg-primary)' }}>
        <Header />
        <main>
          <Routes>
            {/* Home and Main Pages */}
            <Route path="/" element={<Home />} />
            <Route path="/home" element={<Home />} />
            <Route path="/dashboard" element={<Dashboard />} />
            
            {/* Find Care Routes */}
            <Route path="/find-care" element={<Caregivers />} />
            <Route path="/caregivers" element={<Caregivers />} />
            <Route path="/caregiver/:id" element={<ProviderProfile />} />
            <Route path="/companions" element={<Companions />} />
            <Route path="/professionals" element={<Professionals />} />
            <Route path="/care-checkers" element={<CareCheckers />} />

            {/* Provide Care Routes */}
            <Route path="/provide-care" element={<ProvideCare />} />
            
            {/* Care Groups Routes */}
            <Route path="/care-groups" element={<CareGroups />} />
            <Route path="/browse-groups" element={<CareGroups />} />
            <Route path="/create-group" element={<CareGroups />} />
            <Route path="/join-group" element={<JoinGroup />} />
            <Route path="/care-group-settings" element={<CareGroupSettingsPage />} />
            
            {/* Authentication Routes */}
            <Route path="/sign-in" element={<SignIn />} />
            <Route path="/sign-up" element={<Auth />} />
            <Route path="/auth" element={<Auth />} />
            
            {/* Information Pages */}
            <Route path="/get-started" element={<GetStarted />} />
            <Route path="/how-it-works" element={<HowItWorks />} />
            <Route path="/features" element={<Features />} />
            <Route path="/products" element={<Products />} />
            
            {/* Provider and Booking Routes */}
            <Route path="/provider/:providerType/:providerId" element={<ProviderProfile />} />
            <Route path="/booking/create/:providerType/:providerId" element={<CreateBookingPage />} />
            <Route path="/booking-search" element={<BookingSearchPage />} />
            <Route path="/booking/confirmation/:bookingId" element={<BookingConfirmationPage />} />
            <Route path="/booking/notifications" element={<BookingNotificationsPage />} />
            <Route path="/booking-transaction" element={<BookingTransactionPage />} />
            <Route path="/booking-reminders" element={<BookingRemindersPage />} />
            <Route path="/my-bookings" element={<MyBookingsPage />} />
            <Route path="/booking/:id" element={<BookingDetailPage />} />
            
            {/* User Account Routes */}
            <Route path="/profile" element={<ProfileEdit />} />
            <Route path="/profile/edit" element={<ProfileEdit />} />
            
            {/* Communication Routes */}
            <Route path="/messages" element={<MessagingSystem />} />
            <Route path="/connections" element={<Connections />} />
            <Route path="/settings" element={<Settings />} />
            <Route path="/ai-assistant" element={<AIAssistant />} />
            
            {/* Static Pages */}
            <Route path="/about" element={<About />} />
            <Route path="/contact" element={<Contact />} />
            <Route path="/privacy" element={<Privacy />} />
            <Route path="/safety-location" element={<SafetyLocation />} />
            <Route path="/medication-management" element={<MedicationManagement />} />
          </Routes>
        </main>
      </div>
    </Router>
  )
}

export default App
