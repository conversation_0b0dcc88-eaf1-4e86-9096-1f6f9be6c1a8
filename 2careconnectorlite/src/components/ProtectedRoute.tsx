import React, { useEffect, useState } from 'react'
import { Navigate, useLocation } from 'react-router-dom'
import { Loader2 } from 'lucide-react'
import { authService } from '../services/authService'

interface ProtectedRouteProps {
  children: React.ReactNode
  requireAdmin?: boolean
}

export default function ProtectedRoute({ children, requireAdmin = false }: ProtectedRouteProps) {
  const [isAuthenticated, setIsAuthenticated] = useState<boolean | null>(null)
  const [isAdmin, setIsAdmin] = useState<boolean>(false)
  const [loading, setLoading] = useState(true)
  const location = useLocation()

  useEffect(() => {
    const checkAuth = async () => {
      try {
        console.log('🔐 ProtectedRoute: Checking authentication...')
        
        const user = await authService.getCurrentUser()
        
        if (user) {
          console.log('✅ ProtectedRoute: User authenticated:', user.email)
          setIsAuthenticated(true)
          
          // Check admin status if required
          if (requireAdmin) {
            // For now, check if user has admin role
            // In a real app, you'd check against a proper admin role system
            const isUserAdmin = user.role === 'admin' || user.email === '<EMAIL>'
            setIsAdmin(isUserAdmin)
            console.log('🔐 ProtectedRoute: Admin check:', isUserAdmin)
          }
        } else {
          console.log('❌ ProtectedRoute: User not authenticated')
          setIsAuthenticated(false)
        }
      } catch (error) {
        console.error('❌ ProtectedRoute: Authentication check failed:', error)
        setIsAuthenticated(false)
      } finally {
        setLoading(false)
      }
    }

    checkAuth()
  }, [requireAdmin])

  // Show loading spinner while checking authentication
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center" style={{
        background: 'linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%)'
      }}>
        <div className="text-center">
          <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4" style={{ color: 'var(--primary)' }} />
          <p className="text-lg macos-body" style={{
            color: 'var(--text-secondary)',
            fontWeight: '400',
            letterSpacing: '-0.005em'
          }}>
            Verifying authentication...
          </p>
        </div>
      </div>
    )
  }

  // Redirect to sign-in if not authenticated
  if (!isAuthenticated) {
    console.log('🔐 ProtectedRoute: Redirecting to sign-in, current path:', location.pathname)
    return <Navigate to="/sign-in" state={{ from: location.pathname }} replace />
  }

  // Redirect to dashboard if admin access required but user is not admin
  if (requireAdmin && !isAdmin) {
    console.log('🔐 ProtectedRoute: Admin access required but user is not admin')
    return <Navigate to="/dashboard" replace />
  }

  // User is authenticated (and admin if required), render the protected content
  console.log('✅ ProtectedRoute: Access granted')
  return <>{children}</>
}
