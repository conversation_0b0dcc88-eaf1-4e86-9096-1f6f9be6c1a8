import React, { useState, useEffect } from 'react'
import { Alert<PERSON>riangle, Clock, X } from 'lucide-react'

interface SessionTimeoutWarningProps {
  isVisible: boolean
  timeRemaining: number
  onExtend: () => void
  onDismiss: () => void
}

export default function SessionTimeoutWarning({ 
  isVisible, 
  timeRemaining, 
  onExtend, 
  onDismiss 
}: SessionTimeoutWarningProps) {
  const [countdown, setCountdown] = useState(timeRemaining)

  useEffect(() => {
    setCountdown(timeRemaining)
  }, [timeRemaining])

  useEffect(() => {
    if (!isVisible) return

    const interval = setInterval(() => {
      setCountdown(prev => {
        if (prev <= 1000) {
          clearInterval(interval)
          return 0
        }
        return prev - 1000
      })
    }, 1000)

    return () => clearInterval(interval)
  }, [isVisible])

  const formatTime = (ms: number) => {
    const minutes = Math.floor(ms / 60000)
    const seconds = Math.floor((ms % 60000) / 1000)
    return `${minutes}:${seconds.toString().padStart(2, '0')}`
  }

  if (!isVisible) return null

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4" style={{
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      backdropFilter: 'blur(4px)'
    }}>
      <div 
        className="relative max-w-md w-full rounded-2xl p-8 shadow-2xl border"
        style={{
          backgroundColor: 'var(--bg-primary)',
          borderColor: 'var(--border-medium)',
          boxShadow: 'var(--shadow-large)'
        }}
      >
        {/* Close button */}
        <button
          onClick={onDismiss}
          className="absolute top-4 right-4 p-2 rounded-lg transition-all duration-200"
          style={{
            color: 'var(--text-muted)',
            backgroundColor: 'transparent'
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.backgroundColor = 'var(--bg-secondary)'
            e.currentTarget.style.color = 'var(--text-primary)'
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.backgroundColor = 'transparent'
            e.currentTarget.style.color = 'var(--text-muted)'
          }}
          aria-label="Dismiss warning"
        >
          <X className="w-5 h-5" />
        </button>

        {/* Warning icon */}
        <div className="flex justify-center mb-6">
          <div 
            className="p-4 rounded-full"
            style={{
              backgroundColor: 'var(--bg-warning)',
              color: 'var(--text-warning)'
            }}
          >
            <AlertTriangle className="w-8 h-8" />
          </div>
        </div>

        {/* Title */}
        <h2 className="text-2xl font-semibold text-center mb-4 macos-title" style={{
          color: 'var(--text-primary)',
          fontWeight: '600',
          letterSpacing: '-0.01em'
        }}>
          Session Expiring Soon
        </h2>

        {/* Message */}
        <p className="text-center mb-6 macos-body" style={{
          color: 'var(--text-secondary)',
          fontWeight: '400',
          letterSpacing: '-0.005em',
          lineHeight: '1.5'
        }}>
          Your session will expire for security reasons. Click "Stay Signed In" to continue your session.
        </p>

        {/* Countdown */}
        <div className="flex items-center justify-center mb-8 p-4 rounded-xl" style={{
          backgroundColor: 'var(--bg-secondary)',
          border: '1px solid var(--border-light)'
        }}>
          <Clock className="w-5 h-5 mr-3" style={{ color: 'var(--primary)' }} />
          <span className="text-xl font-mono font-semibold" style={{
            color: countdown < 60000 ? 'var(--text-error)' : 'var(--text-primary)'
          }}>
            {formatTime(countdown)}
          </span>
        </div>

        {/* Action buttons */}
        <div className="space-y-3">
          <button
            onClick={onExtend}
            className="w-full py-4 px-6 rounded-xl text-base font-medium text-white transition-all duration-200 macos-body"
            style={{
              backgroundColor: 'var(--primary)',
              boxShadow: 'var(--shadow-medium)'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.opacity = '0.9'
              e.currentTarget.style.transform = 'translateY(-1px)'
              e.currentTarget.style.boxShadow = 'var(--shadow-large)'
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.opacity = '1'
              e.currentTarget.style.transform = 'translateY(0)'
              e.currentTarget.style.boxShadow = 'var(--shadow-medium)'
            }}
          >
            Stay Signed In
          </button>
          
          <button
            onClick={onDismiss}
            className="w-full py-4 px-6 rounded-xl text-base font-medium transition-all duration-200 macos-body border"
            style={{
              color: 'var(--text-secondary)',
              backgroundColor: 'var(--bg-secondary)',
              borderColor: 'var(--border-light)'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = 'var(--bg-primary)'
              e.currentTarget.style.borderColor = 'var(--border-medium)'
              e.currentTarget.style.color = 'var(--text-primary)'
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = 'var(--bg-secondary)'
              e.currentTarget.style.borderColor = 'var(--border-light)'
              e.currentTarget.style.color = 'var(--text-secondary)'
            }}
          >
            Sign Out Now
          </button>
        </div>

        {/* Security note */}
        <p className="text-center mt-6 text-sm macos-body" style={{
          color: 'var(--text-muted)',
          fontWeight: '400'
        }}>
          This is a security feature to protect your account
        </p>
      </div>
    </div>
  )
}
