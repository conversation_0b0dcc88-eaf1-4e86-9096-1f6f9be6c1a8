import React, { useState } from 'react'
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom'
import Header from './components/Header'
import ProtectedRoute from './components/ProtectedRoute'
// import SessionTimeoutWarning from './components/SessionTimeoutWarning'
// import { useSessionTimeout } from './hooks/useSessionTimeout'

// Page Imports
import Home from './pages/Home'
import Caregivers from './pages/Caregivers'
import Companions from './pages/Companions'
import Professionals from './pages/Professionals'
import CareCheckers from './pages/CareCheckers'
import CareGroups from './pages/CareGroups'
import ProvideCare from './pages/ProvideCare'
import JoinGroup from './pages/JoinGroup'
import Dashboard from './pages/Dashboard' // Temporarily disabled due to JSX syntax errors
import Auth from './pages/Auth'
import SignIn from './pages/SignIn'
import AuthTest from './pages/AuthTest'
import GetStarted from './pages/GetStarted'
import HowItWorks from './pages/HowItWorks'
import Features from './pages/Features'
import Products from './pages/Products'
import ProviderProfile from './pages/ProviderProfile'
import BookingConfirmationPage from './pages/BookingConfirmationPage'
import CreateBookingPage from './pages/CreateBookingPage'
import MyBookingsPage from './pages/MyBookingsPage'
import BookingSearchPage from './pages/BookingSearchPage'
import BookingDetailPage from './pages/BookingDetailPage'
import BookingNotificationsPage from './pages/BookingNotificationsPage'
import BookingTransactionPage from './pages/BookingTransactionPage'
import BookingRemindersPage from './pages/BookingRemindersPage'
import CareGroupSettingsPage from './pages/CareGroupSettingsPage'
import ProfileEdit from './pages/ProfileEdit'
import MessagingSystem from './pages/MessagingSystem'
import AIAssistant from './pages/AIAssistant'
import Connections from './pages/Connections'
import Settings from './pages/Settings'
import About from './pages/About'
import Contact from './pages/Contact'
import Privacy from './pages/Privacy'
import SafetyLocation from './pages/SafetyLocation'
import MedicationManagement from './pages/MedicationManagement'
import ForgotPassword from './pages/ForgotPassword'
import ResetPassword from './pages/ResetPassword'
import EmailVerified from './pages/EmailVerified'

import './index.css'

function App() {
  // Temporarily disable session timeout to fix infinite loop
  // const [showSessionWarning, setShowSessionWarning] = useState(false)
  // const [sessionTimeRemaining, setSessionTimeRemaining] = useState(0)

  // const { extendSession, getTimeRemaining } = useSessionTimeout({
  //   timeoutMinutes: 30,
  //   warningMinutes: 5,
  //   onWarning: () => {
  //     setSessionTimeRemaining(getTimeRemaining())
  //     setShowSessionWarning(true)
  //   },
  //   onTimeout: () => {
  //     setShowSessionWarning(false)
  //   }
  // })

  // const handleExtendSession = () => {
  //   extendSession()
  //   setShowSessionWarning(false)
  // }

  // const handleDismissWarning = () => {
  //   setShowSessionWarning(false)
  // }

  return (
    <Router>
      <div style={{ minHeight: '100vh', backgroundColor: 'var(--bg-primary)' }}>
        <Header />
        <main>
          <Routes>
            {/* Home and Main Pages */}
            <Route path="/" element={<Home />} />
            <Route path="/home" element={<Home />} />
            <Route path="/dashboard" element={
              <ProtectedRoute>
                <Dashboard />
              </ProtectedRoute>
            } />
            
            {/* Main Application Routes */}
            <Route path="/find-care" element={<Caregivers />} />
            <Route path="/providers" element={<Navigate to="/find-care" replace />} />
            <Route path="/caregivers" element={<Caregivers />} />
            <Route path="/provider/:providerType/:providerId" element={<ProviderProfile />} />
            <Route path="/companions" element={<Companions />} />
            <Route path="/professionals" element={<Professionals />} />
            <Route path="/care-checkers" element={<CareCheckers />} />
            
            {/* Provide Care Routes */}
            <Route path="/provide-care" element={<ProvideCare />} />
            
            {/* Care Groups Routes */}
            <Route path="/care-groups" element={<CareGroups />} />
            <Route path="/browse-groups" element={<CareGroups />} />
            <Route path="/create-group" element={<CareGroups />} />
            <Route path="/join-group" element={<JoinGroup />} />
            <Route path="/care-group-settings" element={
              <ProtectedRoute>
                <CareGroupSettingsPage />
              </ProtectedRoute>
            } />
            
            {/* Authentication Routes */}
            <Route path="/sign-in" element={<SignIn />} />
            <Route path="/sign-up" element={<Auth />} />
            <Route path="/auth" element={<Auth />} />
            <Route path="/forgot-password" element={<ForgotPassword />} />
            <Route path="/reset-password" element={<ResetPassword />} />
            <Route path="/email-verified" element={<EmailVerified />} />
            
            {/* Information Pages */}
            <Route path="/get-started" element={<GetStarted />} />
            <Route path="/how-it-works" element={<HowItWorks />} />
            <Route path="/features" element={<Features />} />
            <Route path="/products" element={<Products />} />
            
            {/* Provider and Booking Routes */}
            <Route path="/provider/:providerType/:providerId" element={<ProviderProfile />} />
            <Route path="/booking/create/:providerType/:providerId" element={
              <ProtectedRoute>
                <CreateBookingPage />
              </ProtectedRoute>
            } />
            <Route path="/booking-search" element={<BookingSearchPage />} />
            <Route path="/booking/confirmation/:bookingId" element={
              <ProtectedRoute>
                <BookingConfirmationPage />
              </ProtectedRoute>
            } />
            <Route path="/booking/notifications" element={
              <ProtectedRoute>
                <BookingNotificationsPage />
              </ProtectedRoute>
            } />
            <Route path="/booking-transaction" element={
              <ProtectedRoute>
                <BookingTransactionPage />
              </ProtectedRoute>
            } />
            <Route path="/booking-reminders" element={
              <ProtectedRoute>
                <BookingRemindersPage />
              </ProtectedRoute>
            } />
            <Route path="/my-bookings" element={
              <ProtectedRoute>
                <MyBookingsPage />
              </ProtectedRoute>
            } />
            <Route path="/booking/:id" element={
              <ProtectedRoute>
                <BookingDetailPage />
              </ProtectedRoute>
            } />
            
            {/* User Account Routes */}
            <Route path="/profile" element={
              <ProtectedRoute>
                <ProfileEdit />
              </ProtectedRoute>
            } />
            <Route path="/profile/edit" element={
              <ProtectedRoute>
                <ProfileEdit />
              </ProtectedRoute>
            } />
            
            {/* Communication Routes */}
            <Route path="/messages" element={
              <ProtectedRoute>
                <MessagingSystem />
              </ProtectedRoute>
            } />
            <Route path="/connections" element={
              <ProtectedRoute>
                <Connections />
              </ProtectedRoute>
            } />
            <Route path="/settings" element={
              <ProtectedRoute>
                <Settings />
              </ProtectedRoute>
            } />
            <Route path="/ai-assistant" element={
              <ProtectedRoute>
                <AIAssistant />
              </ProtectedRoute>
            } />
            
            {/* Static Pages */}
            <Route path="/about" element={<About />} />
            <Route path="/contact" element={<Contact />} />
            <Route path="/privacy" element={<Privacy />} />
            <Route path="/safety-location" element={
              <ProtectedRoute>
                <SafetyLocation />
              </ProtectedRoute>
            } />
            <Route path="/medication-management" element={
              <ProtectedRoute>
                <MedicationManagement />
              </ProtectedRoute>
            } />
          </Routes>
        </main>

        {/* Session Timeout Warning - Temporarily disabled to fix infinite loop */}
        {/* <SessionTimeoutWarning
          isVisible={showSessionWarning}
          timeRemaining={sessionTimeRemaining}
          onExtend={handleExtendSession}
          onDismiss={handleDismissWarning}
        /> */}
      </div>
    </Router>
  )
}

export default App
