{"mcpServers": {"playwright": {"command": "npx", "args": ["@playwright/mcp"], "env": {"NO_PROXY": "*", "HTTP_PROXY": "", "HTTPS_PROXY": ""}}, "puppeteer": {"command": "npx", "args": ["puppeteer-mcp-server"], "env": {"NO_PROXY": "*", "HTTP_PROXY": "", "HTTPS_PROXY": ""}}, "supabase": {"command": "npx", "args": ["@supabase/mcp-server-supabase"], "env": {"NO_PROXY": "*", "HTTP_PROXY": "", "HTTPS_PROXY": "", "SUPABASE_URL": "https://yekarqanirdkdckimpna.supabase.co", "SUPABASE_ANON_KEY": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inlla2FycWFuaXJka2Rja2ltcG5hIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzUzNTAwNzcsImV4cCI6MjA1MDkyNjA3N30.0Rx8jn-6X1YyebZ0EeFRz4h-4_HFZPG_c5VHWrVNL0M"}}}}