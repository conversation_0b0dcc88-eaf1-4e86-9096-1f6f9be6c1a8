{"root": ["./src/app.tsx", "./src/app_backup.tsx", "./src/app_test.tsx", "./src/main.tsx", "./src/components/header.tsx", "./src/components/protectedroute.tsx", "./src/components/sessiontimeoutwarning.tsx", "./src/hooks/usesessiontimeout.ts", "./src/lib/dataservice.ts", "./src/lib/supabase.ts", "./src/pages/aiassistant.tsx", "./src/pages/about.tsx", "./src/pages/adminanalytics.tsx", "./src/pages/admincontentmoderation.tsx", "./src/pages/admindashboardpage.tsx", "./src/pages/adminsettings.tsx", "./src/pages/auth.tsx", "./src/pages/authtest.tsx", "./src/pages/bookinganalyticspage.tsx", "./src/pages/bookingavailabilitypage.tsx", "./src/pages/bookingcancellationpage.tsx", "./src/pages/bookingconfirmationpage.tsx", "./src/pages/bookingdetailpage.tsx", "./src/pages/bookinghistorypage.tsx", "./src/pages/bookinginvoicepage.tsx", "./src/pages/bookingmodificationpage.tsx", "./src/pages/bookingnotificationspage.tsx", "./src/pages/bookingoverviewpage.tsx", "./src/pages/bookingpaymentpage.tsx", "./src/pages/bookingpreferencespage.tsx", "./src/pages/bookingrecurringpage.tsx", "./src/pages/bookingreminderspage.tsx", "./src/pages/bookingreportspage.tsx", "./src/pages/bookingsearchpage.tsx", "./src/pages/bookingstatuspage.tsx", "./src/pages/bookingtransactionpage.tsx", "./src/pages/carecheckers.tsx", "./src/pages/caregroupdetailpage.tsx", "./src/pages/caregroupeventspage.tsx", "./src/pages/caregroupmemberspage.tsx", "./src/pages/caregroupsettingspage.tsx", "./src/pages/caregroups.tsx", "./src/pages/caregivers.tsx", "./src/pages/companions.tsx", "./src/pages/connections.tsx", "./src/pages/contact.tsx", "./src/pages/createbookingpage.tsx", "./src/pages/dashboard.tsx", "./src/pages/emailverified.tsx", "./src/pages/features.tsx", "./src/pages/forgotpassword.tsx", "./src/pages/getstarted.tsx", "./src/pages/home.tsx", "./src/pages/howitworks.tsx", "./src/pages/joingroup.tsx", "./src/pages/medicationmanagement.tsx", "./src/pages/messagingsystem.tsx", "./src/pages/mybookingspage.tsx", "./src/pages/privacy.tsx", "./src/pages/products.tsx", "./src/pages/professionals.tsx", "./src/pages/profileedit.tsx", "./src/pages/providecare.tsx", "./src/pages/providerbookingcalendarpage.tsx", "./src/pages/providermanagement.tsx", "./src/pages/providerprofile.tsx", "./src/pages/reschedulebookingpage.tsx", "./src/pages/resetpassword.tsx", "./src/pages/safetylocation.tsx", "./src/pages/securemessaging.tsx", "./src/pages/settings.tsx", "./src/pages/sharedcalendars.tsx", "./src/pages/signin.tsx", "./src/pages/submitbookingreviewpage.tsx", "./src/pages/taskmanagement.tsx", "./src/pages/usercarecheckerbookings.tsx", "./src/pages/usercaregiverbookings.tsx", "./src/pages/usercompanionbookings.tsx", "./src/scripts/createsamplesafetydata.ts", "./src/scripts/populatedatabase.ts", "./src/scripts/setupsafetyfeatures.ts", "./src/services/authservice.ts", "./src/services/csrfservice.ts", "./src/services/passwordhistoryservice.ts", "./src/services/ratelimitservice.ts", "./src/services/securesessionservice.ts", "./src/services/securityauditservice.ts", "./src/services/twofactorauthservice.ts", "./src/utils/inputsanitizer.ts"], "errors": true, "version": "5.8.3"}